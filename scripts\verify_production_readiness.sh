#!/bin/bash

# Aetherforge Production Readiness Verification Script
# Linux/Unix version

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
DETAILED="${DETAILED:-false}"
SKIP_TESTS="${SKIP_TESTS:-false}"

# Scoring
OVERALL_SCORE=0
MAX_SCORE=0

# Logging functions
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}" >&2; }
log_warning() { echo -e "${YELLOW}⚠️ $1${NC}"; }
log_info() { echo -e "${BLUE}ℹ️ $1${NC}"; }

log_info "Starting Aetherforge Production Readiness Verification..."
log_info "Environment: $ENVIRONMENT"
log_info "Project Root: $PROJECT_ROOT"

# Test Docker availability
test_docker_availability() {
    log_info "Checking Docker availability..."
    ((MAX_SCORE += 10))
    
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version 2>/dev/null || echo "unknown")
        log_success "Docker is available: $docker_version"
        ((OVERALL_SCORE += 10))
        return 0
    else
        log_error "Docker is not available or not in PATH"
        return 1
    fi
}

# Test Docker Compose availability
test_docker_compose_availability() {
    log_info "Checking Docker Compose availability..."
    ((MAX_SCORE += 10))
    
    if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
        local compose_version=$(docker-compose --version 2>/dev/null || docker compose version 2>/dev/null || echo "unknown")
        log_success "Docker Compose is available: $compose_version"
        ((OVERALL_SCORE += 10))
        return 0
    else
        log_error "Docker Compose is not available or not in PATH"
        return 1
    fi
}

# Test configuration files
test_configuration_files() {
    log_info "Checking configuration files..."
    ((MAX_SCORE += 20))
    local config_score=0
    
    local required_files=(
        "docker-compose.yml"
        "docker-compose.prod.yml"
        "Dockerfile.orchestrator"
        ".env.example"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            log_success "Found: $file"
            ((config_score += 5))
        else
            log_warning "Missing: $file"
        fi
    done
    
    ((OVERALL_SCORE += config_score))
    [[ $config_score -eq 20 ]]
}

# Test deployment scripts
test_deployment_scripts() {
    log_info "Checking deployment scripts..."
    ((MAX_SCORE += 20))
    local script_score=0
    
    local required_scripts=(
        "scripts/deploy_production.sh"
        "scripts/config_manager.sh"
        "scripts/health_check.sh"
        "scripts/backup.sh"
    )
    
    for script in "${required_scripts[@]}"; do
        if [[ -f "$PROJECT_ROOT/$script" ]]; then
            log_success "Found: $script"
            ((script_score += 5))
        else
            log_warning "Missing: $script"
        fi
    done
    
    ((OVERALL_SCORE += script_score))
    [[ $script_score -eq 20 ]]
}

# Test source code structure
test_source_code_structure() {
    log_info "Checking source code structure..."
    ((MAX_SCORE += 15))
    local source_score=0
    
    local required_dirs=(
        "src"
        "tests"
        "docs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            log_success "Found directory: $dir"
            ((source_score += 5))
        else
            log_warning "Missing directory: $dir"
        fi
    done
    
    ((OVERALL_SCORE += source_score))
    [[ $source_score -eq 15 ]]
}

# Test Python dependencies
test_python_dependencies() {
    log_info "Checking Python dependencies..."
    ((MAX_SCORE += 10))
    
    if [[ -f "$PROJECT_ROOT/requirements.txt" ]]; then
        log_success "Found requirements.txt"
        
        if command -v python3 &> /dev/null || command -v python &> /dev/null; then
            local python_cmd=$(command -v python3 || command -v python)
            local python_version=$($python_cmd --version 2>/dev/null || echo "unknown")
            log_success "Python is available: $python_version"
            ((OVERALL_SCORE += 10))
            return 0
        else
            log_warning "Python is not available in PATH"
            ((OVERALL_SCORE += 5))
            return 1
        fi
    else
        log_error "requirements.txt not found"
        return 1
    fi
}

# Test environment variables
test_environment_variables() {
    log_info "Checking environment variables..."
    ((MAX_SCORE += 15))
    local env_score=0
    
    local required_env_vars=(
        "OPENAI_API_KEY"
        "POSTGRES_PASSWORD"
    )
    
    for env_var in "${required_env_vars[@]}"; do
        if [[ -n "${!env_var:-}" ]]; then
            log_success "Environment variable set: $env_var"
            ((env_score += 7))
        else
            log_warning "Environment variable not set: $env_var"
        fi
    done
    
    ((OVERALL_SCORE += env_score))
    [[ $env_score -eq 14 ]]
}

# Run integration tests
test_integration_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "Skipping integration tests (SKIP_TESTS=true)"
        return 0
    fi
    
    log_info "Running integration tests..."
    ((MAX_SCORE += 20))
    
    local test_dir="$PROJECT_ROOT/tests"
    if [[ -d "$test_dir" ]]; then
        local test_count=$(find "$test_dir" -name "*.py" -type f | wc -l)
        if [[ $test_count -gt 0 ]]; then
            log_success "Found $test_count test files"
            
            if [[ -f "$test_dir/test_complete_integration.py" ]]; then
                log_success "Integration test file found"
                ((OVERALL_SCORE += 20))
                return 0
            else
                log_warning "Main integration test file not found"
                ((OVERALL_SCORE += 10))
                return 1
            fi
        else
            log_error "No test files found in tests directory"
            return 1
        fi
    else
        log_error "Tests directory not found"
        return 1
    fi
}

# Test documentation
test_documentation() {
    log_info "Checking documentation..."
    ((MAX_SCORE += 10))
    local doc_score=0
    
    local required_docs=(
        "README.md"
        "docs/user-guides"
        "docs/technical"
    )
    
    for doc in "${required_docs[@]}"; do
        if [[ -e "$PROJECT_ROOT/$doc" ]]; then
            log_success "Found: $doc"
            ((doc_score += 3))
        else
            log_warning "Missing: $doc"
        fi
    done
    
    ((OVERALL_SCORE += doc_score))
    [[ $doc_score -gt 5 ]]
}

# Generate readiness report
generate_readiness_report() {
    local percentage=$(( (OVERALL_SCORE * 100) / MAX_SCORE ))
    
    echo ""
    echo "========================================"
    echo "Aetherforge Production Readiness Report"
    echo "========================================"
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date)"
    echo "Host: $(hostname)"
    echo ""
    
    if [[ $percentage -ge 90 ]]; then
        log_success "Overall Score: $OVERALL_SCORE / $MAX_SCORE ($percentage%)"
    elif [[ $percentage -ge 70 ]]; then
        log_warning "Overall Score: $OVERALL_SCORE / $MAX_SCORE ($percentage%)"
    else
        log_error "Overall Score: $OVERALL_SCORE / $MAX_SCORE ($percentage%)"
    fi
    
    echo ""
    echo "Component Breakdown:"
    test_docker_availability && echo "  ✅ Docker Availability" || echo "  ❌ Docker Availability"
    test_docker_compose_availability && echo "  ✅ Docker Compose" || echo "  ❌ Docker Compose"
    test_configuration_files && echo "  ✅ Configuration Files" || echo "  ❌ Configuration Files"
    test_deployment_scripts && echo "  ✅ Deployment Scripts" || echo "  ❌ Deployment Scripts"
    test_source_code_structure && echo "  ✅ Source Code Structure" || echo "  ❌ Source Code Structure"
    test_python_dependencies && echo "  ✅ Python Dependencies" || echo "  ❌ Python Dependencies"
    test_environment_variables && echo "  ✅ Environment Variables" || echo "  ❌ Environment Variables"
    test_integration_tests && echo "  ✅ Integration Tests" || echo "  ❌ Integration Tests"
    test_documentation && echo "  ✅ Documentation" || echo "  ❌ Documentation"
    
    echo ""
    if [[ $percentage -ge 90 ]]; then
        log_success "🎉 Aetherforge is READY for production deployment!"
    elif [[ $percentage -ge 70 ]]; then
        log_warning "⚠️ Aetherforge is MOSTLY ready - address warnings before production"
    else
        log_error "❌ Aetherforge is NOT ready for production - critical issues must be resolved"
    fi
    
    echo "========================================"
    return $percentage
}

# Show next steps
show_next_steps() {
    local readiness_percentage=$1
    
    echo ""
    echo "Next Steps:"
    
    if [[ $readiness_percentage -ge 90 ]]; then
        log_success "1. Review the production deployment checklist"
        log_success "2. Set up production environment variables"
        log_success "3. Configure SSL certificates"
        log_success "4. Run: ./scripts/deploy_production.sh"
        log_success "5. Monitor deployment with: ./scripts/health_check.sh"
    else
        log_warning "1. Address the missing components identified above"
        log_warning "2. Install missing dependencies (Docker, Python, etc.)"
        log_warning "3. Set up required environment variables"
        log_warning "4. Re-run this verification script"
        log_warning "5. Proceed with deployment once score is 90%+"
    fi
}

# Main execution
main() {
    # Reset scores for clean run
    OVERALL_SCORE=0
    MAX_SCORE=0
    
    # Run all tests
    test_docker_availability
    test_docker_compose_availability
    test_configuration_files
    test_deployment_scripts
    test_source_code_structure
    test_python_dependencies
    test_environment_variables
    test_integration_tests
    test_documentation
    
    # Generate report
    local readiness_percentage
    readiness_percentage=$(generate_readiness_report)
    
    # Show next steps
    show_next_steps $readiness_percentage
    
    # Exit with appropriate code
    if [[ $readiness_percentage -ge 90 ]]; then
        exit 0
    elif [[ $readiness_percentage -ge 70 ]]; then
        exit 1
    else
        exit 2
    fi
}

# Make script executable
chmod +x "$0"

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
