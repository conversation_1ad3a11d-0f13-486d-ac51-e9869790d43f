"""
Configuration for the Research Engine
Defines research parameters, source priorities, and crawling settings
"""

from dataclasses import dataclass
from typing import Dict, List, Any
from enum import Enum

class ResearchMode(Enum):
    """Research operation modes"""
    FAST = "fast"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    DEEP = "deep"

class CrawlStrategy(Enum):
    """Crawling strategies"""
    SINGLE_PAGE = "single_page"
    SMART_CRAWL = "smart_crawl"
    RECURSIVE = "recursive"
    SITEMAP_BASED = "sitemap_based"

@dataclass
class ResearchConfig:
    """Configuration for the Research Engine"""
    
    # MCP-Crawl4AI-RAG Configuration
    mcp_url: str = "http://localhost:8051"
    mcp_timeout: int = 120
    mcp_enabled: bool = True
    
    # OpenAI Configuration
    openai_api_key: str = None
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 4000
    
    # Research Configuration
    default_research_mode: str = "standard"
    max_concurrent_queries: int = 5
    max_results_per_query: int = 20
    enable_code_search: bool = True
    enable_fallback_research: bool = True
    
    # Caching Configuration
    enable_result_caching: bool = True
    cache_ttl_seconds: int = 3600  # 1 hour
    max_cache_size: int = 1000
    
    # Crawling Configuration
    default_crawl_strategy: str = "smart_crawl"
    max_pages_per_crawl: int = 100
    crawl_timeout_seconds: int = 300
    respect_robots_txt: bool = True
    
    # Source Priority Configuration
    enable_source_ranking: bool = True
    boost_official_docs: float = 2.0
    boost_framework_docs: float = 1.5
    boost_community_content: float = 1.2
    
    @staticmethod
    def get_research_mode_config(mode: ResearchMode) -> Dict[str, Any]:
        """Get configuration for specific research modes"""
        configs = {
            ResearchMode.FAST: {
                "max_queries": 3,
                "max_results_per_query": 5,
                "timeout_seconds": 30,
                "enable_code_search": False,
                "crawl_depth": 1
            },
            ResearchMode.STANDARD: {
                "max_queries": 5,
                "max_results_per_query": 10,
                "timeout_seconds": 60,
                "enable_code_search": True,
                "crawl_depth": 2
            },
            ResearchMode.COMPREHENSIVE: {
                "max_queries": 10,
                "max_results_per_query": 20,
                "timeout_seconds": 120,
                "enable_code_search": True,
                "crawl_depth": 3
            },
            ResearchMode.DEEP: {
                "max_queries": 15,
                "max_results_per_query": 30,
                "timeout_seconds": 300,
                "enable_code_search": True,
                "crawl_depth": 5
            }
        }
        return configs.get(mode, configs[ResearchMode.STANDARD])
    
    @staticmethod
    def get_source_priorities() -> Dict[str, int]:
        """Get source priority rankings"""
        return {
            # Official documentation (highest priority)
            "docs.python.org": 10,
            "developer.mozilla.org": 10,
            "docs.microsoft.com": 10,
            "docs.aws.amazon.com": 10,
            "cloud.google.com": 10,
            "docs.docker.com": 10,
            "kubernetes.io": 10,
            "nodejs.org": 10,
            
            # Framework documentation
            "reactjs.org": 9,
            "vuejs.org": 9,
            "angular.io": 9,
            "expressjs.com": 9,
            "flask.palletsprojects.com": 9,
            "django.readthedocs.io": 9,
            "fastapi.tiangolo.com": 9,
            "spring.io": 9,
            
            # Popular libraries and tools
            "github.com": 8,
            "npmjs.com": 8,
            "pypi.org": 8,
            "maven.apache.org": 8,
            
            # Community resources
            "stackoverflow.com": 7,
            "medium.com": 6,
            "dev.to": 6,
            "hashnode.com": 6,
            
            # Tutorial sites
            "freecodecamp.org": 7,
            "codecademy.com": 7,
            "udemy.com": 6,
            "coursera.org": 6,
            
            # Tech blogs and news
            "techcrunch.com": 5,
            "wired.com": 5,
            "arstechnica.com": 5,
            
            # Default priority
            "default": 5
        }
    
    @staticmethod
    def get_crawl_patterns() -> Dict[str, List[str]]:
        """Get URL patterns for different types of content"""
        return {
            "documentation": [
                r".*docs?\..*",
                r".*documentation.*",
                r".*guide.*",
                r".*tutorial.*",
                r".*reference.*"
            ],
            "api_reference": [
                r".*api.*",
                r".*reference.*",
                r".*spec.*",
                r".*swagger.*",
                r".*openapi.*"
            ],
            "code_examples": [
                r".*example.*",
                r".*sample.*",
                r".*demo.*",
                r".*github\.com.*",
                r".*gitlab\.com.*"
            ],
            "tutorials": [
                r".*tutorial.*",
                r".*guide.*",
                r".*how-to.*",
                r".*getting-started.*",
                r".*quickstart.*"
            ],
            "best_practices": [
                r".*best-practice.*",
                r".*pattern.*",
                r".*architecture.*",
                r".*design.*",
                r".*convention.*"
            ]
        }
    
    @staticmethod
    def get_technology_keywords() -> Dict[str, List[str]]:
        """Get keywords for different technologies"""
        return {
            "javascript": [
                "javascript", "js", "node", "nodejs", "npm", "yarn",
                "react", "vue", "angular", "express", "typescript"
            ],
            "python": [
                "python", "pip", "django", "flask", "fastapi", "pandas",
                "numpy", "scikit-learn", "tensorflow", "pytorch"
            ],
            "java": [
                "java", "spring", "maven", "gradle", "hibernate",
                "junit", "tomcat", "jvm", "kotlin"
            ],
            "csharp": [
                "c#", "csharp", "dotnet", ".net", "asp.net", "entity framework",
                "nuget", "visual studio", "azure"
            ],
            "go": [
                "golang", "go", "gin", "echo", "gorilla", "gorm"
            ],
            "rust": [
                "rust", "cargo", "actix", "tokio", "serde", "diesel"
            ],
            "php": [
                "php", "laravel", "symfony", "composer", "wordpress",
                "drupal", "codeigniter"
            ],
            "ruby": [
                "ruby", "rails", "gem", "bundler", "sinatra", "rspec"
            ],
            "databases": [
                "mysql", "postgresql", "mongodb", "redis", "elasticsearch",
                "sqlite", "oracle", "sql server", "cassandra"
            ],
            "cloud": [
                "aws", "azure", "gcp", "docker", "kubernetes", "terraform",
                "ansible", "jenkins", "gitlab ci", "github actions"
            ],
            "frontend": [
                "html", "css", "sass", "less", "webpack", "vite",
                "babel", "eslint", "prettier", "tailwind"
            ],
            "mobile": [
                "react native", "flutter", "ionic", "xamarin",
                "swift", "kotlin", "android", "ios"
            ]
        }
    
    @staticmethod
    def get_research_templates() -> Dict[str, List[str]]:
        """Get research query templates for different project types"""
        return {
            "web_application": [
                "modern web development best practices",
                "responsive web design patterns",
                "web application security guidelines",
                "frontend framework comparison",
                "backend API design principles",
                "web performance optimization",
                "accessibility standards implementation",
                "progressive web app development",
                "single page application architecture"
            ],
            "mobile_application": [
                "mobile app development best practices",
                "mobile UI/UX design patterns",
                "mobile app security guidelines",
                "cross-platform development frameworks",
                "mobile performance optimization",
                "app store optimization strategies",
                "mobile accessibility guidelines",
                "offline-first mobile development",
                "mobile app testing strategies"
            ],
            "api_service": [
                "REST API design best practices",
                "API security implementation",
                "API documentation standards",
                "microservices architecture patterns",
                "API versioning strategies",
                "API rate limiting techniques",
                "GraphQL vs REST comparison",
                "API gateway patterns",
                "service mesh architecture"
            ],
            "data_platform": [
                "data pipeline architecture patterns",
                "data warehouse design principles",
                "ETL best practices",
                "data security and privacy",
                "real-time data processing",
                "data visualization techniques",
                "machine learning pipeline design",
                "big data processing frameworks",
                "data governance strategies"
            ],
            "desktop_application": [
                "desktop application frameworks",
                "cross-platform desktop development",
                "desktop UI design patterns",
                "application packaging and distribution",
                "desktop security best practices",
                "performance optimization techniques",
                "desktop application testing",
                "native vs cross-platform development"
            ],
            "machine_learning": [
                "machine learning model deployment",
                "MLOps best practices",
                "model versioning and management",
                "feature engineering techniques",
                "model monitoring and observability",
                "automated machine learning",
                "deep learning frameworks comparison",
                "model interpretability techniques"
            ]
        }
    
    @staticmethod
    def get_fallback_sources() -> List[Dict[str, str]]:
        """Get fallback sources when MCP service is unavailable"""
        return [
            {
                "name": "MDN Web Docs",
                "url": "https://developer.mozilla.org",
                "type": "documentation",
                "priority": 10
            },
            {
                "name": "Python Documentation",
                "url": "https://docs.python.org",
                "type": "documentation",
                "priority": 10
            },
            {
                "name": "Stack Overflow",
                "url": "https://stackoverflow.com",
                "type": "community",
                "priority": 7
            },
            {
                "name": "GitHub",
                "url": "https://github.com",
                "type": "code_examples",
                "priority": 8
            },
            {
                "name": "Dev.to",
                "url": "https://dev.to",
                "type": "tutorials",
                "priority": 6
            }
        ]
