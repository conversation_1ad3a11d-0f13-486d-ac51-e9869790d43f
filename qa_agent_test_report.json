{"summary": {"total_tests": 8, "passed": 8, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": [{"test_name": "JavaScript React Project", "status": "PASSED", "details": {"quality_score": 32.75, "test_suites_count": 2, "generated_tests_count": 4, "security_vulnerabilities": 0, "performance_metrics": 1, "recommendations_count": 2}}, {"test_name": "Python Flask API", "status": "PASSED", "details": {"quality_score": 32.0, "security_score": 100.0, "test_coverage": 0.0, "maintainability_score": 40.0}}, {"test_name": "TypeScript Node.js Service", "status": "PASSED", "details": {"quality_score": 33.875, "enterprise_compliance": false, "security_vulnerabilities": 0, "performance_score": 100.0}}, {"test_name": "Security Vulnerability Detection", "status": "PASSED", "details": {"vulnerabilities_found": 1, "security_score": 86.0, "critical_vulnerabilities": 0, "has_sql_injection": true}}, {"test_name": "Performance Analysis", "status": "PASSED", "details": {"metrics_collected": 2, "performance_score": 50.0, "bundle_size_analyzed": true, "memory_issues_detected": true}}, {"test_name": "Test Generation", "status": "PASSED", "details": {"tests_generated": 3, "has_function_tests": true, "test_files_created": 2}}, {"test_name": "Failed Test Analysis", "status": "PASSED", "details": {"fixes_generated": 2, "assertion_fix_suggested": true, "timeout_fix_suggested": true}}, {"test_name": "Integration with Workflow", "status": "PASSED", "details": {"workflow_completed": true, "quality_score": 32.75, "feedback_generated": false, "comprehensive_report": true}}]}