# API Extensions: Extending the Aetherforge REST API

This guide covers extending Aetherforge's REST API with custom endpoints, middleware, and functionality to support specialized use cases and integrations.

## 🌐 API Architecture

### Extension Points

```mermaid
graph TB
    subgraph "API Layer"
        CORE[Core API]
        MW[Middleware]
        AUTH[Authentication]
        RATE[Rate Limiting]
    end

    subgraph "Extension Layer"
        CE[Custom Endpoints]
        CM[Custom Middleware]
        CV[Custom Validators]
        CS[Custom Serializers]
    end

    subgraph "Core Services"
        O[Orchestrator]
        AS[Agent System]
        WE[Workflow Engine]
        PS[Pheromone System]
    end

    CORE --> MW
    MW --> AUTH
    AUTH --> RATE

    CE --> CORE
    CM --> MW
    CV --> AUTH
    CS --> CORE

    CORE --> O
    CORE --> AS
    CORE --> WE
    CORE --> PS
```

### Extension Types

| Extension Type | Purpose | Examples | Complexity |
|----------------|---------|----------|------------|
| **Custom Endpoints** | New API functionality | Domain-specific operations | Medium |
| **Middleware** | Request/response processing | Logging, validation, transformation | Low |
| **Authentication** | Custom auth mechanisms | OAuth providers, SSO integration | High |
| **Serializers** | Data format handling | Custom data formats, protocols | Medium |
| **Validators** | Input validation | Business rules, data constraints | Low |
| **WebSocket Handlers** | Real-time communication | Live updates, streaming data | High |

## 🛠️ Creating Custom Endpoints

### Base Extension Framework

```python
# api/extensions/base_extension.py
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, List, Optional, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

class ExtensionType(Enum):
    ENDPOINT = "endpoint"
    MIDDLEWARE = "middleware"
    VALIDATOR = "validator"
    SERIALIZER = "serializer"

@dataclass
class ExtensionInfo:
    name: str
    version: str
    description: str
    extension_type: ExtensionType
    dependencies: List[str]
    permissions: List[str]

class BaseAPIExtension(ABC):
    """Base class for API extensions"""

    def __init__(self, info: ExtensionInfo):
        self.info = info
        self.router = APIRouter()
        self.dependencies = []
        self.middleware = []

    @abstractmethod
    def register_routes(self) -> APIRouter:
        """Register extension routes"""
        pass

    @abstractmethod
    def get_dependencies(self) -> List[Callable]:
        """Get extension dependencies"""
        pass

    def add_middleware(self, middleware: Callable):
        """Add middleware to extension"""
        self.middleware.append(middleware)

    def add_dependency(self, dependency: Callable):
        """Add dependency to extension"""
        self.dependencies.append(dependency)
```

### Custom Endpoint Example

```python
# api/extensions/analytics_extension.py
from fastapi import APIRouter, Depends, Query, Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from api.extensions.base_extension import BaseAPIExtension, ExtensionInfo, ExtensionType
from api.auth import get_current_user
from api.models import User
from services.analytics_service import AnalyticsService

class AnalyticsExtension(BaseAPIExtension):
    """Analytics API extension"""

    def __init__(self):
        info = ExtensionInfo(
            name="analytics",
            version="1.0.0",
            description="Analytics and reporting endpoints",
            extension_type=ExtensionType.ENDPOINT,
            dependencies=["analytics_service"],
            permissions=["analytics_read", "analytics_write"]
        )
        super().__init__(info)
        self.analytics_service = AnalyticsService()

    def register_routes(self) -> APIRouter:
        """Register analytics routes"""

        @self.router.get("/analytics/projects/summary")
        async def get_project_summary(
            user: User = Depends(get_current_user),
            start_date: Optional[datetime] = Query(None),
            end_date: Optional[datetime] = Query(None)
        ) -> Dict[str, Any]:
            """Get project analytics summary"""

            # Set default date range if not provided
            if not end_date:
                end_date = datetime.now()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            # Check permissions
            if not self._has_permission(user, "analytics_read"):
                raise HTTPException(status_code=403, detail="Insufficient permissions")

            # Get analytics data
            summary = await self.analytics_service.get_project_summary(
                user_id=user.id,
                start_date=start_date,
                end_date=end_date
            )

            return {
                "status": "success",
                "data": summary,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
            }

        @self.router.get("/analytics/agents/performance")
        async def get_agent_performance(
            user: User = Depends(get_current_user),
            agent_type: Optional[str] = Query(None),
            limit: int = Query(10, ge=1, le=100)
        ) -> Dict[str, Any]:
            """Get agent performance metrics"""

            if not self._has_permission(user, "analytics_read"):
                raise HTTPException(status_code=403, detail="Insufficient permissions")

            performance_data = await self.analytics_service.get_agent_performance(
                user_id=user.id,
                agent_type=agent_type,
                limit=limit
            )

            return {
                "status": "success",
                "data": performance_data,
                "filters": {
                    "agent_type": agent_type,
                    "limit": limit
                }
            }

        @self.router.post("/analytics/events")
        async def track_custom_event(
            event_data: Dict[str, Any],
            user: User = Depends(get_current_user)
        ) -> Dict[str, Any]:
            """Track custom analytics event"""

            if not self._has_permission(user, "analytics_write"):
                raise HTTPException(status_code=403, detail="Insufficient permissions")

            # Validate event data
            if not self._validate_event_data(event_data):
                raise HTTPException(status_code=400, detail="Invalid event data")

            # Track event
            event_id = await self.analytics_service.track_event(
                user_id=user.id,
                event_type=event_data["type"],
                event_data=event_data.get("data", {}),
                timestamp=datetime.now()
            )

            return {
                "status": "success",
                "event_id": event_id,
                "message": "Event tracked successfully"
            }

        @self.router.get("/analytics/reports/{report_type}")
        async def generate_report(
            report_type: str = Path(...),
            user: User = Depends(get_current_user),
            format: str = Query("json", regex="^(json|csv|pdf)$"),
            filters: Optional[str] = Query(None)
        ) -> Dict[str, Any]:
            """Generate analytics report"""

            if not self._has_permission(user, "analytics_read"):
                raise HTTPException(status_code=403, detail="Insufficient permissions")

            # Parse filters
            filter_dict = {}
            if filters:
                try:
                    import json
                    filter_dict = json.loads(filters)
                except:
                    raise HTTPException(status_code=400, detail="Invalid filters format")

            # Generate report
            report = await self.analytics_service.generate_report(
                report_type=report_type,
                user_id=user.id,
                format=format,
                filters=filter_dict
            )

            if format == "json":
                return {
                    "status": "success",
                    "report_type": report_type,
                    "data": report
                }
            else:
                # For CSV/PDF, return download URL or file content
                return {
                    "status": "success",
                    "report_type": report_type,
                    "download_url": report["download_url"],
                    "expires_at": report["expires_at"]
                }

        return self.router

    def get_dependencies(self) -> List[Callable]:
        """Get extension dependencies"""
        return [get_current_user]

    def _has_permission(self, user: User, permission: str) -> bool:
        """Check if user has required permission"""
        return permission in user.permissions

    def _validate_event_data(self, event_data: Dict[str, Any]) -> bool:
        """Validate event data structure"""
        required_fields = ["type"]
        return all(field in event_data for field in required_fields)
```

### WebSocket Extension

```python
# api/extensions/realtime_extension.py
from fastapi import WebSocket, WebSocketDisconnect, Depends
from typing import Dict, Any, List, Set
import json
import asyncio
from api.extensions.base_extension import BaseAPIExtension, ExtensionInfo, ExtensionType
from api.auth import get_websocket_user
from services.realtime_service import RealtimeService

class RealtimeExtension(BaseAPIExtension):
    """Real-time WebSocket API extension"""

    def __init__(self):
        info = ExtensionInfo(
            name="realtime",
            version="1.0.0",
            description="Real-time WebSocket communication",
            extension_type=ExtensionType.ENDPOINT,
            dependencies=["realtime_service"],
            permissions=["realtime_access"]
        )
        super().__init__(info)
        self.realtime_service = RealtimeService()
        self.active_connections: Dict[str, Set[WebSocket]] = {}

    def register_routes(self) -> APIRouter:
        """Register WebSocket routes"""

        @self.router.websocket("/ws/projects/{project_id}")
        async def project_websocket(
            websocket: WebSocket,
            project_id: str,
            user = Depends(get_websocket_user)
        ):
            """WebSocket endpoint for project updates"""

            await websocket.accept()

            # Add connection to project room
            if project_id not in self.active_connections:
                self.active_connections[project_id] = set()
            self.active_connections[project_id].add(websocket)

            try:
                # Send initial project state
                project_state = await self.realtime_service.get_project_state(project_id)
                await websocket.send_json({
                    "type": "project_state",
                    "data": project_state
                })

                # Listen for messages
                while True:
                    data = await websocket.receive_json()
                    await self._handle_websocket_message(websocket, project_id, user, data)

            except WebSocketDisconnect:
                # Remove connection
                self.active_connections[project_id].discard(websocket)
                if not self.active_connections[project_id]:
                    del self.active_connections[project_id]

        @self.router.websocket("/ws/agents/{agent_id}")
        async def agent_websocket(
            websocket: WebSocket,
            agent_id: str,
            user = Depends(get_websocket_user)
        ):
            """WebSocket endpoint for agent communication"""

            await websocket.accept()

            try:
                # Subscribe to agent events
                await self.realtime_service.subscribe_to_agent(agent_id, websocket)

                while True:
                    data = await websocket.receive_json()
                    await self._handle_agent_message(websocket, agent_id, user, data)

            except WebSocketDisconnect:
                await self.realtime_service.unsubscribe_from_agent(agent_id, websocket)

        return self.router

    async def _handle_websocket_message(self, websocket: WebSocket, project_id: str, user: Any, data: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        message_type = data.get("type")

        if message_type == "subscribe_to_updates":
            # Subscribe to specific update types
            update_types = data.get("update_types", [])
            await self.realtime_service.subscribe_to_updates(project_id, websocket, update_types)

        elif message_type == "send_command":
            # Send command to project agents
            command = data.get("command")
            result = await self.realtime_service.send_command_to_project(project_id, command)

            await websocket.send_json({
                "type": "command_result",
                "data": result
            })

        elif message_type == "request_status":
            # Request current status
            status = await self.realtime_service.get_project_status(project_id)

            await websocket.send_json({
                "type": "status_update",
                "data": status
            })

    async def _handle_agent_message(self, websocket: WebSocket, agent_id: str, user: Any, data: Dict[str, Any]):
        """Handle agent-specific WebSocket message"""
        message_type = data.get("type")

        if message_type == "task_request":
            # Request agent to perform task
            task = data.get("task")
            result = await self.realtime_service.request_agent_task(agent_id, task)

            await websocket.send_json({
                "type": "task_result",
                "data": result
            })

        elif message_type == "get_agent_status":
            # Get agent status
            status = await self.realtime_service.get_agent_status(agent_id)

            await websocket.send_json({
                "type": "agent_status",
                "data": status
            })

    async def broadcast_to_project(self, project_id: str, message: Dict[str, Any]):
        """Broadcast message to all connections in a project"""
        if project_id in self.active_connections:
            disconnected = set()

            for websocket in self.active_connections[project_id]:
                try:
                    await websocket.send_json(message)
                except:
                    disconnected.add(websocket)

            # Remove disconnected websockets
            self.active_connections[project_id] -= disconnected

    def get_dependencies(self) -> List[Callable]:
        """Get extension dependencies"""
        return [get_websocket_user]
```

### Custom Middleware Extension

```python
# api/extensions/security_extension.py
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable, Dict, Any
import time
import hashlib
from api.extensions.base_extension import BaseAPIExtension, ExtensionInfo, ExtensionType

class SecurityMiddleware(BaseHTTPMiddleware):
    """Custom security middleware"""

    def __init__(self, app, rate_limit: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.rate_limit = rate_limit
        self.window_seconds = window_seconds
        self.request_counts: Dict[str, List[float]] = {}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through security middleware"""

        # Rate limiting
        client_ip = request.client.host
        current_time = time.time()

        if not self._check_rate_limit(client_ip, current_time):
            return Response(
                content="Rate limit exceeded",
                status_code=429,
                headers={"Retry-After": str(self.window_seconds)}
            )

        # Security headers
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        # Add request ID for tracking
        request_id = hashlib.md5(f"{client_ip}{current_time}".encode()).hexdigest()[:8]
        response.headers["X-Request-ID"] = request_id

        return response

    def _check_rate_limit(self, client_ip: str, current_time: float) -> bool:
        """Check if request is within rate limit"""
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = []

        # Remove old requests outside the window
        window_start = current_time - self.window_seconds
        self.request_counts[client_ip] = [
            req_time for req_time in self.request_counts[client_ip]
            if req_time > window_start
        ]

        # Check if under rate limit
        if len(self.request_counts[client_ip]) < self.rate_limit:
            self.request_counts[client_ip].append(current_time)
            return True

        return False

class SecurityExtension(BaseAPIExtension):
    """Security middleware extension"""

    def __init__(self, rate_limit: int = 100, window_seconds: int = 60):
        info = ExtensionInfo(
            name="security",
            version="1.0.0",
            description="Security middleware and endpoints",
            extension_type=ExtensionType.MIDDLEWARE,
            dependencies=[],
            permissions=[]
        )
        super().__init__(info)
        self.rate_limit = rate_limit
        self.window_seconds = window_seconds

    def register_routes(self) -> APIRouter:
        """Register security routes"""

        @self.router.get("/security/health")
        async def security_health():
            """Security health check endpoint"""
            return {
                "status": "healthy",
                "security_features": [
                    "rate_limiting",
                    "security_headers",
                    "request_tracking"
                ]
            }

        return self.router

    def get_middleware(self) -> SecurityMiddleware:
        """Get security middleware instance"""
        return SecurityMiddleware(
            app=None,  # Will be set by the main app
            rate_limit=self.rate_limit,
            window_seconds=self.window_seconds
        )

    def get_dependencies(self) -> List[Callable]:
        """Get extension dependencies"""
        return []
```

## 🔧 Extension Registration and Management

### Extension Registry

```python
# api/extensions/registry.py
from typing import Dict, List, Type
from fastapi import FastAPI, APIRouter
from api.extensions.base_extension import BaseAPIExtension
import importlib

class ExtensionRegistry:
    """Registry for managing API extensions"""

    def __init__(self, app: FastAPI):
        self.app = app
        self.extensions: Dict[str, BaseAPIExtension] = {}
        self.routers: List[APIRouter] = []

    def register_extension(self, extension: BaseAPIExtension, prefix: str = ""):
        """Register an API extension"""
        extension_name = extension.info.name

        if extension_name in self.extensions:
            raise ValueError(f"Extension {extension_name} already registered")

        # Register extension
        self.extensions[extension_name] = extension

        # Register routes
        router = extension.register_routes()
        if prefix:
            router.prefix = f"/{prefix.strip('/')}"

        self.app.include_router(router, tags=[extension_name])
        self.routers.append(router)

        # Add middleware if extension provides it
        if hasattr(extension, 'get_middleware'):
            middleware = extension.get_middleware()
            self.app.add_middleware(type(middleware), **middleware.__dict__)

    def unregister_extension(self, extension_name: str):
        """Unregister an API extension"""
        if extension_name in self.extensions:
            del self.extensions[extension_name]
            # Note: FastAPI doesn't support runtime router removal
            # This would require app restart in production

    def get_extension(self, extension_name: str) -> BaseAPIExtension:
        """Get registered extension"""
        return self.extensions.get(extension_name)

    def list_extensions(self) -> List[Dict[str, Any]]:
        """List all registered extensions"""
        return [
            {
                "name": ext.info.name,
                "version": ext.info.version,
                "description": ext.info.description,
                "type": ext.info.extension_type.value,
                "dependencies": ext.info.dependencies,
                "permissions": ext.info.permissions
            }
            for ext in self.extensions.values()
        ]

    def load_extensions_from_config(self, config: Dict[str, Any]):
        """Load extensions from configuration"""
        for ext_config in config.get("extensions", []):
            try:
                # Import extension module
                module_path = ext_config["module"]
                class_name = ext_config["class"]

                module = importlib.import_module(module_path)
                extension_class = getattr(module, class_name)

                # Create and register extension
                extension_params = ext_config.get("params", {})
                extension = extension_class(**extension_params)

                prefix = ext_config.get("prefix", "")
                self.register_extension(extension, prefix)

            except Exception as e:
                print(f"Failed to load extension {ext_config}: {e}")
```

### Extension Configuration

```yaml
# config/extensions.yml
extensions:
  - name: analytics
    module: api.extensions.analytics_extension
    class: AnalyticsExtension
    prefix: api/v1
    enabled: true
    params: {}

  - name: realtime
    module: api.extensions.realtime_extension
    class: RealtimeExtension
    prefix: api/v1
    enabled: true
    params: {}

  - name: security
    module: api.extensions.security_extension
    class: SecurityExtension
    prefix: ""
    enabled: true
    params:
      rate_limit: 100
      window_seconds: 60
```

## 📊 Testing API Extensions

### Extension Testing Framework

```python
# tests/test_api_extensions.py
import pytest
from fastapi.testclient import TestClient
from fastapi import FastAPI
from api.extensions.analytics_extension import AnalyticsExtension
from api.extensions.registry import ExtensionRegistry

@pytest.fixture
def app():
    """Create test FastAPI app"""
    app = FastAPI()
    return app

@pytest.fixture
def extension_registry(app):
    """Create extension registry"""
    return ExtensionRegistry(app)

@pytest.fixture
def analytics_extension():
    """Create analytics extension"""
    return AnalyticsExtension()

@pytest.fixture
def client(app, extension_registry, analytics_extension):
    """Create test client with extensions"""
    extension_registry.register_extension(analytics_extension, "api/v1")
    return TestClient(app)

def test_analytics_project_summary(client):
    """Test analytics project summary endpoint"""
    # Mock authentication
    headers = {"Authorization": "Bearer test_token"}

    response = client.get("/api/v1/analytics/projects/summary", headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "data" in data

def test_analytics_custom_event(client):
    """Test custom event tracking"""
    headers = {"Authorization": "Bearer test_token"}
    event_data = {
        "type": "custom_event",
        "data": {"key": "value"}
    }

    response = client.post("/api/v1/analytics/events", json=event_data, headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "event_id" in data

def test_extension_registration(extension_registry, analytics_extension):
    """Test extension registration"""
    extension_registry.register_extension(analytics_extension, "api/v1")

    extensions = extension_registry.list_extensions()
    assert len(extensions) == 1
    assert extensions[0]["name"] == "analytics"
```

## 🔗 Next Steps

- **[Custom Agents](custom-agents.md)** - Create specialized agents
- **[Plugin Development](plugin-development.md)** - Create plugins
- **[Integration Guide](integration-guide.md)** - External system integration
- **[Component Adapters](component-adapters.md)** - Service integration patterns

## 📖 Resources

- [API Extension Examples](https://github.com/aetherforge/api-extension-examples)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [API Development Best Practices](https://docs.aetherforge.dev/best-practices/api)