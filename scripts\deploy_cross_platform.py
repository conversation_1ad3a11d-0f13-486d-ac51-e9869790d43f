#!/usr/bin/env python3
"""
Cross-Platform TaoForge Deployment Script
Supports Windows, macOS, and Linux environments
"""

import os
import sys
import platform
import subprocess
import shutil
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Color codes for cross-platform output
class Colors:
    if platform.system() == "Windows":
        # Windows color codes
        RED = '\033[91m'
        GREEN = '\033[92m'
        YELLOW = '\033[93m'
        BLUE = '\033[94m'
        NC = '\033[0m'
    else:
        # Unix color codes
        RED = '\033[0;31m'
        GREEN = '\033[0;32m'
        YELLOW = '\033[1;33m'
        BLUE = '\033[0;34m'
        NC = '\033[0m'

class CrossPlatformDeployer:
    """Cross-platform deployment manager for TaoForge"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.is_windows = self.platform == "windows"
        self.is_macos = self.platform == "darwin"
        self.is_linux = self.platform == "linux"
        
        # Platform-specific configurations
        self.docker_compose_cmd = self._get_docker_compose_command()
        self.shell_cmd = self._get_shell_command()
        
    def _get_docker_compose_command(self) -> List[str]:
        """Get the appropriate Docker Compose command for the platform"""
        # Try docker compose (newer) first, then docker-compose (legacy)
        for cmd in [["docker", "compose"], ["docker-compose"]]:
            try:
                result = subprocess.run(
                    cmd + ["--version"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                if result.returncode == 0:
                    return cmd
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        raise RuntimeError("Docker Compose not found. Please install Docker Desktop or Docker Compose.")
    
    def _get_shell_command(self) -> List[str]:
        """Get the appropriate shell command for the platform"""
        if self.is_windows:
            return ["powershell", "-Command"]
        else:
            return ["/bin/bash", "-c"]
    
    def log(self, level: str, message: str):
        """Cross-platform logging with colors"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        color_map = {
            "ERROR": Colors.RED + "❌",
            "SUCCESS": Colors.GREEN + "✅", 
            "WARNING": Colors.YELLOW + "⚠️",
            "INFO": Colors.BLUE + "ℹ️"
        }
        
        icon = color_map.get(level, "")
        print(f"{icon} [{timestamp}] {message}{Colors.NC}")
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are installed"""
        self.log("INFO", f"Checking prerequisites on {self.platform.title()}...")
        
        prerequisites = [
            ("Python", ["python", "--version"]),
            ("Docker", ["docker", "--version"]),
            ("Docker Compose", self.docker_compose_cmd + ["--version"])
        ]
        
        all_good = True
        
        for name, cmd in prerequisites:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip().split('\n')[0]
                    self.log("SUCCESS", f"{name} is available: {version}")
                else:
                    self.log("ERROR", f"{name} check failed")
                    all_good = False
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.log("ERROR", f"{name} is not installed or not in PATH")
                all_good = False
        
        return all_good
    
    def setup_environment(self, environment: str = "development") -> bool:
        """Set up environment configuration"""
        self.log("INFO", f"Setting up {environment} environment...")
        
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if not env_file.exists() and env_example.exists():
            self.log("INFO", "Creating .env file from .env.example...")
            shutil.copy2(env_example, env_file)
            self.log("WARNING", "Please edit .env file with your configuration")
            
            if self.is_windows:
                subprocess.run(["notepad", str(env_file)], check=False)
            elif self.is_macos:
                subprocess.run(["open", "-t", str(env_file)], check=False)
            else:
                # Linux - try common editors
                for editor in ["nano", "vim", "gedit"]:
                    if shutil.which(editor):
                        subprocess.run([editor, str(env_file)], check=False)
                        break
        
        return env_file.exists()
    
    def create_directories(self) -> bool:
        """Create necessary directories"""
        self.log("INFO", "Creating necessary directories...")
        
        directories = [
            "projects",
            "logs",
            "backups",
            "monitoring/grafana/dashboards",
            "nginx/ssl"
        ]
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                self.log("SUCCESS", f"Created directory: {directory}")
            return True
        except Exception as e:
            self.log("ERROR", f"Failed to create directories: {e}")
            return False
    
    def deploy_services(self, environment: str = "development") -> bool:
        """Deploy services using Docker Compose"""
        self.log("INFO", f"Deploying services for {environment}...")
        
        compose_file = "docker-compose.yml"
        if environment == "production":
            compose_file = "docker-compose.prod.yml"
        elif environment == "test":
            compose_file = "docker-compose.test.yml"
        
        compose_path = self.project_root / compose_file
        if not compose_path.exists():
            self.log("ERROR", f"Compose file not found: {compose_file}")
            return False
        
        try:
            # Stop existing services
            self.log("INFO", "Stopping existing services...")
            subprocess.run(
                self.docker_compose_cmd + ["-f", str(compose_path), "down", "--remove-orphans"],
                cwd=self.project_root,
                check=False
            )
            
            # Build and start services
            self.log("INFO", "Building and starting services...")
            result = subprocess.run(
                self.docker_compose_cmd + ["-f", str(compose_path), "up", "-d", "--build"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.log("SUCCESS", "Services deployed successfully")
                return True
            else:
                self.log("ERROR", f"Deployment failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.log("ERROR", f"Deployment error: {e}")
            return False
    
    def health_check(self) -> bool:
        """Perform health checks on deployed services"""
        self.log("INFO", "Performing health checks...")
        
        services = [
            ("Orchestrator", "http://localhost:8000/health"),
            ("Archon", "http://localhost:8100/health"),
            ("Pheromind", "http://localhost:8502/health")
        ]
        
        all_healthy = True
        
        # Import requests here to handle missing dependency gracefully
        try:
            import requests
        except ImportError:
            self.log("WARNING", "requests library not available, skipping HTTP health checks")
            return True
        
        for service_name, url in services:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    self.log("SUCCESS", f"{service_name} is healthy")
                else:
                    self.log("WARNING", f"{service_name} returned status {response.status_code}")
                    all_healthy = False
            except requests.exceptions.RequestException:
                self.log("WARNING", f"{service_name} is not responding")
                all_healthy = False
        
        return all_healthy
    
    def show_access_points(self):
        """Display service access points"""
        self.log("INFO", "Service Access Points:")
        
        access_points = [
            ("Main API", "http://localhost:8000"),
            ("API Documentation", "http://localhost:8000/docs"),
            ("Archon API", "http://localhost:8100"),
            ("Archon UI", "http://localhost:8501"),
            ("Pheromind", "http://localhost:8502"),
            ("BMAD", "http://localhost:8503")
        ]
        
        for name, url in access_points:
            print(f"  🌐 {name}: {url}")
    
    def deploy(self, environment: str = "development") -> bool:
        """Main deployment function"""
        self.log("INFO", f"Starting TaoForge deployment on {self.platform.title()}...")
        
        steps = [
            ("Prerequisites Check", lambda: self.check_prerequisites()),
            ("Environment Setup", lambda: self.setup_environment(environment)),
            ("Directory Creation", lambda: self.create_directories()),
            ("Service Deployment", lambda: self.deploy_services(environment)),
            ("Health Check", lambda: self.health_check())
        ]
        
        for step_name, step_func in steps:
            self.log("INFO", f"Executing: {step_name}")
            if not step_func():
                self.log("ERROR", f"Failed at step: {step_name}")
                return False
        
        self.log("SUCCESS", "🎉 TaoForge deployment completed successfully!")
        self.show_access_points()
        return True

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cross-platform TaoForge deployment")
    parser.add_argument(
        "--environment", 
        choices=["development", "production", "test"],
        default="development",
        help="Deployment environment"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check prerequisites, don't deploy"
    )
    
    args = parser.parse_args()
    
    deployer = CrossPlatformDeployer()
    
    if args.check_only:
        success = deployer.check_prerequisites()
        sys.exit(0 if success else 1)
    else:
        success = deployer.deploy(args.environment)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
