"""
Performance and Load Tests for TaoForge
Tests system performance under various load conditions
"""

import pytest
import asyncio
import time
import statistics
import concurrent.futures
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import threading
import psutil
import os

# Import system components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from pheromone_system import PheromoneSystem
from orchestrator import app
from agent_executors import create_agent_executor
from project_generator_standalone import ProjectGenerationPipeline
from fastapi.testclient import TestClient


class TestPerformanceMetrics:
    """Test performance metrics and monitoring"""
    
    def test_pheromone_system_performance(self):
        """Test pheromone system performance under load"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        temp_file.close()
        
        try:
            system = PheromoneSystem(storage_file=temp_file.name)
            
            # Measure pheromone drop performance
            start_time = time.time()
            num_pheromones = 1000
            
            for i in range(num_pheromones):
                system.drop_pheromone(
                    signal=f"test_signal_{i % 10}",
                    payload={"index": i, "data": f"test_data_{i}"},
                    project_id=f"project_{i % 5}",
                    agent_id=f"agent_{i % 3}"
                )
            
            drop_time = time.time() - start_time
            
            # Performance assertions
            assert drop_time < 5.0  # Should complete in under 5 seconds
            assert drop_time / num_pheromones < 0.01  # Less than 10ms per pheromone
            
            # Measure retrieval performance
            start_time = time.time()
            
            for i in range(100):
                pheromones = system.get_pheromones(project_id=f"project_{i % 5}")
                assert len(pheromones) > 0
            
            retrieval_time = time.time() - start_time
            assert retrieval_time < 1.0  # Should complete in under 1 second
            
            # Test statistics performance
            start_time = time.time()
            stats = system.get_statistics()
            stats_time = time.time() - start_time
            
            assert stats_time < 0.1  # Statistics should be fast
            assert stats["total_pheromones"] == num_pheromones
            
        finally:
            os.unlink(temp_file.name)
    
    @pytest.mark.asyncio
    async def test_concurrent_pheromone_operations(self):
        """Test concurrent pheromone operations"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        temp_file.close()
        
        try:
            system = PheromoneSystem(storage_file=temp_file.name)
            
            # Test concurrent drops
            async def drop_pheromones(agent_id, count):
                for i in range(count):
                    system.drop_pheromone(
                        signal=f"concurrent_signal_{agent_id}",
                        payload={"agent": agent_id, "index": i},
                        project_id="concurrent_project",
                        agent_id=f"agent_{agent_id}"
                    )
            
            # Run concurrent operations
            start_time = time.time()
            
            tasks = []
            for agent_id in range(10):
                task = drop_pheromones(agent_id, 50)
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            concurrent_time = time.time() - start_time
            
            # Verify all pheromones were created
            all_pheromones = system.get_pheromones(project_id="concurrent_project")
            assert len(all_pheromones) == 500  # 10 agents * 50 pheromones each
            
            # Performance assertion
            assert concurrent_time < 3.0  # Should complete in under 3 seconds
            
        finally:
            os.unlink(temp_file.name)
    
    def test_memory_usage_under_load(self):
        """Test memory usage under heavy load"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        temp_file.close()
        
        try:
            system = PheromoneSystem(storage_file=temp_file.name)
            
            # Measure initial memory
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Add large number of pheromones
            num_pheromones = 5000
            for i in range(num_pheromones):
                system.drop_pheromone(
                    signal=f"memory_test_{i}",
                    payload={"large_data": "x" * 1000},  # 1KB payload
                    project_id=f"project_{i % 10}",
                    agent_id=f"agent_{i % 5}"
                )
            
            # Measure memory after load
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # Memory usage should be reasonable (less than 100MB increase)
            assert memory_increase < 100
            
            # Test cleanup reduces memory
            system.cleanup_expired()
            
            # Force garbage collection
            import gc
            gc.collect()
            
            cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Memory should not continuously grow
            assert cleanup_memory <= final_memory
            
        finally:
            os.unlink(temp_file.name)


class TestAPIPerformance:
    """Test API endpoint performance"""
    
    @pytest.fixture
    def test_client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_health_endpoint_performance(self, test_client):
        """Test health endpoint response time"""
        response_times = []
        
        for _ in range(100):
            start_time = time.time()
            response = test_client.get("/health")
            response_time = time.time() - start_time
            
            assert response.status_code == 200
            response_times.append(response_time)
        
        # Performance metrics
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        # Performance assertions
        assert avg_response_time < 0.1  # Average under 100ms
        assert max_response_time < 0.5  # Max under 500ms
        assert p95_response_time < 0.2  # 95th percentile under 200ms
    
    def test_pheromone_api_performance(self, test_client):
        """Test pheromone API endpoint performance"""
        # Test pheromone creation performance
        creation_times = []
        
        for i in range(50):
            pheromone_data = {
                "signal": f"performance_test_{i}",
                "payload": {"test": True, "index": i},
                "project_id": "performance_project"
            }
            
            start_time = time.time()
            response = test_client.post("/pheromones", json=pheromone_data)
            creation_time = time.time() - start_time
            
            assert response.status_code == 200
            creation_times.append(creation_time)
        
        # Test pheromone retrieval performance
        retrieval_times = []
        
        for _ in range(20):
            start_time = time.time()
            response = test_client.get("/pheromones?project_id=performance_project")
            retrieval_time = time.time() - start_time
            
            assert response.status_code == 200
            retrieval_times.append(retrieval_time)
        
        # Performance assertions
        avg_creation_time = statistics.mean(creation_times)
        avg_retrieval_time = statistics.mean(retrieval_times)
        
        assert avg_creation_time < 0.1  # Creation under 100ms
        assert avg_retrieval_time < 0.1  # Retrieval under 100ms
    
    def test_concurrent_api_requests(self, test_client):
        """Test API performance under concurrent load"""
        def make_request(request_id):
            start_time = time.time()
            response = test_client.get("/health")
            end_time = time.time()
            
            return {
                "request_id": request_id,
                "status_code": response.status_code,
                "response_time": end_time - start_time
            }
        
        # Test with concurrent requests
        num_concurrent = 20
        num_requests_per_thread = 10
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = []
            
            for thread_id in range(num_concurrent):
                for req_id in range(num_requests_per_thread):
                    future = executor.submit(make_request, f"{thread_id}_{req_id}")
                    futures.append(future)
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Verify all requests succeeded
        assert len(results) == num_concurrent * num_requests_per_thread
        assert all(result["status_code"] == 200 for result in results)
        
        # Performance metrics
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        # Performance assertions
        assert total_time < 10.0  # Total time under 10 seconds
        assert avg_response_time < 1.0  # Average response time under 1 second
        assert max_response_time < 2.0  # Max response time under 2 seconds


class TestAgentPerformance:
    """Test agent execution performance"""
    
    @pytest.mark.asyncio
    async def test_agent_executor_performance(self):
        """Test agent executor performance"""
        executor = create_agent_executor("analyst")
        
        # Mock OpenAI to avoid actual API calls
        with patch.object(executor, 'call_openai', return_value="Mock response"):
            execution_times = []
            
            for i in range(10):
                context = {
                    "prompt": f"Test prompt {i}",
                    "project_path": f"/tmp/test_{i}",
                    "project_type": "test"
                }
                
                start_time = time.time()
                result = await executor.execute(context)
                execution_time = time.time() - start_time
                
                assert result["success"] is True
                execution_times.append(execution_time)
            
            # Performance metrics
            avg_execution_time = statistics.mean(execution_times)
            max_execution_time = max(execution_times)
            
            # Performance assertions (with mocked calls, should be very fast)
            assert avg_execution_time < 0.1  # Average under 100ms
            assert max_execution_time < 0.5  # Max under 500ms
    
    @pytest.mark.asyncio
    async def test_concurrent_agent_execution(self):
        """Test concurrent agent execution"""
        async def execute_agent(agent_role, task_id):
            executor = create_agent_executor(agent_role)
            
            with patch.object(executor, 'call_openai', return_value=f"Mock response for {agent_role}"):
                context = {
                    "prompt": f"Task {task_id} for {agent_role}",
                    "project_path": f"/tmp/concurrent_{task_id}",
                    "project_type": "test"
                }
                
                start_time = time.time()
                result = await executor.execute(context)
                execution_time = time.time() - start_time
                
                return {
                    "agent_role": agent_role,
                    "task_id": task_id,
                    "success": result["success"],
                    "execution_time": execution_time
                }
        
        # Test concurrent execution of different agents
        start_time = time.time()
        
        tasks = []
        agent_roles = ["analyst", "architect", "developer", "qa"]
        
        for i in range(5):  # 5 tasks per agent type
            for role in agent_roles:
                task = execute_agent(role, i)
                tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # Verify all executions succeeded
        assert len(results) == 20  # 5 tasks * 4 agent types
        assert all(result["success"] for result in results)
        
        # Performance assertions
        assert total_time < 5.0  # Should complete in under 5 seconds
        
        # Check individual execution times
        execution_times = [result["execution_time"] for result in results]
        avg_execution_time = statistics.mean(execution_times)
        assert avg_execution_time < 0.2  # Average under 200ms


class TestProjectGenerationPerformance:
    """Test project generation pipeline performance"""
    
    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace"""
        workspace = tempfile.mkdtemp(prefix="taoforge_perf_")
        yield workspace
        shutil.rmtree(workspace, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_project_generation_timing(self, temp_workspace):
        """Test project generation timing"""
        project_path = Path(temp_workspace) / "performance_test_project"
        
        pipeline = ProjectGenerationPipeline()
        
        # Mock all external calls for consistent timing
        with patch('src.agent_executors.call_openai_api', return_value="Mock AI response"):
            with patch('src.file_generators.generate_project_files') as mock_files:
                mock_files.return_value = {
                    "package.json": '{"name": "test"}',
                    "README.md": "# Test Project",
                    "src/index.js": "console.log('test');"
                }
                
                start_time = time.time()
                
                result = await pipeline.generate_project(
                    prompt="Create a test application",
                    project_name="PerformanceTest",
                    project_type="fullstack",
                    project_path=str(project_path)
                )
                
                generation_time = time.time() - start_time
                
                # Verify success
                assert result["success"] is True
                
                # Performance assertion
                assert generation_time < 10.0  # Should complete in under 10 seconds
                
                # Verify project was created
                assert project_path.exists()
                assert (project_path / ".aetherforge.json").exists()
    
    @pytest.mark.asyncio
    async def test_multiple_project_generation(self, temp_workspace):
        """Test generating multiple projects concurrently"""
        async def generate_project(project_id):
            project_path = Path(temp_workspace) / f"project_{project_id}"
            pipeline = ProjectGenerationPipeline()
            
            with patch('src.agent_executors.call_openai_api', return_value="Mock response"):
                with patch('src.file_generators.generate_project_files') as mock_files:
                    mock_files.return_value = {
                        "package.json": f'{{"name": "project-{project_id}"}}',
                        "README.md": f"# Project {project_id}"
                    }
                    
                    start_time = time.time()
                    
                    result = await pipeline.generate_project(
                        prompt=f"Create project {project_id}",
                        project_name=f"Project{project_id}",
                        project_type="api",
                        project_path=str(project_path)
                    )
                    
                    generation_time = time.time() - start_time
                    
                    return {
                        "project_id": project_id,
                        "success": result["success"],
                        "generation_time": generation_time,
                        "path_exists": project_path.exists()
                    }
        
        # Generate multiple projects concurrently
        start_time = time.time()
        
        tasks = [generate_project(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # Verify all projects were generated successfully
        assert len(results) == 3
        assert all(result["success"] for result in results)
        assert all(result["path_exists"] for result in results)
        
        # Performance assertions
        assert total_time < 15.0  # Should complete in under 15 seconds
        
        # Individual project generation should be reasonable
        generation_times = [result["generation_time"] for result in results]
        avg_generation_time = statistics.mean(generation_times)
        assert avg_generation_time < 8.0  # Average under 8 seconds per project


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
