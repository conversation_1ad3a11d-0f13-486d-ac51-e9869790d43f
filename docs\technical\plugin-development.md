# Plugin Development Guide

This guide covers creating plugins for Aetherforge to extend functionality, add new features, and integrate with external tools and services.

## 🔌 Plugin Architecture

### Plugin System Overview

```mermaid
graph TB
    subgraph "Plugin System"
        PM[Plugin Manager]
        PR[Plugin Registry]
        PL[Plugin Loader]
        PE[Plugin Engine]
    end
    
    subgraph "Core System"
        O[Orchestrator]
        API[REST API]
        WE[Workflow Engine]
        AS[Agent System]
    end
    
    subgraph "Plugins"
        CP[Core Plugins]
        UP[User Plugins]
        TP[Third-party Plugins]
    end
    
    PM --> PR
    PM --> PL
    PM --> PE
    
    PE --> O
    PE --> API
    PE --> WE
    PE --> AS
    
    PL --> CP
    PL --> UP
    PL --> TP
```

### Plugin Types

| Plugin Type | Purpose | Examples |
|-------------|---------|----------|
| **Core Plugins** | Essential system functionality | Authentication, logging, monitoring |
| **Agent Plugins** | Extend agent capabilities | New tools, specialized skills |
| **Workflow Plugins** | Custom workflow steps | Deployment, testing, validation |
| **Integration Plugins** | External service integration | Cloud providers, databases, APIs |
| **UI Plugins** | Extend user interface | Custom panels, visualizations |
| **Tool Plugins** | Add development tools | Code generators, analyzers |

## 🛠️ Creating Your First Plugin

### Step 1: Plugin Structure

```
my_plugin/
├── plugin.json              # Plugin manifest
├── __init__.py              # Plugin entry point
├── main.py                  # Main plugin logic
├── config/                  # Configuration files
│   ├── settings.json
│   └── schema.json
├── assets/                  # Static assets
│   ├── icons/
│   └── templates/
├── tests/                   # Plugin tests
│   ├── test_main.py
│   └── test_integration.py
├── docs/                    # Plugin documentation
│   ├── README.md
│   └── usage.md
└── requirements.txt         # Plugin dependencies
```

### Step 2: Plugin Manifest

```json
{
  "name": "my-awesome-plugin",
  "version": "1.0.0",
  "description": "An awesome plugin for Aetherforge",
  "author": "Your Name",
  "email": "<EMAIL>",
  "license": "MIT",
  "homepage": "https://github.com/yourusername/my-awesome-plugin",
  "repository": {
    "type": "git",
    "url": "https://github.com/yourusername/my-awesome-plugin.git"
  },
  "keywords": ["aetherforge", "plugin", "awesome"],
  "aetherforge": {
    "min_version": "1.0.0",
    "max_version": "2.0.0"
  },
  "plugin": {
    "type": "integration",
    "category": "development-tools",
    "entry_point": "main:MyAwesomePlugin",
    "dependencies": ["requests", "pydantic"],
    "permissions": [
      "file_system_read",
      "file_system_write",
      "network_access",
      "agent_communication"
    ],
    "hooks": [
      "pre_project_creation",
      "post_project_creation",
      "agent_task_execution"
    ],
    "api_endpoints": [
      {
        "path": "/plugins/my-awesome-plugin/status",
        "method": "GET",
        "handler": "get_status"
      },
      {
        "path": "/plugins/my-awesome-plugin/configure",
        "method": "POST",
        "handler": "configure"
      }
    ],
    "ui_components": [
      {
        "type": "panel",
        "name": "My Awesome Panel",
        "component": "components/AwesomePanel.vue"
      }
    ]
  },
  "configuration": {
    "schema": "config/schema.json",
    "defaults": "config/settings.json"
  }
}
```

### Step 3: Base Plugin Class

```python
# main.py
import asyncio
import logging
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PluginStatus(Enum):
    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class PluginInfo:
    name: str
    version: str
    description: str
    author: str
    status: PluginStatus
    dependencies: List[str]
    permissions: List[str]

class BasePlugin(ABC):
    """Base class for all Aetherforge plugins"""
    
    def __init__(self, plugin_info: PluginInfo):
        self.info = plugin_info
        self.status = PluginStatus.INACTIVE
        self.config = {}
        self.hooks = {}
        self.api_handlers = {}
        self.logger = logging.getLogger(f"plugin.{self.info.name}")
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin with configuration"""
        pass
    
    @abstractmethod
    async def activate(self) -> bool:
        """Activate the plugin"""
        pass
    
    @abstractmethod
    async def deactivate(self) -> bool:
        """Deactivate the plugin"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup plugin resources"""
        pass
    
    async def get_status(self) -> Dict[str, Any]:
        """Get plugin status information"""
        return {
            "name": self.info.name,
            "version": self.info.version,
            "status": self.status.value,
            "description": self.info.description,
            "author": self.info.author
        }
    
    def register_hook(self, hook_name: str, handler):
        """Register a hook handler"""
        if hook_name not in self.hooks:
            self.hooks[hook_name] = []
        self.hooks[hook_name].append(handler)
    
    def register_api_handler(self, path: str, method: str, handler):
        """Register an API endpoint handler"""
        key = f"{method.upper()}:{path}"
        self.api_handlers[key] = handler
    
    async def execute_hook(self, hook_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute registered hooks"""
        if hook_name not in self.hooks:
            return context
        
        for handler in self.hooks[hook_name]:
            try:
                context = await handler(context)
            except Exception as e:
                self.logger.error(f"Hook {hook_name} failed: {e}")
        
        return context

class MyAwesomePlugin(BasePlugin):
    """Example plugin implementation"""
    
    def __init__(self):
        plugin_info = PluginInfo(
            name="my-awesome-plugin",
            version="1.0.0",
            description="An awesome plugin for Aetherforge",
            author="Your Name",
            status=PluginStatus.INACTIVE,
            dependencies=["requests", "pydantic"],
            permissions=["file_system_read", "network_access"]
        )
        super().__init__(plugin_info)
        
        # Register hooks
        self.register_hook("pre_project_creation", self._pre_project_creation)
        self.register_hook("post_project_creation", self._post_project_creation)
        
        # Register API handlers
        self.register_api_handler("/status", "GET", self._api_get_status)
        self.register_api_handler("/configure", "POST", self._api_configure)
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin"""
        try:
            self.config = config
            
            # Validate configuration
            if not self._validate_config(config):
                self.logger.error("Invalid configuration")
                return False
            
            # Initialize external connections, load models, etc.
            await self._setup_external_connections()
            
            self.logger.info(f"Plugin {self.info.name} initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize plugin: {e}")
            return False
    
    async def activate(self) -> bool:
        """Activate the plugin"""
        try:
            # Start background tasks, enable features, etc.
            await self._start_background_tasks()
            
            self.status = PluginStatus.ACTIVE
            self.logger.info(f"Plugin {self.info.name} activated")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to activate plugin: {e}")
            self.status = PluginStatus.ERROR
            return False
    
    async def deactivate(self) -> bool:
        """Deactivate the plugin"""
        try:
            # Stop background tasks, disable features, etc.
            await self._stop_background_tasks()
            
            self.status = PluginStatus.INACTIVE
            self.logger.info(f"Plugin {self.info.name} deactivated")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deactivate plugin: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources"""
        try:
            # Close connections, release resources, etc.
            await self._cleanup_resources()
            self.logger.info(f"Plugin {self.info.name} cleaned up")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup plugin: {e}")
    
    # Hook handlers
    async def _pre_project_creation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle pre-project creation hook"""
        self.logger.info("Pre-project creation hook executed")
        
        # Add custom logic here
        project_config = context.get("project_config", {})
        
        # Example: Add custom project metadata
        if "metadata" not in project_config:
            project_config["metadata"] = {}
        
        project_config["metadata"]["awesome_plugin_version"] = self.info.version
        project_config["metadata"]["processed_by_awesome_plugin"] = True
        
        context["project_config"] = project_config
        return context
    
    async def _post_project_creation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle post-project creation hook"""
        self.logger.info("Post-project creation hook executed")
        
        project = context.get("project")
        if project:
            # Example: Send notification, update external systems, etc.
            await self._notify_external_system(project)
        
        return context
    
    # API handlers
    async def _api_get_status(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle GET /status API endpoint"""
        return await self.get_status()
    
    async def _api_configure(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle POST /configure API endpoint"""
        new_config = request.get("config", {})
        
        if self._validate_config(new_config):
            self.config.update(new_config)
            return {"status": "success", "message": "Configuration updated"}
        else:
            return {"status": "error", "message": "Invalid configuration"}
    
    # Helper methods
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration"""
        required_fields = ["api_key", "endpoint_url"]
        return all(field in config for field in required_fields)
    
    async def _setup_external_connections(self):
        """Setup external connections"""
        # Initialize HTTP clients, database connections, etc.
        pass
    
    async def _start_background_tasks(self):
        """Start background tasks"""
        # Start monitoring, periodic tasks, etc.
        pass
    
    async def _stop_background_tasks(self):
        """Stop background tasks"""
        # Stop and cleanup background tasks
        pass
    
    async def _cleanup_resources(self):
        """Cleanup resources"""
        # Close connections, release memory, etc.
        pass
    
    async def _notify_external_system(self, project: Dict[str, Any]):
        """Notify external system about project creation"""
        # Send webhook, update database, etc.
        pass
```

### Step 4: Plugin Manager Integration

```python
# plugin_manager.py
import json
import importlib
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Type
from plugins.base_plugin import BasePlugin, PluginStatus

class PluginManager:
    """Manages plugin lifecycle and operations"""
    
    def __init__(self, plugin_directory: str = "plugins"):
        self.plugin_directory = Path(plugin_directory)
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_configs: Dict[str, Dict] = {}
        self.hooks: Dict[str, List[BasePlugin]] = {}
    
    async def discover_plugins(self) -> List[str]:
        """Discover available plugins"""
        discovered = []
        
        for plugin_path in self.plugin_directory.iterdir():
            if plugin_path.is_dir():
                manifest_path = plugin_path / "plugin.json"
                if manifest_path.exists():
                    try:
                        with open(manifest_path) as f:
                            manifest = json.load(f)
                        discovered.append(manifest["name"])
                    except Exception as e:
                        print(f"Failed to load plugin manifest {manifest_path}: {e}")
        
        return discovered
    
    async def load_plugin(self, plugin_name: str) -> bool:
        """Load a plugin"""
        try:
            plugin_path = self.plugin_directory / plugin_name
            manifest_path = plugin_path / "plugin.json"
            
            # Load manifest
            with open(manifest_path) as f:
                manifest = json.load(f)
            
            # Import plugin module
            module_name = f"plugins.{plugin_name}.main"
            module = importlib.import_module(module_name)
            
            # Get plugin class
            entry_point = manifest["plugin"]["entry_point"]
            class_name = entry_point.split(":")[1]
            plugin_class = getattr(module, class_name)
            
            # Create plugin instance
            plugin = plugin_class()
            
            # Load configuration
            config = self._load_plugin_config(plugin_name)
            
            # Initialize plugin
            if await plugin.initialize(config):
                self.plugins[plugin_name] = plugin
                self.plugin_configs[plugin_name] = config
                self._register_plugin_hooks(plugin)
                return True
            
            return False
            
        except Exception as e:
            print(f"Failed to load plugin {plugin_name}: {e}")
            return False
    
    async def activate_plugin(self, plugin_name: str) -> bool:
        """Activate a plugin"""
        if plugin_name not in self.plugins:
            return False
        
        plugin = self.plugins[plugin_name]
        return await plugin.activate()
    
    async def deactivate_plugin(self, plugin_name: str) -> bool:
        """Deactivate a plugin"""
        if plugin_name not in self.plugins:
            return False
        
        plugin = self.plugins[plugin_name]
        return await plugin.deactivate()
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a plugin"""
        if plugin_name not in self.plugins:
            return False
        
        plugin = self.plugins[plugin_name]
        
        # Deactivate if active
        if plugin.status == PluginStatus.ACTIVE:
            await plugin.deactivate()
        
        # Cleanup
        await plugin.cleanup()
        
        # Remove from registry
        del self.plugins[plugin_name]
        del self.plugin_configs[plugin_name]
        self._unregister_plugin_hooks(plugin)
        
        return True
    
    async def execute_hooks(self, hook_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute all registered hooks for an event"""
        if hook_name not in self.hooks:
            return context
        
        for plugin in self.hooks[hook_name]:
            if plugin.status == PluginStatus.ACTIVE:
                context = await plugin.execute_hook(hook_name, context)
        
        return context
    
    def get_plugin_status(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """Get plugin status"""
        if plugin_name not in self.plugins:
            return None
        
        return asyncio.create_task(self.plugins[plugin_name].get_status())
    
    def list_plugins(self) -> List[Dict[str, Any]]:
        """List all loaded plugins"""
        return [
            {
                "name": name,
                "status": plugin.status.value,
                "version": plugin.info.version,
                "description": plugin.info.description
            }
            for name, plugin in self.plugins.items()
        ]
    
    def _load_plugin_config(self, plugin_name: str) -> Dict[str, Any]:
        """Load plugin configuration"""
        config_path = self.plugin_directory / plugin_name / "config" / "settings.json"
        
        if config_path.exists():
            with open(config_path) as f:
                return json.load(f)
        
        return {}
    
    def _register_plugin_hooks(self, plugin: BasePlugin):
        """Register plugin hooks"""
        for hook_name in plugin.hooks:
            if hook_name not in self.hooks:
                self.hooks[hook_name] = []
            self.hooks[hook_name].append(plugin)
    
    def _unregister_plugin_hooks(self, plugin: BasePlugin):
        """Unregister plugin hooks"""
        for hook_name in plugin.hooks:
            if hook_name in self.hooks:
                self.hooks[hook_name] = [p for p in self.hooks[hook_name] if p != plugin]
```

## 🔧 Advanced Plugin Features

### Configuration Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "api_key": {
      "type": "string",
      "description": "API key for external service",
      "minLength": 10
    },
    "endpoint_url": {
      "type": "string",
      "format": "uri",
      "description": "External service endpoint URL"
    },
    "timeout": {
      "type": "integer",
      "minimum": 1,
      "maximum": 300,
      "default": 30,
      "description": "Request timeout in seconds"
    },
    "features": {
      "type": "object",
      "properties": {
        "auto_sync": {
          "type": "boolean",
          "default": true
        },
        "notifications": {
          "type": "boolean",
          "default": false
        }
      }
    }
  },
  "required": ["api_key", "endpoint_url"],
  "additionalProperties": false
}
```

### Plugin Testing

```python
# tests/test_my_awesome_plugin.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from plugins.my_awesome_plugin.main import MyAwesomePlugin

@pytest.fixture
async def plugin():
    plugin = MyAwesomePlugin()
    config = {
        "api_key": "test_key",
        "endpoint_url": "https://api.example.com"
    }
    await plugin.initialize(config)
    yield plugin
    await plugin.cleanup()

@pytest.mark.asyncio
async def test_plugin_initialization(plugin):
    """Test plugin initialization"""
    assert plugin.info.name == "my-awesome-plugin"
    assert plugin.status.value in ["inactive", "active"]

@pytest.mark.asyncio
async def test_plugin_activation(plugin):
    """Test plugin activation"""
    result = await plugin.activate()
    assert result == True
    assert plugin.status.value == "active"

@pytest.mark.asyncio
async def test_hook_execution(plugin):
    """Test hook execution"""
    context = {
        "project_config": {
            "name": "test_project"
        }
    }
    
    result = await plugin._pre_project_creation(context)
    
    assert "metadata" in result["project_config"]
    assert result["project_config"]["metadata"]["awesome_plugin_version"] == "1.0.0"

@pytest.mark.asyncio
async def test_api_handlers(plugin):
    """Test API handlers"""
    # Test status endpoint
    status = await plugin._api_get_status({})
    assert "name" in status
    assert "version" in status
    
    # Test configure endpoint
    request = {
        "config": {
            "api_key": "new_key",
            "endpoint_url": "https://new-api.example.com"
        }
    }
    
    result = await plugin._api_configure(request)
    assert result["status"] == "success"
```

## 📦 Plugin Distribution

### Publishing to Plugin Registry

```bash
# Build plugin package
python setup.py sdist bdist_wheel

# Upload to Aetherforge Plugin Registry
aetherforge-cli plugin publish my-awesome-plugin-1.0.0.tar.gz

# Install from registry
aetherforge-cli plugin install my-awesome-plugin
```

### Plugin Marketplace

```yaml
# .aetherforge/plugin-marketplace.yml
name: my-awesome-plugin
category: development-tools
tags: [automation, integration, productivity]
screenshots:
  - assets/screenshot1.png
  - assets/screenshot2.png
demo_video: https://youtube.com/watch?v=demo
pricing:
  model: free  # free, paid, freemium
  price: 0
support:
  documentation: docs/README.md
  issues: https://github.com/yourusername/my-awesome-plugin/issues
  email: <EMAIL>
```

## 🔗 Next Steps

- **[Integration Guide](integration-guide.md)** - Integrate with external systems
- **[Component Adapters](component-adapters.md)** - Service integration patterns
- **[API Extensions](api-extensions.md)** - Extend the REST API
- **[Custom Agents](custom-agents.md)** - Create specialized agents

## 📖 Resources

- [Plugin Examples Repository](https://github.com/aetherforge/plugin-examples)
- [Plugin Development Kit](https://github.com/aetherforge/plugin-sdk)
- [Community Plugins](https://marketplace.aetherforge.dev)
