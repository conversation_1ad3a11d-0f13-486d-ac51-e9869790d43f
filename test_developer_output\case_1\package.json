{"name": "e-commerce-platform", "version": "1.0.0", "description": "Generated by Aetherforge - E-commerce Platform", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix", "dev": "nodemon src/server.ts"}, "dependencies": {"react": "^18.0", "react-dom": "^18.0", "express": "^4.0", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0"}, "devDependencies": {"typescript": "^5.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "jest": "^29.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5"}, "keywords": ["aetherforge", "generated", "event_driven"], "author": "Aetherforge Developer Agent", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}