# API Documentation

## E-commerce Platform API

### Overview

This document describes the REST API for E-commerce Platform.

**Base URL:** `http://localhost:3000/api`

**Authentication:** <PERSON><PERSON> (JWT)

### Authentication

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "name": "<PERSON>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### Error Responses

All API endpoints return errors in the following format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  }
}
```

### Rate Limiting

API requests are limited to 100 requests per 15 minutes per IP address.

### Support

For API support, contact <EMAIL>
