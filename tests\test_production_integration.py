"""
Production Integration Tests for Aetherforge
Comprehensive tests for production deployment readiness
"""

import pytest
import asyncio
import json
import tempfile
import shutil
import requests
import time
import subprocess
import os
import docker
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from component_adapters import ComponentManager
except ImportError:
    ComponentManager = None

try:
    from config_manager import ConfigurationManager, AetherforgeConfig
except ImportError:
    ConfigurationManager = None
    AetherforgeConfig = None

try:
    from orchestrator import OrchestratorConfig, ProjectRequest, app
except ImportError:
    OrchestratorConfig = None
    ProjectRequest = None
    app = None

class TestProductionIntegration:
    """Test production deployment integration"""
    
    @pytest.fixture(scope="session")
    def docker_client(self):
        """Docker client for container management"""
        try:
            client = docker.from_env()
            yield client
        except Exception as e:
            pytest.skip(f"Docker not available: {e}")
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def production_config(self, temp_dir):
        """Create production configuration"""
        config = AetherforgeConfig(
            environment="production",
            debug=False,
            log_level="info",
            enable_monitoring=True,
            max_concurrent_projects=10,
            project_timeout_minutes=60
        )
        
        config_file = Path(temp_dir) / "production_config.json"
        config_manager = ConfigurationManager(str(config_file))
        config_manager.config = config
        config_manager.save_configuration()
        
        return config_manager
    
    def test_docker_compose_validation(self):
        """Test Docker Compose configuration validation"""
        compose_files = [
            "docker-compose.yml",
            "docker-compose.prod.yml"
        ]
        
        for compose_file in compose_files:
            if Path(compose_file).exists():
                # Validate compose file syntax
                result = subprocess.run([
                    "docker-compose", "-f", compose_file, "config"
                ], capture_output=True, text=True)
                
                assert result.returncode == 0, f"Invalid {compose_file}: {result.stderr}"
    
    def test_dockerfile_validation(self):
        """Test Dockerfile validation"""
        dockerfiles = [
            "Dockerfile.orchestrator",
            "Dockerfile.test"
        ]
        
        for dockerfile in dockerfiles:
            if Path(dockerfile).exists():
                # Basic syntax validation
                content = Path(dockerfile).read_text()
                assert "FROM" in content
                assert "WORKDIR" in content
                assert "COPY" in content or "ADD" in content
    
    @pytest.mark.asyncio
    async def test_service_health_endpoints(self):
        """Test service health check endpoints"""
        # Test orchestrator health endpoint
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                assert "status" in health_data
                assert "timestamp" in health_data
                assert "services" in health_data
        except requests.exceptions.ConnectionError:
            pytest.skip("Orchestrator service not running")
    
    @pytest.mark.asyncio
    async def test_component_service_connectivity(self):
        """Test connectivity to all component services"""
        services = {
            "archon": "http://localhost:8100/health",
            "mcp": "http://localhost:8051/health", 
            "pheromind": "http://localhost:8502/health",
            "bmad": "http://localhost:8503/health"
        }
        
        for service_name, health_url in services.items():
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {service_name} service is healthy")
                else:
                    print(f"⚠️ {service_name} service returned status {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"❌ {service_name} service is not reachable")
    
    def test_environment_variables_validation(self, production_config):
        """Test production environment variables"""
        env_config = production_config.get_environment_config()
        
        # Required production environment variables
        required_vars = [
            "ORCHESTRATOR_URL",
            "ARCHON_URL", 
            "MCP_URL",
            "PHEROMIND_URL",
            "BMAD_URL",
            "PROJECTS_DIR",
            "LOG_LEVEL",
            "AETHERFORGE_ENV"
        ]
        
        for var in required_vars:
            assert var in env_config, f"Missing required environment variable: {var}"
            assert env_config[var] is not None, f"Environment variable {var} is None"
    
    @pytest.mark.asyncio
    async def test_database_connectivity(self):
        """Test database connectivity"""
        try:
            import asyncpg
            
            # Test PostgreSQL connection
            conn_string = os.getenv("DATABASE_URL", "postgresql://aetherforge:password@localhost:5432/aetherforge")
            
            try:
                conn = await asyncpg.connect(conn_string)
                await conn.execute("SELECT 1")
                await conn.close()
                print("✅ PostgreSQL connection successful")
            except Exception as e:
                print(f"❌ PostgreSQL connection failed: {e}")
                
        except ImportError:
            pytest.skip("asyncpg not available for database testing")
    
    @pytest.mark.asyncio
    async def test_redis_connectivity(self):
        """Test Redis connectivity"""
        try:
            import redis.asyncio as redis
            
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            
            try:
                r = redis.from_url(redis_url)
                await r.ping()
                await r.close()
                print("✅ Redis connection successful")
            except Exception as e:
                print(f"❌ Redis connection failed: {e}")
                
        except ImportError:
            pytest.skip("redis not available for testing")
    
    def test_ssl_certificate_validation(self):
        """Test SSL certificate configuration"""
        ssl_dir = Path("nginx/ssl")
        
        if ssl_dir.exists():
            cert_files = list(ssl_dir.glob("*.crt")) + list(ssl_dir.glob("*.pem"))
            key_files = list(ssl_dir.glob("*.key"))
            
            if cert_files and key_files:
                print("✅ SSL certificates found")
                
                # Basic certificate validation
                for cert_file in cert_files:
                    content = cert_file.read_text()
                    assert "BEGIN CERTIFICATE" in content
                    assert "END CERTIFICATE" in content
            else:
                print("⚠️ No SSL certificates found - using HTTP only")
    
    @pytest.mark.asyncio
    async def test_monitoring_stack_health(self):
        """Test monitoring stack health"""
        monitoring_services = {
            "prometheus": "http://localhost:9090/-/healthy",
            "grafana": "http://localhost:3001/api/health"
        }
        
        for service_name, health_url in monitoring_services.items():
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {service_name} monitoring service is healthy")
                else:
                    print(f"⚠️ {service_name} monitoring service returned status {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"❌ {service_name} monitoring service is not reachable")
    
    @pytest.mark.asyncio
    async def test_load_balancer_configuration(self):
        """Test load balancer configuration"""
        nginx_config = Path("nginx/nginx.conf")
        
        if nginx_config.exists():
            config_content = nginx_config.read_text()
            
            # Check for essential nginx configuration
            assert "upstream" in config_content
            assert "proxy_pass" in config_content
            assert "server" in config_content
            
            print("✅ Nginx configuration validated")
        else:
            print("⚠️ Nginx configuration not found")
    
    def test_backup_and_recovery_scripts(self):
        """Test backup and recovery script availability"""
        script_files = [
            "scripts/backup.sh",
            "scripts/restore.sh",
            "scripts/health_check.sh"
        ]
        
        for script_file in script_files:
            script_path = Path(script_file)
            if script_path.exists():
                # Check if script is executable
                assert os.access(script_path, os.X_OK), f"Script {script_file} is not executable"
                print(f"✅ {script_file} found and executable")
            else:
                print(f"⚠️ {script_file} not found")
    
    @pytest.mark.asyncio
    async def test_production_performance_limits(self, production_config):
        """Test production performance configuration"""
        config = production_config.config
        
        # Verify production-appropriate limits
        assert config.max_concurrent_projects >= 5, "Production should handle at least 5 concurrent projects"
        assert config.project_timeout_minutes >= 30, "Production timeout should be at least 30 minutes"
        assert config.log_level in ["info", "warning", "error"], "Production should use appropriate log level"
        assert config.debug is False, "Debug should be disabled in production"
    
    @pytest.mark.asyncio
    async def test_security_configuration(self):
        """Test security configuration"""
        # Test for security headers in nginx config
        nginx_config = Path("nginx/nginx.conf")
        
        if nginx_config.exists():
            config_content = nginx_config.read_text()
            
            security_headers = [
                "X-Content-Type-Options",
                "X-Frame-Options", 
                "X-XSS-Protection",
                "Strict-Transport-Security"
            ]
            
            for header in security_headers:
                if header in config_content:
                    print(f"✅ Security header {header} configured")
                else:
                    print(f"⚠️ Security header {header} not found")
    
    @pytest.mark.asyncio
    async def test_container_resource_limits(self, docker_client):
        """Test container resource limits"""
        try:
            containers = docker_client.containers.list()
            
            for container in containers:
                if "aetherforge" in container.name or any(service in container.name for service in ["orchestrator", "archon", "pheromind"]):
                    # Check if container has resource limits
                    stats = container.stats(stream=False)
                    
                    if "memory" in stats:
                        memory_limit = stats["memory"]["limit"]
                        if memory_limit < 2**63 - 1:  # Not unlimited
                            print(f"✅ {container.name} has memory limit: {memory_limit // (1024**3)}GB")
                        else:
                            print(f"⚠️ {container.name} has no memory limit")
                            
        except Exception as e:
            print(f"❌ Could not check container resource limits: {e}")
    
    @pytest.mark.asyncio
    async def test_log_aggregation_setup(self):
        """Test log aggregation configuration"""
        log_dirs = [
            "logs",
            "nginx/logs",
            "/var/log/aetherforge"
        ]
        
        for log_dir in log_dirs:
            log_path = Path(log_dir)
            if log_path.exists():
                print(f"✅ Log directory {log_dir} exists")
                
                # Check if directory is writable
                if os.access(log_path, os.W_OK):
                    print(f"✅ Log directory {log_dir} is writable")
                else:
                    print(f"⚠️ Log directory {log_dir} is not writable")
            else:
                print(f"⚠️ Log directory {log_dir} does not exist")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
