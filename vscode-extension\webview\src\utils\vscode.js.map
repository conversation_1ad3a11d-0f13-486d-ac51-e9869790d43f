{"version": 3, "file": "vscode.js", "sourceRoot": "", "sources": ["vscode.ts"], "names": [], "mappings": ";;;AAQA,MAAM,gBAAgB;IAMpB;QAJQ,oBAAe,GAAqC,IAAI,GAAG,EAAE,CAAC;QAC9D,oBAAe,GAAqC,IAAI,GAAG,EAAE,CAAC;QAC9D,mBAAc,GAAG,CAAC,CAAC;QAGzB,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;QACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,oBAAoB;QAC1B,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAC3C,MAAM,OAAO,GAAmB,KAAK,CAAC,IAAI,CAAC;YAE3C,IAAI,OAAO,CAAC,SAAS,EAAE;gBACrB,+BAA+B;gBAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC5D,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;iBAChD;aACF;iBAAM;gBACL,yBAAyB;gBACzB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1D,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACvB;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAe,EAAE,IAAU;QACrC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAU,OAAe,EAAE,IAAU;QACpD,MAAM,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAE/B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,EAAE;gBACnD,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,IAAI,YAAY,EAAE,KAAK,EAAE;oBACvB,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;iBACvC;qBAAM;oBACL,OAAO,CAAC,YAAY,CAAC,CAAC;iBACvB;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,OAAe,EAAE,OAA4B;QACrD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE3C,8BAA8B;QAC9B,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAU;QACjB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAY;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACjD,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAE7C,0CAA0C;AAC7B,QAAA,WAAW,GAAG;IACzB;;OAEG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,GAAG,OAAiB,EAAE,EAAE;QAClD,cAAM,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,SAAS,EAAE,CAAC,OAAe,EAAE,GAAG,OAAiB,EAAE,EAAE;QACnD,cAAM,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,WAAW,EAAE,CAAC,OAAe,EAAE,GAAG,OAAiB,EAAE,EAAE;QACrD,cAAM,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,YAAY,EAAE,CAAC,GAAW,EAAE,EAAE;QAC5B,cAAM,CAAC,WAAW,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,eAAe,EAAE,CAAC,IAAY,EAAE,EAAE;QAChC,cAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,KAAK,EAAE,OAAe,EAAE,QAAiB,EAAE,EAAE;QACrD,OAAO,cAAM,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,KAAK,EAAE,OAAa,EAAE,EAAE;QACtC,OAAO,cAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,mBAAmB,EAAE,KAAK,IAAI,EAAE;QAC9B,OAAO,cAAM,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE;QAClD,cAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,UAAU,EAAE,CAAC,IAAY,EAAE,SAAS,GAAG,KAAK,EAAE,EAAE;QAC9C,cAAM,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,GAAG,EAAE;QACtB,cAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,gBAAgB,EAAE,KAAK,EAAE,OAAgB,EAAE,EAAE;QAC3C,OAAO,cAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,mBAAmB,EAAE,CAAC,OAAe,EAAE,KAAU,EAAE,EAAE;QACnD,cAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;CACF,CAAC;AAEF,6BAA6B;AACtB,MAAM,SAAS,GAAG,GAAG,EAAE;IAC5B,OAAO,EAAE,MAAM,EAAN,cAAM,EAAE,GAAG,mBAAW,EAAE,CAAC;AACpC,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF,kCAAkC;AAC3B,MAAM,gBAAgB,GAAG,CAAC,OAAe,EAAE,OAA4B,EAAE,EAAE;IAChF,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,WAAW,GAAG,cAAM,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,OAAO,WAAW,CAAC;IACrB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC;AALW,QAAA,gBAAgB,oBAK3B;AAEF,kCAAkC;AAC3B,MAAM,cAAc,GAAG,CAAI,YAAe,EAAE,EAAE;IACnD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAI,GAAG,EAAE;QAC/C,MAAM,MAAM,GAAG,cAAM,CAAC,QAAQ,EAAE,CAAC;QACjC,OAAO,MAAM,IAAI,YAAY,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,eAAK,CAAC,WAAW,CAAC,CAAC,OAAmB,EAAE,EAAE;QAC5D,QAAQ,CAAC,IAAI,CAAC,EAAE;YACd,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC;YACzC,cAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC1B,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,KAAK,EAAE,WAAW,CAAU,CAAC;AACvC,CAAC,CAAC;AAfW,QAAA,cAAc,kBAezB;AAEF,yBAAyB;AACzB,iCAA0B;AAE1B,oCAAoC;AACpC,MAAa,oBAAqB,SAAQ,eAAK,CAAC,SAG/C;IACC,YAAY,KAAU;QACpB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,wBAAwB,CAAC,KAAY;QAC1C,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACnC,CAAC;IAED,iBAAiB,CAAC,KAAY,EAAE,SAAc;QAC5C,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAClD,cAAM,CAAC,WAAW,CAAC,cAAc,EAAE;YACjC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,OAAO,eAAK,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,EAC/D,eAAK,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,sBAAsB,CAAC,EACvD,eAAK,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,EACzD,eAAK,CAAC,aAAa,CAAC,QAAQ,EAAE;gBAC5B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;aAClD,EAAE,WAAW,CAAC,CAChB,CAAC;SACH;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;CACF;AAnCD,oDAmCC"}