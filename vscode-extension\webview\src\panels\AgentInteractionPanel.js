"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const framer_motion_1 = require("framer-motion");
const lucide_react_1 = require("lucide-react");
const react_hot_toast_1 = require("react-hot-toast");
const date_fns_1 = require("date-fns");
const store_1 = require("@/store");
const vscode_1 = require("@/utils/vscode");
const Button_1 = require("@/components/common/Button");
const Card_1 = require("@/components/common/Card");
const Input_1 = require("@/components/common/Input");
const AgentInteractionPanel = () => {
    const { agents, selectedAgent, messages, tasks, isLoading, error, connectionStatus, loadAgents, selectAgent, sendMessage, executeTask, loadMessages, loadTasks, updateAgentStatus, addMessage, updateTask } = (0, store_1.useAgentInteractionStore)();
    const [messageInput, setMessageInput] = (0, react_1.useState)('');
    const [taskInput, setTaskInput] = (0, react_1.useState)('');
    const [showTaskDialog, setShowTaskDialog] = (0, react_1.useState)(false);
    const [searchQuery, setSearchQuery] = (0, react_1.useState)('');
    const [filterStatus, setFilterStatus] = (0, react_1.useState)('all');
    const messagesEndRef = (0, react_1.useRef)(null);
    const messageInputRef = (0, react_1.useRef)(null);
    // Auto-scroll to bottom of messages
    (0, react_1.useEffect)(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);
    // Load initial data
    (0, react_1.useEffect)(() => {
        loadAgents();
        loadTasks();
    }, [loadAgents, loadTasks]);
    // Handle real-time updates
    (0, vscode_1.useVSCodeMessage)('agentUpdate', (data) => {
        updateAgentStatus(data.agentId, data);
    });
    (0, vscode_1.useVSCodeMessage)('newMessage', (data) => {
        addMessage(data);
    });
    (0, vscode_1.useVSCodeMessage)('taskUpdate', (data) => {
        updateTask(data.taskId, data);
    });
    const handleSendMessage = async () => {
        if (!messageInput.trim() || !selectedAgent)
            return;
        try {
            await sendMessage(messageInput);
            setMessageInput('');
            messageInputRef.current?.focus();
        }
        catch (error) {
            react_hot_toast_1.default.error('Failed to send message');
        }
    };
    const handleExecuteTask = async () => {
        if (!taskInput.trim() || !selectedAgent)
            return;
        try {
            await executeTask({
                title: taskInput,
                description: taskInput,
                agentId: selectedAgent.id,
                status: 'pending',
                priority: 'normal',
                progress: 0,
                estimatedDuration: 0,
                dependencies: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
            setTaskInput('');
            setShowTaskDialog(false);
            react_hot_toast_1.default.success('Task assigned successfully');
        }
        catch (error) {
            react_hot_toast_1.default.error('Failed to assign task');
        }
    };
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };
    const filteredAgents = agents.filter(agent => {
        const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            agent.role.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesFilter = filterStatus === 'all' || agent.status === filterStatus;
        return matchesSearch && matchesFilter;
    });
    const getStatusColor = (status) => {
        switch (status) {
            case 'idle': return 'text-green-600 bg-green-100';
            case 'busy': return 'text-yellow-600 bg-yellow-100';
            case 'working': return 'text-blue-600 bg-blue-100';
            case 'error': return 'text-red-600 bg-red-100';
            case 'offline': return 'text-gray-600 bg-gray-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };
    if (error) {
        return (<div className="p-6">
        <Card_1.default variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Connection Error</h3>
            <p className="mt-1">{error}</p>
            <Button_1.default variant="outline" size="sm" className="mt-3" onClick={loadAgents}>
              Retry Connection
            </Button_1.default>
          </div>
        </Card_1.default>
      </div>);
    }
    return (<vscode_1.WebviewErrorBoundary>
      <div className="h-screen flex bg-gray-50">
        <react_hot_toast_1.Toaster position="top-right"/>
        
        {/* Agent Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <lucide_react_1.Bot className="w-5 h-5 mr-2 text-blue-600"/>
                Agents
              </h2>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
            connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'}`}>
                {connectionStatus}
              </div>
            </div>
            
            {/* Search and Filter */}
            <div className="space-y-2">
              <Input_1.default placeholder="Search agents..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} icon={<lucide_react_1.Search className="w-4 h-4"/>} fullWidth/>
              
              <select value={filterStatus} onChange={(e) => setFilterStatus(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option value="all">All Status</option>
                <option value="idle">Idle</option>
                <option value="busy">Busy</option>
                <option value="working">Working</option>
                <option value="error">Error</option>
                <option value="offline">Offline</option>
              </select>
            </div>
          </div>
          
          {/* Agent List */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (<div className="p-4 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading agents...</p>
              </div>) : filteredAgents.length === 0 ? (<div className="p-4 text-center text-gray-500">
                No agents found
              </div>) : (<div className="p-2 space-y-2">
                {filteredAgents.map((agent) => (<AgentCard key={agent.id} agent={agent} isSelected={selectedAgent?.id === agent.id} onClick={() => selectAgent(agent)}/>))}
              </div>)}
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedAgent ? (<>
              {/* Chat Header */}
              <div className="bg-white border-b border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <lucide_react_1.Bot className="w-5 h-5 text-blue-600"/>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{selectedAgent.name}</h3>
                      <p className="text-sm text-gray-500">{selectedAgent.role}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button_1.default variant="outline" size="sm" onClick={() => setShowTaskDialog(true)} icon={<lucide_react_1.Play className="w-4 h-4"/>}>
                      Assign Task
                    </Button_1.default>
                    
                    <Button_1.default variant="ghost" size="sm" icon={<lucide_react_1.MoreVertical className="w-4 h-4"/>}/>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (<MessageBubble key={message.id} message={message}/>))}
                <div ref={messagesEndRef}/>
              </div>

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 p-4">
                <div className="flex items-end space-x-2">
                  <div className="flex-1">
                    <Input_1.default ref={messageInputRef} placeholder="Type your message..." value={messageInput} onChange={(e) => setMessageInput(e.target.value)} onKeyPress={handleKeyPress} fullWidth/>
                  </div>
                  <Button_1.default onClick={handleSendMessage} disabled={!messageInput.trim()} icon={<lucide_react_1.Send className="w-4 h-4"/>}>
                    Send
                  </Button_1.default>
                </div>
              </div>
            </>) : (<div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <lucide_react_1.Bot className="w-16 h-16 text-gray-400 mx-auto mb-4"/>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Select an Agent
                </h3>
                <p className="text-gray-500">
                  Choose an agent from the sidebar to start chatting
                </p>
              </div>
            </div>)}
        </div>

        {/* Task Assignment Dialog */}
        <framer_motion_1.AnimatePresence>
          {showTaskDialog && (<TaskAssignmentDialog onClose={() => setShowTaskDialog(false)} onSubmit={handleExecuteTask} taskInput={taskInput} setTaskInput={setTaskInput} agent={selectedAgent}/>)}
        </framer_motion_1.AnimatePresence>
      </div>
    </vscode_1.WebviewErrorBoundary>);
};
// Agent Card Component
const AgentCard = ({ agent, isSelected, onClick }) => (<framer_motion_1.motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className={`
      p-3 rounded-lg border cursor-pointer transition-all
      ${isSelected
        ? 'bg-blue-50 border-blue-200 shadow-sm'
        : 'bg-white border-gray-200 hover:bg-gray-50'}
    `} onClick={onClick}>
    <div className="flex items-start justify-between">
      <div className="flex items-start space-x-3">
        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <lucide_react_1.Bot className="w-4 h-4 text-blue-600"/>
        </div>

        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate">
            {agent.name}
          </div>
          <div className="text-sm text-gray-500 truncate">
            {agent.role}
          </div>
          {agent.currentTask && (<div className="text-xs text-blue-600 mt-1 truncate">
              {agent.currentTask}
            </div>)}
        </div>
      </div>

      <div className="flex flex-col items-end space-y-1">
        <span className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${getStatusColor(agent.status)}
        `}>
          {agent.status}
        </span>

        {agent.performance && (<div className="text-xs text-gray-500">
            {Math.round(agent.performance.efficiency * 100)}% eff
          </div>)}
      </div>
    </div>

    {agent.performance && (<div className="mt-3 space-y-2">
        <div className="flex justify-between text-xs text-gray-500">
          <span>Tasks: {agent.performance.tasksCompleted}</span>
          <span>Success: {Math.round(agent.performance.successRate * 100)}%</span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-1">
          <div className="bg-blue-500 h-1 rounded-full transition-all duration-300" style={{ width: `${agent.performance.efficiency * 100}%` }}/>
        </div>
      </div>)}

    <div className="mt-2 text-xs text-gray-400">
      Last active: {(0, date_fns_1.formatDistanceToNow)(new Date(agent.lastActivity), { addSuffix: true })}
    </div>
  </framer_motion_1.motion.div>);
// Message Bubble Component
const MessageBubble = ({ message }) => {
    const isUser = message.type === 'user';
    return (<framer_motion_1.motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`flex items-start space-x-2 max-w-[70%] ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
        <div className={`
          w-8 h-8 rounded-full flex items-center justify-center
          ${isUser ? 'bg-blue-600' : 'bg-gray-200'}
        `}>
          {isUser ? (<lucide_react_1.User className="w-4 h-4 text-white"/>) : (<lucide_react_1.Bot className="w-4 h-4 text-gray-600"/>)}
        </div>

        <div className={`
          px-4 py-2 rounded-lg
          ${isUser
            ? 'bg-blue-600 text-white'
            : 'bg-white border border-gray-200 text-gray-900'}
        `}>
          <div className="text-sm whitespace-pre-wrap">
            {message.content}
          </div>

          <div className={`
            text-xs mt-1
            ${isUser ? 'text-blue-100' : 'text-gray-500'}
          `}>
            {(0, date_fns_1.formatDistanceToNow)(new Date(message.timestamp), { addSuffix: true })}
          </div>
        </div>
      </div>
    </framer_motion_1.motion.div>);
};
// Task Assignment Dialog Component
const TaskAssignmentDialog = ({ onClose, onSubmit, taskInput, setTaskInput, agent }) => (<framer_motion_1.motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={onClose}>
    <framer_motion_1.motion.div initial={{ scale: 0.9, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: 0.9, opacity: 0 }} className="bg-white rounded-lg max-w-md w-full" onClick={(e) => e.stopPropagation()}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Assign Task to {agent?.name}
          </h3>
          <Button_1.default variant="ghost" onClick={onClose}>×</Button_1.default>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Task Description
            </label>
            <textarea value={taskInput} onChange={(e) => setTaskInput(e.target.value)} placeholder="Describe the task you want the agent to perform..." rows={4} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"/>
          </div>

          <div className="flex justify-end space-x-3">
            <Button_1.default variant="outline" onClick={onClose}>
              Cancel
            </Button_1.default>
            <Button_1.default onClick={onSubmit} disabled={!taskInput.trim()} icon={<lucide_react_1.Play className="w-4 h-4"/>}>
              Assign Task
            </Button_1.default>
          </div>
        </div>
      </div>
    </framer_motion_1.motion.div>
  </framer_motion_1.motion.div>);
function getStatusColor(status) {
    switch (status) {
        case 'idle': return 'text-green-600 bg-green-100';
        case 'busy': return 'text-yellow-600 bg-yellow-100';
        case 'working': return 'text-blue-600 bg-blue-100';
        case 'error': return 'text-red-600 bg-red-100';
        case 'offline': return 'text-gray-600 bg-gray-100';
        default: return 'text-gray-600 bg-gray-100';
    }
}
exports.default = AgentInteractionPanel;
//# sourceMappingURL=AgentInteractionPanel.js.map