import * as vscode from 'vscode';
import * as path from 'path';
import { WorkspaceManager } from './workspace-manager';
import { ProjectTemplateManager, ProjectConfig, ProjectType } from './project-template-manager';

// Re-export types for external use
export { ProjectConfig, ProjectType } from './project-template-manager';

export interface FileSystemOperation {
  id: string;
  type: 'create' | 'modify' | 'delete' | 'move' | 'copy' | 'batch';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  error?: string;
  details: any;
  timestamp: Date;
}

export interface SearchOptions {
  pattern: string;
  includePattern?: string;
  excludePattern?: string;
  caseSensitive?: boolean;
  wholeWord?: boolean;
  useRegex?: boolean;
  maxResults?: number;
}

export interface SearchResult {
  file: string;
  line: number;
  column: number;
  text: string;
  match: string;
}

export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  created: Date;
  modified: Date;
  isDirectory: boolean;
  isReadonly: boolean;
  encoding?: string;
}

export class FileSystemService {
  private workspaceManager: WorkspaceManager;
  private templateManager: ProjectTemplateManager;
  private operations: Map<string, FileSystemOperation> = new Map();
  private operationCounter = 0;

  constructor(context: vscode.ExtensionContext) {
    this.workspaceManager = new WorkspaceManager(context);
    this.templateManager = new ProjectTemplateManager(this.workspaceManager);
  }

  /**
   * Get workspace information
   */
  getWorkspaceInfo() {
    return this.workspaceManager.getWorkspaceInfo();
  }

  /**
   * Create a new project from template
   */
  async createProject(config: ProjectConfig, templateId?: string): Promise<string> {
    const operationId = this.createOperation('create', {
      type: 'project',
      config,
      templateId
    });

    try {
      this.updateOperation(operationId, { status: 'running', progress: 10 });

      if (templateId) {
        await this.templateManager.createProjectFromTemplate(templateId, config);
      } else {
        // Create basic project structure
        await this.createBasicProject(config);
      }

      this.updateOperation(operationId, { status: 'completed', progress: 100 });
      return operationId;
    } catch (error) {
      this.updateOperation(operationId, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Create basic project structure without template
   */
  private async createBasicProject(config: ProjectConfig): Promise<void> {
    const projectPath = config.name;

    // Create project directory
    await this.workspaceManager.createDirectory(projectPath);

    // Create basic files
    const readme = `# ${config.name}\n\n${config.description || 'A new project created with Aetherforge'}\n`;
    await this.workspaceManager.createFile(path.join(projectPath, 'README.md'), readme);

    const gitignore = `node_modules/\n.env\n.DS_Store\n*.log\n`;
    await this.workspaceManager.createFile(path.join(projectPath, '.gitignore'), gitignore);

    // Create project metadata
    const metadata = {
      projectId: `proj_${Date.now()}`,
      name: config.name,
      description: config.description,
      type: config.type,
      createdAt: new Date().toISOString(),
      version: config.version || '1.0.0',
      aetherforge: {
        version: '1.0.0',
        status: 'initialized'
      }
    };

    await this.workspaceManager.createFile(
      path.join(projectPath, '.aetherforge.json'),
      JSON.stringify(metadata, null, 2)
    );
  }

  /**
   * Create multiple files at once
   */
  async createFiles(files: Array<{ path: string; content: string }>): Promise<string> {
    const operationId = this.createOperation('batch', { type: 'create_files', files });

    try {
      this.updateOperation(operationId, { status: 'running', progress: 0 });

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        await this.workspaceManager.createFile(file.path, file.content);
        
        const progress = Math.round(((i + 1) / files.length) * 100);
        this.updateOperation(operationId, { progress });
      }

      this.updateOperation(operationId, { status: 'completed', progress: 100 });
      return operationId;
    } catch (error) {
      this.updateOperation(operationId, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Search for text in files
   */
  async searchInFiles(options: SearchOptions): Promise<SearchResult[]> {
    const workspaceInfo = this.workspaceManager.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const results: SearchResult[] = [];
    const searchPattern = options.useRegex 
      ? new RegExp(options.pattern, options.caseSensitive ? 'g' : 'gi')
      : options.pattern;

    // Use VS Code's built-in search API
    const searchResults = await vscode.workspace.findFiles(
      options.includePattern || '**/*',
      options.excludePattern || null,
      options.maxResults || 1000
    );

    // For now, return empty results as this is a simplified implementation
    // In a full implementation, you would read each file and search for the pattern
    for (const uri of searchResults) {
      const relativePath = this.workspaceManager.getRelativePath(uri.fsPath) || uri.fsPath;

      try {
        const content = await this.workspaceManager.readFile(relativePath);
        const lines = content.split('\n');

        lines.forEach((line, index) => {
          if (line.toLowerCase().includes(options.pattern.toLowerCase())) {
            results.push({
              file: relativePath,
              line: index + 1,
              column: line.indexOf(options.pattern) + 1,
              text: line,
              match: options.pattern
            });
          }
        });
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  /**
   * Get file information
   */
  async getFileInfo(relativePath: string): Promise<FileInfo> {
    const stats = await this.workspaceManager.getFileStats(relativePath);
    const fullPath = this.workspaceManager.getAbsolutePath(relativePath);
    
    if (!fullPath) {
      throw new Error('Cannot resolve file path');
    }

    return {
      path: relativePath,
      name: path.basename(relativePath),
      extension: path.extname(relativePath),
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      isDirectory: stats.isDirectory(),
      isReadonly: !stats.mode || !(stats.mode & 0o200)
    };
  }

  /**
   * List files in directory with filtering
   */
  async listFiles(
    relativePath: string = '', 
    recursive: boolean = false,
    filter?: (file: string) => boolean
  ): Promise<FileInfo[]> {
    const files = await this.workspaceManager.listDirectory(relativePath);
    const fileInfos: FileInfo[] = [];

    for (const file of files) {
      const filePath = path.join(relativePath, file);
      
      if (filter && !filter(file)) {
        continue;
      }

      try {
        const info = await this.getFileInfo(filePath);
        fileInfos.push(info);

        if (recursive && info.isDirectory) {
          const subFiles = await this.listFiles(filePath, true, filter);
          fileInfos.push(...subFiles);
        }
      } catch (error) {
        // Skip files that can't be accessed
        console.warn(`Cannot access file: ${filePath}`, error);
      }
    }

    return fileInfos;
  }

  /**
   * Watch files for changes
   */
  watchFiles(
    pattern: string,
    callbacks: {
      onCreate?: (uri: vscode.Uri) => void;
      onChange?: (uri: vscode.Uri) => void;
      onDelete?: (uri: vscode.Uri) => void;
    }
  ): vscode.Disposable {
    const watcherId = `watcher_${Date.now()}`;
    
    const watcher = this.workspaceManager.setupFileWatcher(
      watcherId,
      { pattern },
      callbacks
    );

    return {
      dispose: () => {
        this.workspaceManager.removeFileWatcher(watcherId);
      }
    };
  }

  /**
   * Get available project templates
   */
  getProjectTemplates() {
    return this.templateManager.getTemplates();
  }

  /**
   * Backup files before modification
   */
  async backupFiles(files: string[]): Promise<string> {
    const operationId = this.createOperation('backup', { files });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `.aetherforge/backups/${timestamp}`;

    try {
      this.updateOperation(operationId, { status: 'running', progress: 0 });

      await this.workspaceManager.createDirectory(backupDir);

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const backupPath = path.join(backupDir, file);
        
        // Ensure backup directory structure exists
        await this.workspaceManager.createDirectory(path.dirname(backupPath));
        
        // Copy file to backup location
        await this.workspaceManager.copyFile(file, backupPath);
        
        const progress = Math.round(((i + 1) / files.length) * 100);
        this.updateOperation(operationId, { progress });
      }

      this.updateOperation(operationId, { status: 'completed', progress: 100 });
      return backupDir;
    } catch (error) {
      this.updateOperation(operationId, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Get operation status
   */
  getOperation(id: string): FileSystemOperation | undefined {
    return this.operations.get(id);
  }

  /**
   * Get all operations
   */
  getAllOperations(): FileSystemOperation[] {
    return Array.from(this.operations.values());
  }

  /**
   * Create new operation
   */
  private createOperation(type: string, details: any): string {
    const id = `op_${++this.operationCounter}_${Date.now()}`;
    const operation: FileSystemOperation = {
      id,
      type: type as any,
      status: 'pending',
      progress: 0,
      details,
      timestamp: new Date()
    };

    this.operations.set(id, operation);
    return id;
  }

  /**
   * Update operation status
   */
  private updateOperation(id: string, updates: Partial<FileSystemOperation>): void {
    const operation = this.operations.get(id);
    if (operation) {
      Object.assign(operation, updates);
      this.operations.set(id, operation);
    }
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.workspaceManager.dispose();
  }
}
