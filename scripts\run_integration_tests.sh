#!/bin/bash

# Aetherforge Integration Test Runner
# Comprehensive integration testing for production readiness

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_ENV="${TEST_ENV:-test}"
COMPOSE_FILE="docker-compose.test.yml"
TEST_RESULTS_DIR="$PROJECT_ROOT/test_results"
COVERAGE_THRESHOLD=80

# Test configuration
PARALLEL_TESTS="${PARALLEL_TESTS:-4}"
TEST_TIMEOUT="${TEST_TIMEOUT:-1800}"  # 30 minutes
RETRY_COUNT=3

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")
            echo -e "${RED}❌ [${timestamp}] ${message}${NC}" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ [${timestamp}] ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ [${timestamp}] ${message}${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ [${timestamp}] ${message}${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    cleanup_test_environment
    exit 1
}

# Trap cleanup on exit
trap cleanup_test_environment EXIT

# Setup test environment
setup_test_environment() {
    log "INFO" "Setting up test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Create test Docker Compose file if it doesn't exist
    if [[ ! -f "$PROJECT_ROOT/$COMPOSE_FILE" ]]; then
        log "INFO" "Creating test Docker Compose configuration..."
        create_test_compose_file
    fi
    
    # Set test environment variables
    export AETHERFORGE_ENV="test"
    export PROJECTS_DIR="$PROJECT_ROOT/test_projects"
    export PHEROMONE_FILE="$PROJECT_ROOT/test_pheromones.json"
    export POSTGRES_DB="aetherforge_test"
    export REDIS_DB="1"
    
    # Clean up any existing test containers
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" down --remove-orphans --volumes 2>/dev/null || true
    
    # Start test infrastructure
    log "INFO" "Starting test infrastructure..."
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d postgres redis
    
    # Wait for infrastructure to be ready
    wait_for_service "postgres" "5432"
    wait_for_service "redis" "6379"
    
    log "SUCCESS" "Test environment setup completed"
}

# Create test Docker Compose file
create_test_compose_file() {
    cat > "$PROJECT_ROOT/$COMPOSE_FILE" << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=aetherforge_test
      - POSTGRES_USER=aetherforge
      - POSTGRES_PASSWORD=test_password
    ports:
      - "5433:5432"
    volumes:
      - postgres-test-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aetherforge"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis-test-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=test
      - DATABASE_URL=****************************************************/aetherforge_test
      - REDIS_URL=redis://redis:6379/1
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - test-results:/app/test_results
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    working_dir: /app

volumes:
  postgres-test-data:
  redis-test-data:
  test-results:
EOF
}

# Wait for service to be ready
wait_for_service() {
    local service=$1
    local port=$2
    local timeout=${3:-60}
    
    log "INFO" "Waiting for $service to be ready on port $port..."
    
    local count=0
    while ! nc -z localhost "$port" && [[ $count -lt $timeout ]]; do
        sleep 1
        ((count++))
    done
    
    if [[ $count -ge $timeout ]]; then
        error_exit "$service failed to start within $timeout seconds"
    fi
    
    log "SUCCESS" "$service is ready"
}

# Run unit tests
run_unit_tests() {
    log "INFO" "Running unit tests..."
    
    local test_cmd="python -m pytest tests/ -v --tb=short --junitxml=test_results/unit_tests.xml --cov=src --cov-report=xml:test_results/coverage.xml --cov-report=html:test_results/htmlcov"
    
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" run --rm test-runner $test_cmd; then
        log "SUCCESS" "Unit tests passed"
        return 0
    else
        log "ERROR" "Unit tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    log "INFO" "Running integration tests..."
    
    # Start application services for integration tests
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    sleep 30
    
    local test_cmd="python -m pytest tests/test_*integration*.py -v --tb=short --junitxml=test_results/integration_tests.xml"
    
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" run --rm test-runner $test_cmd; then
        log "SUCCESS" "Integration tests passed"
        return 0
    else
        log "ERROR" "Integration tests failed"
        return 1
    fi
}

# Run production integration tests
run_production_tests() {
    log "INFO" "Running production integration tests..."
    
    local test_cmd="python -m pytest tests/test_production_integration.py -v --tb=short --junitxml=test_results/production_tests.xml"
    
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" run --rm test-runner $test_cmd; then
        log "SUCCESS" "Production integration tests passed"
        return 0
    else
        log "ERROR" "Production integration tests failed"
        return 1
    fi
}

# Run performance tests
run_performance_tests() {
    log "INFO" "Running performance tests..."
    
    local test_cmd="python -m pytest tests/test_performance.py -v --tb=short --junitxml=test_results/performance_tests.xml"
    
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" run --rm test-runner $test_cmd; then
        log "SUCCESS" "Performance tests passed"
        return 0
    else
        log "WARNING" "Performance tests failed (non-blocking)"
        return 0  # Performance tests are non-blocking
    fi
}

# Run security tests
run_security_tests() {
    log "INFO" "Running security tests..."
    
    # Check for common security issues
    local security_issues=0
    
    # Check for hardcoded secrets
    log "INFO" "Checking for hardcoded secrets..."
    if grep -r -i "password\|secret\|key" --include="*.py" --include="*.js" --include="*.yml" "$PROJECT_ROOT/src" | grep -v "test" | grep -E "(=|:)\s*['\"][^'\"]{8,}['\"]"; then
        log "WARNING" "Potential hardcoded secrets found"
        ((security_issues++))
    fi
    
    # Check for SQL injection vulnerabilities
    log "INFO" "Checking for SQL injection vulnerabilities..."
    if grep -r "execute.*%" --include="*.py" "$PROJECT_ROOT/src"; then
        log "WARNING" "Potential SQL injection vulnerabilities found"
        ((security_issues++))
    fi
    
    # Check for insecure configurations
    log "INFO" "Checking for insecure configurations..."
    if grep -r "debug.*=.*True" --include="*.py" --include="*.yml" "$PROJECT_ROOT"; then
        log "WARNING" "Debug mode enabled in production files"
        ((security_issues++))
    fi
    
    if [[ $security_issues -eq 0 ]]; then
        log "SUCCESS" "Security tests passed"
        return 0
    else
        log "WARNING" "Security tests found $security_issues potential issues"
        return 0  # Security tests are non-blocking for now
    fi
}

# Check code coverage
check_coverage() {
    log "INFO" "Checking code coverage..."
    
    local coverage_file="$TEST_RESULTS_DIR/coverage.xml"
    
    if [[ -f "$coverage_file" ]]; then
        # Extract coverage percentage (simplified)
        local coverage_percent
        coverage_percent=$(grep -o 'line-rate="[0-9.]*"' "$coverage_file" | head -1 | grep -o '[0-9.]*' | awk '{print int($1*100)}')
        
        if [[ $coverage_percent -ge $COVERAGE_THRESHOLD ]]; then
            log "SUCCESS" "Code coverage: ${coverage_percent}% (threshold: ${COVERAGE_THRESHOLD}%)"
            return 0
        else
            log "WARNING" "Code coverage: ${coverage_percent}% (below threshold: ${COVERAGE_THRESHOLD}%)"
            return 1
        fi
    else
        log "WARNING" "Coverage report not found"
        return 1
    fi
}

# Generate test report
generate_test_report() {
    log "INFO" "Generating test report..."
    
    local report_file="$TEST_RESULTS_DIR/test_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Aetherforge Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Aetherforge Integration Test Report</h1>
        <p>Generated: $(date)</p>
        <p>Environment: $TEST_ENV</p>
        <p>Host: $(hostname)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="metric">
            <strong>Total Tests:</strong> $(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "tests=" {} \; | grep -o 'tests="[0-9]*"' | grep -o '[0-9]*' | awk '{sum+=$1} END {print sum}' || echo "0")
        </div>
        <div class="metric">
            <strong>Failures:</strong> $(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "failures=" {} \; | grep -o 'failures="[0-9]*"' | grep -o '[0-9]*' | awk '{sum+=$1} END {print sum}' || echo "0")
        </div>
        <div class="metric">
            <strong>Errors:</strong> $(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "errors=" {} \; | grep -o 'errors="[0-9]*"' | grep -o '[0-9]*' | awk '{sum+=$1} END {print sum}' || echo "0")
        </div>
    </div>
    
    <div class="section">
        <h2>Test Results</h2>
        <ul>
EOF
    
    # Add test results
    for test_file in "$TEST_RESULTS_DIR"/*.xml; do
        if [[ -f "$test_file" ]]; then
            local test_name=$(basename "$test_file" .xml)
            local failures=$(grep -o 'failures="[0-9]*"' "$test_file" | grep -o '[0-9]*' || echo "0")
            local errors=$(grep -o 'errors="[0-9]*"' "$test_file" | grep -o '[0-9]*' || echo "0")
            
            if [[ $failures -eq 0 && $errors -eq 0 ]]; then
                echo "            <li class=\"success\">✅ $test_name: PASSED</li>" >> "$report_file"
            else
                echo "            <li class=\"error\">❌ $test_name: FAILED (failures: $failures, errors: $errors)</li>" >> "$report_file"
            fi
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>Coverage Report</h2>
        <p><a href="htmlcov/index.html">View detailed coverage report</a></p>
    </div>
</body>
</html>
EOF
    
    log "SUCCESS" "Test report generated: $report_file"
}

# Cleanup test environment
cleanup_test_environment() {
    log "INFO" "Cleaning up test environment..."
    
    # Stop and remove test containers
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" down --remove-orphans --volumes 2>/dev/null || true
    
    # Clean up test files
    rm -rf "$PROJECT_ROOT/test_projects" 2>/dev/null || true
    rm -f "$PROJECT_ROOT/test_pheromones.json" 2>/dev/null || true
    
    log "SUCCESS" "Test environment cleanup completed"
}

# Main test execution
main() {
    local test_type=${1:-"all"}
    local exit_code=0
    
    log "INFO" "Starting Aetherforge integration tests (type: $test_type)..."
    
    # Setup test environment
    setup_test_environment
    
    case $test_type in
        "unit")
            run_unit_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "production")
            run_production_tests || exit_code=1
            ;;
        "performance")
            run_performance_tests || exit_code=1
            ;;
        "security")
            run_security_tests || exit_code=1
            ;;
        "all")
            run_unit_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_production_tests || exit_code=1
            run_performance_tests || exit_code=1
            run_security_tests || exit_code=1
            check_coverage || exit_code=1
            ;;
        *)
            error_exit "Unknown test type: $test_type"
            ;;
    esac
    
    # Generate test report
    generate_test_report
    
    if [[ $exit_code -eq 0 ]]; then
        log "SUCCESS" "All tests completed successfully!"
    else
        log "ERROR" "Some tests failed. Check the test report for details."
    fi
    
    return $exit_code
}

# Show usage
show_usage() {
    cat << EOF
Aetherforge Integration Test Runner

Usage: $0 [TEST_TYPE]

Test Types:
    all             Run all tests (default)
    unit            Unit tests only
    integration     Integration tests only
    production      Production readiness tests
    performance     Performance tests
    security        Security tests

Environment Variables:
    TEST_ENV            Test environment name (default: test)
    PARALLEL_TESTS      Number of parallel test processes (default: 4)
    TEST_TIMEOUT        Test timeout in seconds (default: 1800)
    COVERAGE_THRESHOLD  Minimum coverage percentage (default: 80)

Examples:
    $0              # Run all tests
    $0 unit         # Run unit tests only
    $0 production   # Run production readiness tests

EOF
}

# Make script executable
chmod +x "$0"

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case ${1:-} in
        "-h"|"--help"|"help")
            show_usage
            ;;
        *)
            main "$@"
            ;;
    esac
fi
