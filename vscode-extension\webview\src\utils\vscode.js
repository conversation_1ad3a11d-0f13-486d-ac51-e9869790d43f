"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebviewErrorBoundary = exports.useVSCodeState = exports.useVSCodeMessage = exports.useVSCode = exports.vsCodeUtils = exports.vscode = void 0;
class VSCodeAPIWrapper {
    constructor() {
        this.messageHandlers = new Map();
        this.requestHandlers = new Map();
        this.requestCounter = 0;
        this.api = window.acquireVsCodeApi();
        this.setupMessageListener();
    }
    setupMessageListener() {
        window.addEventListener('message', (event) => {
            const message = event.data;
            if (message.requestId) {
                // Handle response to a request
                const handler = this.requestHandlers.get(message.requestId);
                if (handler) {
                    handler(message.data);
                    this.requestHandlers.delete(message.requestId);
                }
            }
            else {
                // Handle regular message
                const handler = this.messageHandlers.get(message.command);
                if (handler) {
                    handler(message.data);
                }
            }
        });
    }
    /**
     * Send a message to the extension
     */
    postMessage(command, data) {
        this.api.postMessage({ command, data });
    }
    /**
     * Send a request and wait for response
     */
    async sendRequest(command, data) {
        const requestId = `req_${++this.requestCounter}`;
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.requestHandlers.delete(requestId);
                reject(new Error(`Request timeout: ${command}`));
            }, 30000); // 30 second timeout
            this.requestHandlers.set(requestId, (responseData) => {
                clearTimeout(timeout);
                if (responseData?.error) {
                    reject(new Error(responseData.error));
                }
                else {
                    resolve(responseData);
                }
            });
            this.api.postMessage({ command, data, requestId });
        });
    }
    /**
     * Register a message handler
     */
    onMessage(command, handler) {
        this.messageHandlers.set(command, handler);
        // Return unsubscribe function
        return () => {
            this.messageHandlers.delete(command);
        };
    }
    /**
     * Get stored state
     */
    getState() {
        return this.api.getState();
    }
    /**
     * Store state
     */
    setState(state) {
        this.api.setState(state);
    }
    /**
     * Update state partially
     */
    updateState(updates) {
        const currentState = this.getState() || {};
        this.setState({ ...currentState, ...updates });
    }
}
// Create singleton instance
exports.vscode = new VSCodeAPIWrapper();
// Utility functions for common operations
exports.vsCodeUtils = {
    /**
     * Show information message
     */
    showInfo: (message, ...actions) => {
        exports.vscode.postMessage('showInfo', { message, actions });
    },
    /**
     * Show error message
     */
    showError: (message, ...actions) => {
        exports.vscode.postMessage('showError', { message, actions });
    },
    /**
     * Show warning message
     */
    showWarning: (message, ...actions) => {
        exports.vscode.postMessage('showWarning', { message, actions });
    },
    /**
     * Open external URL
     */
    openExternal: (url) => {
        exports.vscode.postMessage('openExternal', { url });
    },
    /**
     * Copy to clipboard
     */
    copyToClipboard: (text) => {
        exports.vscode.postMessage('copyToClipboard', { text });
    },
    /**
     * Save file
     */
    saveFile: async (content, filename) => {
        return exports.vscode.sendRequest('saveFile', { content, filename });
    },
    /**
     * Open file dialog
     */
    openFileDialog: async (options) => {
        return exports.vscode.sendRequest('openFileDialog', options);
    },
    /**
     * Get workspace folders
     */
    getWorkspaceFolders: async () => {
        return exports.vscode.sendRequest('getWorkspaceFolders');
    },
    /**
     * Execute VS Code command
     */
    executeCommand: (command, ...args) => {
        exports.vscode.postMessage('executeCommand', { command, args });
    },
    /**
     * Open folder in VS Code
     */
    openFolder: (path, newWindow = false) => {
        exports.vscode.postMessage('openFolder', { path, newWindow });
    },
    /**
     * Refresh connection to orchestrator
     */
    refreshConnection: () => {
        exports.vscode.postMessage('refreshConnection');
    },
    /**
     * Get extension configuration
     */
    getConfiguration: async (section) => {
        return exports.vscode.sendRequest('getConfiguration', { section });
    },
    /**
     * Update extension configuration
     */
    updateConfiguration: (section, value) => {
        exports.vscode.postMessage('updateConfiguration', { section, value });
    }
};
// React hook for VS Code API
const useVSCode = () => {
    return { vscode: exports.vscode, ...exports.vsCodeUtils };
};
exports.useVSCode = useVSCode;
// React hook for message handling
const useVSCodeMessage = (command, handler) => {
    react_1.default.useEffect(() => {
        const unsubscribe = exports.vscode.onMessage(command, handler);
        return unsubscribe;
    }, [command, handler]);
};
exports.useVSCodeMessage = useVSCodeMessage;
// React hook for state management
const useVSCodeState = (initialState) => {
    const [state, setState] = react_1.default.useState(() => {
        const stored = exports.vscode.getState();
        return stored || initialState;
    });
    const updateState = react_1.default.useCallback((updates) => {
        setState(prev => {
            const newState = { ...prev, ...updates };
            exports.vscode.setState(newState);
            return newState;
        });
    }, []);
    return [state, updateState];
};
exports.useVSCodeState = useVSCodeState;
// Import React for hooks
const react_1 = require("react");
// Error boundary for webview panels
class WebviewErrorBoundary extends react_1.default.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('Webview error:', error, errorInfo);
        exports.vscode.postMessage('webviewError', {
            error: error.message,
            stack: error.stack,
            errorInfo
        });
    }
    render() {
        if (this.state.hasError) {
            return react_1.default.createElement('div', { className: 'error-boundary' }, react_1.default.createElement('h2', null, 'Something went wrong'), react_1.default.createElement('p', null, this.state.error?.message), react_1.default.createElement('button', {
                onClick: () => this.setState({ hasError: false })
            }, 'Try again'));
        }
        return this.props.children;
    }
}
exports.WebviewErrorBoundary = WebviewErrorBoundary;
//# sourceMappingURL=vscode.js.map