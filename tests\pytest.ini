[tool:pytest]
# Pytest configuration for TaoForge test suite

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version requirements
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --junitxml=test-results.xml

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    performance: Performance and load tests
    slow: Tests that take a long time to run
    api: Tests that require API access
    vscode: Tests for VS Code extension functionality
    pheromone: Tests for pheromone system
    workflow: Tests for workflow engine
    agent: Tests for agent executors
    orchestrator: Tests for orchestrator functionality
    config: Tests for configuration management
    mock: Tests that use extensive mocking
    real: Tests that use real external services (use sparingly)

# Test timeout (in seconds)
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    */node_modules/*
    */migrations/*
    */settings/*
    */config/*
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
title = TaoForge Test Coverage Report

[coverage:xml]
output = coverage.xml
