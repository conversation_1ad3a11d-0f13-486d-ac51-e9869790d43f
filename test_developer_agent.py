#!/usr/bin/env python3
"""
Test script for the Developer Agent
Demonstrates the comprehensive code generation capabilities
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from developer_agent import DeveloperAgent, CodeQuality, ProjectType
from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier, TechnologyChoice

async def test_developer_agent():
    """Test the developer agent with sample architecture output"""
    
    # Sample architecture data (from architect agent output)
    sample_architectures = [
        {
            "project_name": "E-commerce Platform",
            "architecture_pattern": "event_driven",
            "scalability_tier": "medium",
            "technology_stack": {
                "frontend": [
                    {
                        "name": "React",
                        "version": "18.x",
                        "category": "Frontend Framework",
                        "justification": "Mature ecosystem with excellent performance"
                    },
                    {
                        "name": "TypeScript",
                        "version": "5.x",
                        "category": "Programming Language",
                        "justification": "Type safety and better maintainability"
                    }
                ],
                "backend": [
                    {
                        "name": "Node.js",
                        "version": "18.x LTS",
                        "category": "Runtime",
                        "justification": "JavaScript runtime with excellent performance"
                    },
                    {
                        "name": "Express.js",
                        "version": "4.x",
                        "category": "Backend Framework",
                        "justification": "Minimal and flexible web framework"
                    }
                ],
                "testing": [
                    {
                        "name": "Jest",
                        "version": "29.x",
                        "category": "Unit Testing",
                        "justification": "Comprehensive testing framework"
                    }
                ]
            },
            "quality_attributes": {
                "performance": {
                    "response_time": "< 500ms for API calls",
                    "throughput": "1,000 requests/second"
                }
            },
            "security_architecture": {
                "authentication_strategy": {
                    "method": "JWT-based stateless authentication"
                }
            },
            "constraints": ["Budget constraints", "Timeline constraints"],
            "assumptions": ["Team has TypeScript experience"],
            "risks": []
        },
        {
            "project_name": "Task Management API",
            "architecture_pattern": "layered",
            "scalability_tier": "small",
            "technology_stack": {
                "backend": [
                    {
                        "name": "Node.js",
                        "version": "18.x LTS",
                        "category": "Runtime",
                        "justification": "JavaScript runtime"
                    },
                    {
                        "name": "Express.js",
                        "version": "4.x",
                        "category": "Backend Framework",
                        "justification": "Minimal web framework"
                    }
                ],
                "testing": [
                    {
                        "name": "Jest",
                        "version": "29.x",
                        "category": "Unit Testing",
                        "justification": "Testing framework"
                    }
                ]
            },
            "quality_attributes": {
                "performance": {
                    "response_time": "< 200ms for API calls"
                }
            },
            "security_architecture": {},
            "constraints": [],
            "assumptions": [],
            "risks": []
        }
    ]
    
    developer = DeveloperAgent()
    
    for i, arch_data in enumerate(sample_architectures, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {arch_data['project_name'].upper()}")
        print(f"{'='*60}")
        print(f"Architecture Pattern: {arch_data['architecture_pattern']}")
        print(f"Scalability Tier: {arch_data['scalability_tier']}")
        
        try:
            # Convert dict to SystemArchitecture object
            technology_stack = {}
            for category, techs in arch_data['technology_stack'].items():
                technology_stack[category] = [
                    TechnologyChoice(
                        name=tech['name'],
                        version=tech['version'],
                        category=tech['category'],
                        justification=tech['justification'],
                        alternatives=[],
                        pros=[],
                        cons=[],
                        learning_curve="medium",
                        community_support="excellent"
                    ) for tech in techs
                ]
            
            architecture = SystemArchitecture(
                project_name=arch_data['project_name'],
                architecture_pattern=ArchitecturePattern(arch_data['architecture_pattern']),
                scalability_tier=ScalabilityTier(arch_data['scalability_tier']),
                components=[],  # Simplified for testing
                data_architecture={},
                security_architecture=arch_data['security_architecture'],
                deployment_architecture={},
                integration_patterns=[],
                technology_stack=technology_stack,
                quality_attributes=arch_data['quality_attributes'],
                constraints=arch_data['constraints'],
                assumptions=arch_data['assumptions'],
                risks=arch_data['risks']
            )
            
            # Generate project code
            print("\n💻 Starting code generation...")
            output_path = Path(f"test_developer_output/case_{i}")
            project_structure = await developer.generate_project(
                architecture, 
                output_path, 
                CodeQuality.PRODUCTION
            )
            
            print(f"✅ Code generation complete!")
            print(f"   Project Type: {project_structure.type.value}")
            print(f"   Source Files: {len(project_structure.source_files)}")
            print(f"   Test Files: {len(project_structure.test_files)}")
            print(f"   Config Files: {len(project_structure.config_files)}")
            print(f"   Documentation Files: {len(project_structure.documentation_files)}")
            
            # Display generated files
            print(f"\n📁 Generated Files:")
            all_files = (project_structure.source_files + project_structure.test_files + 
                        project_structure.config_files + project_structure.documentation_files)
            
            for file in all_files[:10]:  # Show first 10 files
                print(f"   - {file.path} ({file.file_type})")
            
            if len(all_files) > 10:
                print(f"   ... and {len(all_files) - 10} more files")
            
            # Save project structure as JSON for integration testing
            structure_json_path = output_path / "project_structure.json"
            structure_json_path.parent.mkdir(parents=True, exist_ok=True)
            
            structure_dict = {
                "project_name": project_structure.name,
                "project_type": project_structure.type.value,
                "root_path": project_structure.root_path,
                "files": {
                    "source": [{"path": f.path, "language": f.language, "type": f.file_type} for f in project_structure.source_files],
                    "test": [{"path": f.path, "language": f.language, "type": f.file_type} for f in project_structure.test_files],
                    "config": [{"path": f.path, "language": f.language, "type": f.file_type} for f in project_structure.config_files],
                    "documentation": [{"path": f.path, "language": f.language, "type": f.file_type} for f in project_structure.documentation_files]
                },
                "dependencies": project_structure.dependencies,
                "scripts": project_structure.scripts,
                "environment_variables": project_structure.environment_variables
            }
            
            with open(structure_json_path, 'w') as f:
                json.dump(structure_dict, f, indent=2)
            
            print(f"💾 Project structure saved to: {structure_json_path}")
            
        except Exception as e:
            print(f"❌ Test case {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎉 All test cases completed!")
    print("📁 Check the test_developer_output/ directory for generated code")
    print(f"{'='*60}")

async def test_integration_with_architect():
    """Test integration between architect and developer agents"""
    print("\n🔗 Testing Architect-Developer Integration...")
    
    try:
        # Import architect agent
        from architect_agent import ArchitectAgent
        
        # Create a sample prompt
        prompt = "Create a REST API for a blog platform with posts, comments, and user authentication"
        
        print(f"📝 Generating architecture for: {prompt}")
        
        # Run architect agent (simplified input)
        architect = ArchitectAgent()
        
        # Create simplified analyst output for architect
        analyst_output = {
            "project_name": "Blog Platform API",
            "description": prompt,
            "functional_requirements": [
                {"title": "User Authentication", "priority": "High"},
                {"title": "Post Management", "priority": "High"},
                {"title": "Comment System", "priority": "Medium"}
            ],
            "user_stories": [
                {"title": "User Registration", "role": "user", "action": "register", "benefit": "access the platform"},
                {"title": "Create Post", "role": "author", "action": "create posts", "benefit": "share content"},
                {"title": "Add Comments", "role": "reader", "action": "comment on posts", "benefit": "engage with content"}
            ],
            "technical_preferences": {
                "backend": {"runtime": "Node.js", "framework": "Express.js"},
                "database": {"primary": "PostgreSQL"}
            },
            "success_metrics": ["API response times < 200ms", "99% uptime"]
        }
        
        # Design architecture
        architecture = await architect.design_architecture(analyst_output)
        
        print(f"✅ Architecture complete: {architecture.project_name}")
        print(f"   Pattern: {architecture.architecture_pattern.value}")
        print(f"   Components: {len(architecture.components)}")
        
        # Run developer agent
        developer = DeveloperAgent()
        output_path = Path("integration_test_output/developer")
        
        project_structure = await developer.generate_project(
            architecture, 
            output_path, 
            CodeQuality.PRODUCTION
        )
        
        print(f"✅ Code generation complete: {project_structure.name}")
        print(f"   Type: {project_structure.type.value}")
        print(f"   Files: {len(project_structure.source_files + project_structure.test_files + project_structure.config_files + project_structure.documentation_files)}")
        
        print(f"🎉 Integration test complete! Check {output_path} for generated code.")
        
    except ImportError:
        print("❌ Architect agent not available for integration test")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

def print_usage():
    """Print usage information"""
    print("""
💻 Developer Agent Test Suite

Usage:
    python test_developer_agent.py [command]

Commands:
    test        Run comprehensive code generation tests (default)
    integration Test integration with architect agent
    help        Show this help message

Examples:
    python test_developer_agent.py test
    python test_developer_agent.py integration

The developer agent will:
1. 💻 Parse architecture specifications
2. 🏗️ Generate project structure
3. 📝 Create source code files
4. 🧪 Generate comprehensive tests
5. ⚙️ Create configuration files
6. 📚 Generate documentation
7. ✅ Validate code quality

Generated files include:
- React/TypeScript frontend components
- Express.js/Node.js backend services
- Jest unit and integration tests
- Docker configuration
- Package.json with dependencies
- TypeScript configuration
- Comprehensive documentation
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "integration":
        await test_integration_with_architect()
    elif command == "test":
        await test_developer_agent()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
