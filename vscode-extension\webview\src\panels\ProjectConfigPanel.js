"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const framer_motion_1 = require("framer-motion");
const react_hook_form_1 = require("react-hook-form");
const lucide_react_1 = require("lucide-react");
const react_hot_toast_1 = require("react-hot-toast");
const store_1 = require("@/store");
const vscode_1 = require("@/utils/vscode");
const Button_1 = require("@/components/common/Button");
const Card_1 = require("@/components/common/Card");
const Input_1 = require("@/components/common/Input");
const ProjectConfigPanel = () => {
    const { config, templates, projectTypes, workflows, isLoading, error, updateConfig, resetConfig, loadTemplates, loadProjectTypes, loadWorkflows, validateConfig, createProject, previewProject } = (0, store_1.useProjectConfigStore)();
    const [activeTab, setActiveTab] = (0, react_1.useState)('basic');
    const [previewData, setPreviewData] = (0, react_1.useState)(null);
    const [showPreview, setShowPreview] = (0, react_1.useState)(false);
    const { control, handleSubmit, watch, setValue, formState: { errors } } = (0, react_hook_form_1.useForm)({
        defaultValues: config
    });
    const watchedValues = watch();
    // Update store when form values change
    (0, react_1.useEffect)(() => {
        updateConfig(watchedValues);
    }, [watchedValues, updateConfig]);
    // Load initial data
    (0, react_1.useEffect)(() => {
        loadProjectTypes();
        loadWorkflows();
        loadTemplates();
    }, [loadProjectTypes, loadWorkflows, loadTemplates]);
    // Handle messages from extension
    (0, vscode_1.useVSCodeMessage)('projectCreated', (data) => {
        react_hot_toast_1.default.success(`Project "${data.name}" created successfully!`);
    });
    (0, vscode_1.useVSCodeMessage)('projectPreview', (data) => {
        setPreviewData(data);
        setShowPreview(true);
    });
    (0, vscode_1.useVSCodeMessage)('error', (data) => {
        react_hot_toast_1.default.error(data.message || 'An error occurred');
    });
    const onSubmit = async (data) => {
        try {
            if (!validateConfig()) {
                react_hot_toast_1.default.error('Please fill in all required fields');
                return;
            }
            await createProject();
            react_hot_toast_1.default.success('Project creation started!');
        }
        catch (error) {
            react_hot_toast_1.default.error('Failed to create project');
        }
    };
    const handlePreview = async () => {
        try {
            const preview = await previewProject();
            setPreviewData(preview);
            setShowPreview(true);
        }
        catch (error) {
            react_hot_toast_1.default.error('Failed to generate preview');
        }
    };
    const handleLoadTemplate = (templateId) => {
        const template = templates.find(t => t.id === templateId);
        if (template) {
            updateConfig(template.config);
            Object.keys(template.config).forEach(key => {
                setValue(key, template.config[key]);
            });
            react_hot_toast_1.default.success(`Template "${template.name}" loaded`);
        }
    };
    const tabs = [
        { id: 'basic', label: 'Basic Info', icon: lucide_react_1.FileText },
        { id: 'features', label: 'Features', icon: lucide_react_1.Settings },
        { id: 'tech', label: 'Technology', icon: lucide_react_1.Code },
        { id: 'deployment', label: 'Deployment', icon: lucide_react_1.Cloud },
        { id: 'quality', label: 'Quality', icon: lucide_react_1.Shield }
    ];
    if (error) {
        return (<div className="p-6">
        <Card_1.default variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Error</h3>
            <p className="mt-1">{error}</p>
            <Button_1.default variant="outline" size="sm" className="mt-3" onClick={() => window.location.reload()} icon={<lucide_react_1.RefreshCw className="w-4 h-4"/>}>
              Retry
            </Button_1.default>
          </div>
        </Card_1.default>
      </div>);
    }
    return (<vscode_1.WebviewErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <react_hot_toast_1.Toaster position="top-right"/>
        
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <lucide_react_1.Rocket className="w-6 h-6 mr-2 text-blue-600"/>
                Create New Project
              </h1>
              <p className="text-gray-600 mt-1">
                Configure your project settings and let AI agents build it for you
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              {templates.length > 0 && (<Input_1.Select options={[
                { value: '', label: 'Load Template...' },
                ...templates.map(t => ({ value: t.id, label: t.name }))
            ]} onChange={(e) => e.target.value && handleLoadTemplate(e.target.value)} className="w-48"/>)}
              
              <Button_1.default variant="outline" onClick={handlePreview} icon={<lucide_react_1.Eye className="w-4 h-4"/>} disabled={isLoading}>
                Preview
              </Button_1.default>
              
              <Button_1.default variant="primary" onClick={handleSubmit(onSubmit)} loading={isLoading} icon={<lucide_react_1.Save className="w-4 h-4"/>}>
                Create Project
              </Button_1.default>
            </div>
          </div>
        </div>

        <div className="flex">
          {/* Sidebar Navigation */}
          <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
            <nav className="p-4 space-y-2">
              {tabs.map((tab) => {
            const Icon = tab.icon;
            return (<button key={tab.id} onClick={() => setActiveTab(tab.id)} className={`
                      w-full flex items-center px-3 py-2 text-left rounded-md transition-colors
                      ${activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-100'}
                    `}>
                    <Icon className="w-4 h-4 mr-3"/>
                    {tab.label}
                  </button>);
        })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl">
              <framer_motion_1.AnimatePresence mode="wait">
                <framer_motion_1.motion.div key={activeTab} initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.2 }}>
                  {activeTab === 'basic' && (<BasicInfoTab control={control} errors={errors} projectTypes={projectTypes} workflows={workflows}/>)}
                  
                  {activeTab === 'features' && (<FeaturesTab control={control}/>)}
                  
                  {activeTab === 'tech' && (<TechnologyTab control={control}/>)}
                  
                  {activeTab === 'deployment' && (<DeploymentTab control={control}/>)}
                  
                  {activeTab === 'quality' && (<QualityTab control={control}/>)}
                </framer_motion_1.motion.div>
              </framer_motion_1.AnimatePresence>
            </form>
          </div>
        </div>

        {/* Preview Modal */}
        <framer_motion_1.AnimatePresence>
          {showPreview && (<PreviewModal data={previewData} onClose={() => setShowPreview(false)}/>)}
        </framer_motion_1.AnimatePresence>
      </div>
    </vscode_1.WebviewErrorBoundary>);
};
// Basic Info Tab Component
const BasicInfoTab = ({ control, errors, projectTypes, workflows }) => (<Card_1.default title="Basic Information" className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <react_hook_form_1.Controller name="name" control={control} rules={{ required: 'Project name is required' }} render={({ field }) => (<Input_1.default {...field} label="Project Name" placeholder="my-awesome-project" error={errors.name?.message} fullWidth/>)}/>

      <react_hook_form_1.Controller name="type" control={control} rules={{ required: 'Project type is required' }} render={({ field }) => (<Input_1.Select {...field} label="Project Type" options={[
            { value: '', label: 'Select project type...' },
            ...projectTypes.map(type => ({
                value: type.id,
                label: type.name
            }))
        ]} error={errors.type?.message} fullWidth/>)}/>
    </div>

    <react_hook_form_1.Controller name="description" control={control} rules={{ required: 'Project description is required' }} render={({ field }) => (<Input_1.Textarea {...field} label="Project Description" placeholder="Describe your project in detail. Include features, requirements, and any specific needs..." rows={4} error={errors.description?.message} fullWidth/>)}/>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <react_hook_form_1.Controller name="workflow" control={control} render={({ field }) => (<Input_1.Select {...field} label="Workflow" options={[
            { value: 'default', label: 'Default' },
            { value: 'rapid', label: 'Rapid Development' },
            { value: 'thorough', label: 'Thorough Development' },
            { value: 'enterprise', label: 'Enterprise Grade' },
            ...workflows.map(w => ({ value: w.id, label: w.name }))
        ]} fullWidth/>)}/>

      <react_hook_form_1.Controller name="priority" control={control} render={({ field }) => (<Input_1.Select {...field} label="Priority" options={[
            { value: 'low', label: 'Low' },
            { value: 'normal', label: 'Normal' },
            { value: 'high', label: 'High' },
            { value: 'urgent', label: 'Urgent' }
        ]} fullWidth/>)}/>

      <react_hook_form_1.Controller name="agentBehavior" control={control} render={({ field }) => (<Input_1.Select {...field} label="Agent Behavior" options={[
            { value: 'conservative', label: 'Conservative' },
            { value: 'balanced', label: 'Balanced' },
            { value: 'aggressive', label: 'Aggressive' },
            { value: 'creative', label: 'Creative' },
            { value: 'production', label: 'Production' }
        ]} fullWidth/>)}/>
    </div>
  </Card_1.default>);
// Features Tab Component
const FeaturesTab = ({ control }) => (<div className="space-y-6">
    <Card_1.default title="Development Features">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[
        { key: 'enableParallelExecution', label: 'Parallel Execution', description: 'Enable parallel task execution for faster development' },
        { key: 'enableCodeReview', label: 'Code Review', description: 'Automated code review and quality checks' },
        { key: 'enableTesting', label: 'Testing', description: 'Comprehensive test suite generation' },
        { key: 'enableDocumentation', label: 'Documentation', description: 'Automatic documentation generation' },
        { key: 'enableCICD', label: 'CI/CD Pipeline', description: 'Continuous integration and deployment setup' },
        { key: 'enableDocker', label: 'Docker Support', description: 'Containerization with Docker' }
    ].map((feature) => (<react_hook_form_1.Controller key={feature.key} name={`features.${feature.key}`} control={control} render={({ field }) => (<label className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="checkbox" checked={field.value} onChange={field.onChange} className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
                <div>
                  <div className="font-medium text-gray-900">{feature.label}</div>
                  <div className="text-sm text-gray-500">{feature.description}</div>
                </div>
              </label>)}/>))}
      </div>
    </Card_1.default>

    <Card_1.default title="Advanced Features">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[
        { key: 'enableAIOptimization', label: 'AI Optimization', description: 'AI-powered code optimization and suggestions' },
        { key: 'enableSecurityScan', label: 'Security Scanning', description: 'Automated security vulnerability scanning' },
        { key: 'enablePerformanceOptimization', label: 'Performance Optimization', description: 'Performance monitoring and optimization' },
        { key: 'enableMonitoring', label: 'Monitoring', description: 'Application monitoring and alerting' },
        { key: 'enableAnalytics', label: 'Analytics', description: 'Usage analytics and reporting' },
        { key: 'enableDeployment', label: 'Auto Deployment', description: 'Automated deployment to cloud platforms' }
    ].map((feature) => (<react_hook_form_1.Controller key={feature.key} name={`features.${feature.key}`} control={control} render={({ field }) => (<label className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <input type="checkbox" checked={field.value} onChange={field.onChange} className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
                <div>
                  <div className="font-medium text-gray-900">{feature.label}</div>
                  <div className="text-sm text-gray-500">{feature.description}</div>
                </div>
              </label>)}/>))}
      </div>
    </Card_1.default>
  </div>);
// Technology Tab Component
const TechnologyTab = ({ control }) => {
    const techOptions = {
        programmingLanguages: [
            'JavaScript', 'TypeScript', 'Python', 'Java', 'C#', 'Go', 'Rust', 'PHP', 'Ruby', 'Swift', 'Kotlin'
        ],
        frameworks: [
            'React', 'Vue.js', 'Angular', 'Next.js', 'Express.js', 'FastAPI', 'Django', 'Spring Boot', 'ASP.NET', 'Laravel'
        ],
        databases: [
            'PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'SQLite', 'Elasticsearch', 'DynamoDB', 'Firebase'
        ],
        cloudProviders: [
            'AWS', 'Google Cloud', 'Azure', 'Vercel', 'Netlify', 'Heroku', 'DigitalOcean'
        ]
    };
    return (<div className="space-y-6">
      {Object.entries(techOptions).map(([category, options]) => (<Card_1.default key={category} title={category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}>
          <react_hook_form_1.Controller name={`technologies.${category}`} control={control} render={({ field }) => (<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {options.map((option) => (<label key={option} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                    <input type="checkbox" checked={field.value?.includes(option) || false} onChange={(e) => {
                        const current = field.value || [];
                        if (e.target.checked) {
                            field.onChange([...current, option]);
                        }
                        else {
                            field.onChange(current.filter((item) => item !== option));
                        }
                    }} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
                    <span className="text-sm font-medium text-gray-700">{option}</span>
                  </label>))}
              </div>)}/>
        </Card_1.default>))}
    </div>);
};
// Deployment Tab Component
const DeploymentTab = ({ control }) => (<div className="space-y-6">
    <Card_1.default title="Deployment Configuration">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <react_hook_form_1.Controller name="deployment.environment" control={control} render={({ field }) => (<Input_1.Select {...field} label="Environment" options={[
            { value: 'development', label: 'Development' },
            { value: 'staging', label: 'Staging' },
            { value: 'production', label: 'Production' }
        ]} fullWidth/>)}/>

        <react_hook_form_1.Controller name="deployment.scalability" control={control} render={({ field }) => (<Input_1.Select {...field} label="Scalability" options={[
            { value: 'single', label: 'Single Instance' },
            { value: 'horizontal', label: 'Horizontal Scaling' },
            { value: 'vertical', label: 'Vertical Scaling' },
            { value: 'auto', label: 'Auto Scaling' }
        ]} fullWidth/>)}/>
      </div>

      <react_hook_form_1.Controller name="deployment.targets" control={control} render={({ field }) => (<div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Deployment Targets</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {['Docker', 'Kubernetes', 'AWS Lambda', 'Vercel', 'Netlify', 'Heroku', 'Google Cloud Run', 'Azure Container Instances'].map((target) => (<label key={target} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                  <input type="checkbox" checked={field.value?.includes(target) || false} onChange={(e) => {
                const current = field.value || [];
                if (e.target.checked) {
                    field.onChange([...current, target]);
                }
                else {
                    field.onChange(current.filter((item) => item !== target));
                }
            }} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
                  <span className="text-sm font-medium text-gray-700">{target}</span>
                </label>))}
            </div>
          </div>)}/>

      <react_hook_form_1.Controller name="deployment.monitoring" control={control} render={({ field }) => (<label className="flex items-center space-x-3">
            <input type="checkbox" checked={field.value} onChange={field.onChange} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
            <div>
              <div className="font-medium text-gray-900">Enable Monitoring</div>
              <div className="text-sm text-gray-500">Set up monitoring and alerting for the deployed application</div>
            </div>
          </label>)}/>
    </Card_1.default>
  </div>);
// Quality Tab Component
const QualityTab = ({ control }) => (<div className="space-y-6">
    <Card_1.default title="Code Quality Settings">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <react_hook_form_1.Controller name="quality.codeQualityLevel" control={control} render={({ field }) => (<Input_1.Select {...field} label="Code Quality Level" options={[
            { value: 'basic', label: 'Basic' },
            { value: 'standard', label: 'Standard' },
            { value: 'high', label: 'High' },
            { value: 'enterprise', label: 'Enterprise' }
        ]} fullWidth/>)}/>

        <react_hook_form_1.Controller name="quality.securityLevel" control={control} render={({ field }) => (<Input_1.Select {...field} label="Security Level" options={[
            { value: 'basic', label: 'Basic' },
            { value: 'standard', label: 'Standard' },
            { value: 'high', label: 'High' },
            { value: 'enterprise', label: 'Enterprise' }
        ]} fullWidth/>)}/>
      </div>

      <react_hook_form_1.Controller name="quality.testCoverageTarget" control={control} render={({ field }) => (<div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test Coverage Target: {Math.round(field.value * 100)}%
            </label>
            <input type="range" min="0" max="1" step="0.1" value={field.value} onChange={(e) => field.onChange(parseFloat(e.target.value))} className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"/>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>)}/>

      <react_hook_form_1.Controller name="quality.documentationLevel" control={control} render={({ field }) => (<Input_1.Select {...field} label="Documentation Level" options={[
            { value: 'minimal', label: 'Minimal' },
            { value: 'standard', label: 'Standard' },
            { value: 'comprehensive', label: 'Comprehensive' }
        ]} fullWidth/>)}/>
    </Card_1.default>
  </div>);
// Preview Modal Component
const PreviewModal = ({ data, onClose }) => (<framer_motion_1.motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={onClose}>
    <framer_motion_1.motion.div initial={{ scale: 0.9, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: 0.9, opacity: 0 }} className="bg-white rounded-lg max-w-4xl max-h-[80vh] overflow-auto" onClick={(e) => e.stopPropagation()}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Project Preview</h2>
          <Button_1.default variant="ghost" onClick={onClose}>×</Button_1.default>
        </div>

        <div className="prose max-w-none">
          {data ? (<pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(data, null, 2)}
            </pre>) : (<div className="text-center py-8 text-gray-500">
              No preview data available
            </div>)}
        </div>
      </div>
    </framer_motion_1.motion.div>
  </framer_motion_1.motion.div>);
exports.default = ProjectConfigPanel;
//# sourceMappingURL=ProjectConfigPanel.js.map