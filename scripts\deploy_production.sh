#!/bin/bash

# Aetherforge Production Deployment Script
# Comprehensive production deployment with health checks and rollback capability

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-production}"
BACKUP_DIR="${BACKUP_DIR:-/opt/aetherforge/backups}"
LOG_FILE="${LOG_FILE:-/var/log/aetherforge/deployment.log}"
HEALTH_CHECK_TIMEOUT=300
ROLLBACK_ENABLED="${ROLLBACK_ENABLED:-true}"

# Deployment configuration
COMPOSE_FILE="docker-compose.prod.yml"
SERVICES=("orchestrator" "archon" "mcp-crawl4ai" "pheromind" "bmad" "postgres" "redis" "nginx" "prometheus" "grafana")
CRITICAL_SERVICES=("orchestrator" "postgres" "redis")

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
    
    case $level in
        "ERROR")
            echo -e "${RED}❌ ${message}${NC}" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ ${message}${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ ${message}${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    if [[ "$ROLLBACK_ENABLED" == "true" ]]; then
        log "INFO" "Initiating rollback..."
        rollback_deployment
    fi
    exit 1
}

# Trap errors
trap 'error_exit "Deployment failed at line $LINENO"' ERR

# Pre-deployment checks
pre_deployment_checks() {
    log "INFO" "Starting pre-deployment checks..."
    
    # Check if running as root or with sudo
    if [[ $EUID -eq 0 ]]; then
        log "WARNING" "Running as root. Consider using a dedicated user for production."
    fi
    
    # Check Docker and Docker Compose
    if ! command -v docker &> /dev/null; then
        error_exit "Docker is not installed"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose is not installed"
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error_exit "Docker daemon is not running"
    fi
    
    # Check compose file exists
    if [[ ! -f "$PROJECT_ROOT/$COMPOSE_FILE" ]]; then
        error_exit "Docker Compose file not found: $COMPOSE_FILE"
    fi
    
    # Validate compose file
    if ! docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" config &> /dev/null; then
        error_exit "Invalid Docker Compose configuration"
    fi
    
    # Check disk space (minimum 10GB)
    available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then  # 10GB in KB
        error_exit "Insufficient disk space. At least 10GB required."
    fi
    
    # Check memory (minimum 4GB)
    available_memory=$(free -m | awk 'NR==2{print $7}')
    if [[ $available_memory -lt 4096 ]]; then
        log "WARNING" "Less than 4GB memory available. Performance may be affected."
    fi
    
    # Check required environment variables
    required_vars=("POSTGRES_PASSWORD" "OPENAI_API_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Required environment variable $var is not set"
        fi
    done
    
    log "SUCCESS" "Pre-deployment checks completed"
}

# Create backup
create_backup() {
    log "INFO" "Creating backup before deployment..."
    
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_path="$BACKUP_DIR/aetherforge_backup_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup database
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log "INFO" "Backing up PostgreSQL database..."
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres pg_dumpall -U aetherforge > "$backup_path/database.sql"
    fi
    
    # Backup volumes
    log "INFO" "Backing up Docker volumes..."
    docker run --rm -v aetherforge_projects-data:/data -v "$backup_path":/backup alpine tar czf /backup/projects-data.tar.gz -C /data .
    docker run --rm -v aetherforge_pheromone-data:/data -v "$backup_path":/backup alpine tar czf /backup/pheromone-data.tar.gz -C /data .
    
    # Backup configuration files
    log "INFO" "Backing up configuration files..."
    cp -r "$PROJECT_ROOT/nginx" "$backup_path/" 2>/dev/null || true
    cp "$PROJECT_ROOT/.env" "$backup_path/" 2>/dev/null || true
    cp "$PROJECT_ROOT/$COMPOSE_FILE" "$backup_path/"
    
    echo "$backup_path" > "$BACKUP_DIR/latest_backup.txt"
    log "SUCCESS" "Backup created at $backup_path"
}

# Deploy services
deploy_services() {
    log "INFO" "Starting service deployment..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images
    log "INFO" "Pulling latest Docker images..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # Build custom images
    log "INFO" "Building custom images..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    # Stop existing services gracefully
    log "INFO" "Stopping existing services..."
    docker-compose -f "$COMPOSE_FILE" down --timeout 30
    
    # Start infrastructure services first
    log "INFO" "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" up -d postgres redis
    
    # Wait for infrastructure to be ready
    wait_for_service "postgres" "5432"
    wait_for_service "redis" "6379"
    
    # Start application services
    log "INFO" "Starting application services..."
    docker-compose -f "$COMPOSE_FILE" up -d orchestrator archon mcp-crawl4ai pheromind bmad
    
    # Start monitoring and proxy services
    log "INFO" "Starting monitoring and proxy services..."
    docker-compose -f "$COMPOSE_FILE" up -d prometheus grafana nginx
    
    log "SUCCESS" "All services started"
}

# Wait for service to be ready
wait_for_service() {
    local service=$1
    local port=$2
    local timeout=${3:-60}
    
    log "INFO" "Waiting for $service to be ready on port $port..."
    
    local count=0
    while ! nc -z localhost "$port" && [[ $count -lt $timeout ]]; do
        sleep 1
        ((count++))
    done
    
    if [[ $count -ge $timeout ]]; then
        error_exit "$service failed to start within $timeout seconds"
    fi
    
    log "SUCCESS" "$service is ready"
}

# Health checks
perform_health_checks() {
    log "INFO" "Performing health checks..."
    
    local health_check_start=$(date +%s)
    local all_healthy=true
    
    # Check critical services first
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! check_service_health "$service"; then
            all_healthy=false
            log "ERROR" "Critical service $service failed health check"
        fi
    done
    
    if [[ "$all_healthy" == "false" ]]; then
        error_exit "Critical services failed health checks"
    fi
    
    # Check all other services
    for service in "${SERVICES[@]}"; do
        if [[ ! " ${CRITICAL_SERVICES[@]} " =~ " ${service} " ]]; then
            if ! check_service_health "$service"; then
                log "WARNING" "Service $service failed health check but deployment will continue"
            fi
        fi
    done
    
    local health_check_end=$(date +%s)
    local health_check_duration=$((health_check_end - health_check_start))
    
    log "SUCCESS" "Health checks completed in ${health_check_duration}s"
}

# Check individual service health
check_service_health() {
    local service=$1
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        case $service in
            "orchestrator")
                if curl -sf http://localhost:8000/health &>/dev/null; then
                    log "SUCCESS" "$service health check passed"
                    return 0
                fi
                ;;
            "postgres")
                if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres pg_isready -U aetherforge &>/dev/null; then
                    log "SUCCESS" "$service health check passed"
                    return 0
                fi
                ;;
            "redis")
                if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T redis redis-cli ping | grep -q PONG; then
                    log "SUCCESS" "$service health check passed"
                    return 0
                fi
                ;;
            "nginx")
                if curl -sf http://localhost:80 &>/dev/null; then
                    log "SUCCESS" "$service health check passed"
                    return 0
                fi
                ;;
            *)
                # Generic container health check
                if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
                    log "SUCCESS" "$service health check passed"
                    return 0
                fi
                ;;
        esac
        
        sleep 5
        ((attempt++))
    done
    
    log "ERROR" "$service health check failed after $max_attempts attempts"
    return 1
}

# Rollback deployment
rollback_deployment() {
    log "WARNING" "Starting deployment rollback..."
    
    if [[ ! -f "$BACKUP_DIR/latest_backup.txt" ]]; then
        log "ERROR" "No backup found for rollback"
        return 1
    fi
    
    local backup_path=$(cat "$BACKUP_DIR/latest_backup.txt")
    
    if [[ ! -d "$backup_path" ]]; then
        log "ERROR" "Backup directory not found: $backup_path"
        return 1
    fi
    
    # Stop current services
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" down --timeout 30
    
    # Restore database
    if [[ -f "$backup_path/database.sql" ]]; then
        log "INFO" "Restoring database..."
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d postgres
        wait_for_service "postgres" "5432"
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres psql -U aetherforge < "$backup_path/database.sql"
    fi
    
    # Restore volumes
    if [[ -f "$backup_path/projects-data.tar.gz" ]]; then
        log "INFO" "Restoring project data..."
        docker run --rm -v aetherforge_projects-data:/data -v "$backup_path":/backup alpine tar xzf /backup/projects-data.tar.gz -C /data
    fi
    
    if [[ -f "$backup_path/pheromone-data.tar.gz" ]]; then
        log "INFO" "Restoring pheromone data..."
        docker run --rm -v aetherforge_pheromone-data:/data -v "$backup_path":/backup alpine tar xzf /backup/pheromone-data.tar.gz -C /data
    fi
    
    # Start services with previous configuration
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d
    
    log "SUCCESS" "Rollback completed"
}

# Post-deployment tasks
post_deployment_tasks() {
    log "INFO" "Performing post-deployment tasks..."
    
    # Update system monitoring
    if command -v systemctl &> /dev/null; then
        log "INFO" "Updating systemd services..."
        # Add any systemd service updates here
    fi
    
    # Clean up old Docker images
    log "INFO" "Cleaning up old Docker images..."
    docker image prune -f
    
    # Set up log rotation
    setup_log_rotation
    
    # Send deployment notification
    send_deployment_notification "success"
    
    log "SUCCESS" "Post-deployment tasks completed"
}

# Setup log rotation
setup_log_rotation() {
    log "INFO" "Setting up log rotation..."
    
    local logrotate_config="/etc/logrotate.d/aetherforge"
    
    if [[ -w "/etc/logrotate.d" ]]; then
        cat > "$logrotate_config" << EOF
/var/log/aetherforge/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 aetherforge aetherforge
    postrotate
        docker-compose -f $PROJECT_ROOT/$COMPOSE_FILE restart nginx
    endscript
}
EOF
        log "SUCCESS" "Log rotation configured"
    else
        log "WARNING" "Cannot configure log rotation - insufficient permissions"
    fi
}

# Send deployment notification
send_deployment_notification() {
    local status=$1
    local webhook_url="${DEPLOYMENT_WEBHOOK_URL:-}"
    
    if [[ -n "$webhook_url" ]]; then
        local message="Aetherforge deployment $status on $(hostname) at $(date)"
        curl -X POST -H "Content-Type: application/json" \
             -d "{\"text\":\"$message\"}" \
             "$webhook_url" &>/dev/null || true
    fi
}

# Display deployment summary
display_summary() {
    log "INFO" "Deployment Summary"
    echo "=================================="
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Deployment Time: $(date)"
    echo "Services Status:"
    
    for service in "${SERVICES[@]}"; do
        if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
            echo "  ✅ $service: Running"
        else
            echo "  ❌ $service: Not Running"
        fi
    done
    
    echo ""
    echo "Access Points:"
    echo "  🌐 Main API: http://localhost:8000"
    echo "  📊 Monitoring: http://localhost:3001"
    echo "  🔍 Metrics: http://localhost:9090"
    echo "  📋 Documentation: http://localhost:8000/docs"
    echo "=================================="
}

# Main deployment function
main() {
    log "INFO" "Starting Aetherforge production deployment..."
    
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Run deployment steps
    pre_deployment_checks
    create_backup
    deploy_services
    perform_health_checks
    post_deployment_tasks
    display_summary
    
    log "SUCCESS" "Aetherforge production deployment completed successfully!"
}

# Make script executable
chmod +x "$0"

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
