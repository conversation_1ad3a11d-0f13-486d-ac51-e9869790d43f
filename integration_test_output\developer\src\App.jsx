import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { BlogPlatformAPIProvider } from './context/BlogPlatformAPIContext';
import { <PERSON><PERSON>, Footer, LoadingSpinner, ErrorBoundary } from './components';
import { Home, Dashboard, NotFound } from './pages';
import './styles/App.css';



/**
 * Function description
 * @param {any} param - Parameter description
 * @returns {any} Return description
 */
const App = () => {
  return (
    <ErrorBoundary>
      <BlogPlatformAPIProvider>
        <Router>
          <div className="app">
            <Header />
            <main className="main-content">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </BlogPlatformAPIProvider>
    </ErrorBoundary>
  );
};

export default App;