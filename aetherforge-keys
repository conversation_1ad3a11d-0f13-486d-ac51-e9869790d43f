#!/usr/bin/env python3
"""
Aetherforge API Key Management CLI Entry Point
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from api_key_cli import main
    main()
except ImportError as e:
    print(f"❌ Error: Could not import API key CLI: {e}")
    print("Make sure you're running this from the Aetherforge project directory.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
