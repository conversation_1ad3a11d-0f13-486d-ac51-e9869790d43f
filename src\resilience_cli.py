#!/usr/bin/env python3
"""
CLI tool for testing and monitoring API resilience in Aetherforge
"""

import asyncio
import argparse
import json
import sys
from typing import Dict, Any
from pathlib import Path

try:
    from .api_resilience import get_resilience_layer, APIProvider
    from .api_manager import get_api_manager
except ImportError:
    from api_resilience import get_resilience_layer, APIProvider
    from api_manager import get_api_manager

def print_json(data: Dict[str, Any], indent: int = 2):
    """Pretty print JSON data"""
    print(json.dumps(data, indent=indent, default=str))

async def test_providers():
    """Test all configured API providers"""
    print("🔍 Testing all API providers...")
    resilience_layer = get_resilience_layer()
    
    results = await resilience_layer.test_all_providers()
    
    print("\n📊 Provider Test Results:")
    print("=" * 50)
    
    for provider, result in results.items():
        status_emoji = "✅" if result["status"] == "healthy" else "❌"
        print(f"{status_emoji} {provider.upper()}")
        
        if result["status"] == "healthy":
            print(f"   Model: {result['model']}")
            print(f"   Response Time: {result['response_time']:.2f}s")
            if result.get('response_preview'):
                print(f"   Preview: {result['response_preview']}...")
        else:
            print(f"   Error: {result['error']}")
        print()

async def show_resilience_stats():
    """Show comprehensive resilience statistics"""
    print("📈 API Resilience Statistics")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    stats = resilience_layer.get_resilience_stats()
    
    # Provider Status
    print("\n🔧 Provider Status:")
    for provider, status in stats["provider_status"].items():
        configured = "✅" if status["configured"] else "❌"
        can_request = "✅" if status["can_make_request"] else "⏳"
        print(f"  {provider}: {configured} Configured | {can_request} Available")
        print(f"    Model: {status['model']}")
        print(f"    Rate Limit Remaining: {status['rate_limit_remaining']}")
    
    # Quota Usage
    print("\n💰 Quota Usage:")
    for provider, usage in stats["quota_usage"].items():
        if usage["requests"] > 0:
            print(f"  {provider}:")
            print(f"    Requests: {usage['requests']}")
            print(f"    Tokens: {usage['tokens']}")
            print(f"    Cost: ${usage['cost']:.2f}")
            if usage["warnings"]:
                for warning in usage["warnings"]:
                    print(f"    ⚠️  {warning}")
    
    # Cache Stats
    print(f"\n💾 Cache: {stats['cache_stats']['entries']} entries")
    
    # Fallback Configuration
    print("\n🔄 Fallback Configuration:")
    config = stats["fallback_config"]
    print(f"  Provider Fallback: {'✅' if config['provider_fallback'] else '❌'}")
    print(f"  Model Fallback: {'✅' if config['model_fallback'] else '❌'}")
    print(f"  Degraded Service: {'✅' if config['degraded_service'] else '❌'}")
    print(f"  Cache Fallback: {'✅' if config['cache_fallback'] else '❌'}")
    
    # Recent Notifications
    print(f"\n🔔 Recent Notifications ({stats['notification_summary']['total']} total):")
    summary = stats["notification_summary"]
    print(f"  Errors: {summary['errors']} | Warnings: {summary['warnings']} | Info: {summary['info']}")
    
    recent = stats["recent_notifications"]
    if recent:
        print("\n  Latest notifications:")
        for notification in recent[-5:]:  # Show last 5
            severity_emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(notification["severity"], "📝")
            print(f"    {severity_emoji} [{notification['type']}] {notification['message']}")

async def test_resilience():
    """Test resilience features with simulated failures"""
    print("🧪 Testing API Resilience Features")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    
    # Test normal operation
    print("\n1. Testing normal operation...")
    test_messages = [{"role": "user", "content": "Hello, this is a test message."}]
    
    result = await resilience_layer.generate_text_resilient(test_messages, max_tokens=50)
    
    if result.success:
        print(f"✅ Success with {result.provider_used.value if result.provider_used else 'unknown'}")
        print(f"   Response time: {result.total_time:.2f}s")
        print(f"   Retries: {result.retries_attempted}")
        print(f"   Fallback used: {result.fallback_used}")
    else:
        print(f"❌ Failed: {result.error}")
    
    # Test with cache
    print("\n2. Testing cache functionality...")
    result2 = await resilience_layer.generate_text_resilient(test_messages, max_tokens=50)
    
    if result2.success and result2.fallback_used and not result2.provider_used:
        print("✅ Cache hit detected")
    else:
        print("ℹ️  No cache hit (may be expected)")
    
    print("\n3. Testing quota warnings...")
    # This would require actual quota limits to be meaningful
    print("ℹ️  Quota warnings are shown in stats when limits are approached")

async def show_notifications():
    """Show recent notifications"""
    print("🔔 Recent Notifications")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    notifications = resilience_layer.get_user_notifications(20)
    
    if not notifications:
        print("No recent notifications.")
        return
    
    for notification in notifications:
        severity_emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(notification["severity"], "📝")
        provider_info = f" ({notification['provider']})" if notification['provider'] else ""
        print(f"{severity_emoji} [{notification['type']}]{provider_info}")
        print(f"   {notification['message']}")
        print(f"   {notification['timestamp']}")
        print()

async def clear_notifications():
    """Clear all notifications"""
    resilience_layer = get_resilience_layer()
    resilience_layer.clear_notifications()
    print("✅ All notifications cleared.")

async def simulate_failure():
    """Simulate API failures to test fallback mechanisms"""
    print("⚠️  Simulating API Failures")
    print("=" * 50)
    print("This would require temporarily disabling providers or")
    print("using mock failures to test fallback behavior.")
    print("For now, check the logs and stats for real failure handling.")

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="Aetherforge API Resilience CLI")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Test providers command
    subparsers.add_parser("test", help="Test all API providers")
    
    # Stats command
    subparsers.add_parser("stats", help="Show resilience statistics")
    
    # Resilience test command
    subparsers.add_parser("resilience", help="Test resilience features")
    
    # Notifications commands
    subparsers.add_parser("notifications", help="Show recent notifications")
    subparsers.add_parser("clear-notifications", help="Clear all notifications")
    
    # Simulate failure command
    subparsers.add_parser("simulate", help="Simulate failures (for testing)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Run the appropriate async function
    if args.command == "test":
        asyncio.run(test_providers())
    elif args.command == "stats":
        asyncio.run(show_resilience_stats())
    elif args.command == "resilience":
        asyncio.run(test_resilience())
    elif args.command == "notifications":
        asyncio.run(show_notifications())
    elif args.command == "clear-notifications":
        asyncio.run(clear_notifications())
    elif args.command == "simulate":
        asyncio.run(simulate_failure())

if __name__ == "__main__":
    main()
