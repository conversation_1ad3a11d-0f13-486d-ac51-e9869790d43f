import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageCircle,
  Send,
  Bot,
  User,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  Settings,
  MoreVertical,
  Search,
  Filter,
  BarChart3
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';

import { useAgentInteractionStore } from '@/store';
import { Agent, ChatMessage, Task } from '@/types';
import { vscode, useVSCodeMessage, WebviewErrorBoundary } from '@/utils/vscode';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import Input from '@/components/common/Input';
import Progress, { CircularProgress } from '@/components/common/Progress';

const AgentInteractionPanel: React.FC = () => {
  const {
    agents,
    selectedAgent,
    messages,
    tasks,
    isLoading,
    error,
    connectionStatus,
    loadAgents,
    selectAgent,
    sendMessage,
    executeTask,
    loadMessages,
    loadTasks,
    updateAgentStatus,
    addMessage,
    updateTask
  } = useAgentInteractionStore();

  const [messageInput, setMessageInput] = useState('');
  const [taskInput, setTaskInput] = useState('');
  const [showTaskDialog, setShowTaskDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load initial data
  useEffect(() => {
    loadAgents();
    loadTasks();
  }, [loadAgents, loadTasks]);

  // Handle real-time updates
  useVSCodeMessage('agentUpdate', (data) => {
    updateAgentStatus(data.agentId, data);
  });

  useVSCodeMessage('newMessage', (data) => {
    addMessage(data);
  });

  useVSCodeMessage('taskUpdate', (data) => {
    updateTask(data.taskId, data);
  });

  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedAgent) return;

    try {
      await sendMessage(messageInput);
      setMessageInput('');
      messageInputRef.current?.focus();
    } catch (error) {
      toast.error('Failed to send message');
    }
  };

  const handleExecuteTask = async () => {
    if (!taskInput.trim() || !selectedAgent) return;

    try {
      await executeTask({
        title: taskInput,
        description: taskInput,
        agentId: selectedAgent.id,
        status: 'pending',
        priority: 'normal',
        progress: 0,
        estimatedDuration: 0,
        dependencies: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      setTaskInput('');
      setShowTaskDialog(false);
      toast.success('Task assigned successfully');
    } catch (error) {
      toast.error('Failed to assign task');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.role.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || agent.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'idle': return 'text-green-600 bg-green-100';
      case 'busy': return 'text-yellow-600 bg-yellow-100';
      case 'working': return 'text-blue-600 bg-blue-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'offline': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (error) {
    return (
      <div className="p-6">
        <Card variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Connection Error</h3>
            <p className="mt-1">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={loadAgents}
            >
              Retry Connection
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <WebviewErrorBoundary>
      <div className="h-screen flex bg-gray-50">
        <Toaster position="top-right" />
        
        {/* Agent Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Bot className="w-5 h-5 mr-2 text-blue-600" />
                Agents
              </h2>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
                connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {connectionStatus}
              </div>
            </div>
            
            {/* Search and Filter */}
            <div className="space-y-2">
              <Input
                placeholder="Search agents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search className="w-4 h-4" />}
                fullWidth
              />
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="idle">Idle</option>
                <option value="busy">Busy</option>
                <option value="working">Working</option>
                <option value="error">Error</option>
                <option value="offline">Offline</option>
              </select>
            </div>
          </div>
          
          {/* Agent List */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading agents...</p>
              </div>
            ) : filteredAgents.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No agents found
              </div>
            ) : (
              <div className="p-2 space-y-2">
                {filteredAgents.map((agent) => (
                  <AgentCard
                    key={agent.id}
                    agent={agent}
                    isSelected={selectedAgent?.id === agent.id}
                    onClick={() => selectAgent(agent)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex">
          {selectedAgent ? (
            <>
              {/* Chat Section */}
              <div className="flex-1 flex flex-col">
                {/* Chat Header */}
                <div className="bg-white border-b border-gray-200 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <Bot className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{selectedAgent.name}</h3>
                        <p className="text-sm text-gray-500">{selectedAgent.role}</p>
                        {selectedAgent.currentTask && (
                          <p className="text-xs text-blue-600">Currently: {selectedAgent.currentTask}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`
                        px-2 py-1 rounded-full text-xs font-medium
                        ${getStatusColor(selectedAgent.status)}
                      `}>
                        {selectedAgent.status}
                      </span>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowTaskDialog(true)}
                        icon={<Play className="w-4 h-4" />}
                      >
                        Assign Task
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        icon={<MoreVertical className="w-4 h-4" />}
                      />
                    </div>
                  </div>
                </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <MessageBubble key={message.id} message={message} />
                ))}
                <div ref={messagesEndRef} />
              </div>

                {/* Message Input */}
                <div className="bg-white border-t border-gray-200 p-4">
                  <div className="flex items-end space-x-2">
                    <div className="flex-1">
                      <Input
                        ref={messageInputRef}
                        placeholder="Type your message..."
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={handleKeyPress}
                        fullWidth
                      />
                    </div>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!messageInput.trim()}
                      icon={<Send className="w-4 h-4" />}
                    >
                      Send
                    </Button>
                  </div>
                </div>
              </div>

              {/* Agent Details Sidebar */}
              <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
                <AgentDetailsSidebar agent={selectedAgent} tasks={tasks} />
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Bot className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Select an Agent
                </h3>
                <p className="text-gray-500">
                  Choose an agent from the sidebar to start chatting
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Task Assignment Dialog */}
        <AnimatePresence>
          {showTaskDialog && (
            <TaskAssignmentDialog
              onClose={() => setShowTaskDialog(false)}
              onSubmit={handleExecuteTask}
              taskInput={taskInput}
              setTaskInput={setTaskInput}
              agent={selectedAgent}
            />
          )}
        </AnimatePresence>
      </div>
    </WebviewErrorBoundary>
  );
};

// Agent Card Component
const AgentCard: React.FC<{
  agent: Agent;
  isSelected: boolean;
  onClick: () => void;
}> = ({ agent, isSelected, onClick }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className={`
      p-3 rounded-lg border cursor-pointer transition-all
      ${isSelected
        ? 'bg-blue-50 border-blue-200 shadow-sm'
        : 'bg-white border-gray-200 hover:bg-gray-50'
      }
    `}
    onClick={onClick}
  >
    <div className="flex items-start justify-between">
      <div className="flex items-start space-x-3">
        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <Bot className="w-4 h-4 text-blue-600" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate">
            {agent.name}
          </div>
          <div className="text-sm text-gray-500 truncate">
            {agent.role}
          </div>
          {agent.currentTask && (
            <div className="text-xs text-blue-600 mt-1 truncate">
              {agent.currentTask}
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col items-end space-y-1">
        <span className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${getStatusColor(agent.status)}
        `}>
          {agent.status}
        </span>

        {agent.performance && (
          <div className="text-xs text-gray-500">
            {Math.round(agent.performance.efficiency * 100)}% eff
          </div>
        )}
      </div>
    </div>

    {agent.performance && (
      <div className="mt-3 space-y-2">
        <div className="flex justify-between text-xs text-gray-500">
          <span>Tasks: {agent.performance.tasksCompleted}</span>
          <span>Success: {Math.round(agent.performance.successRate * 100)}%</span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-1">
          <div
            className="bg-blue-500 h-1 rounded-full transition-all duration-300"
            style={{ width: `${agent.performance.efficiency * 100}%` }}
          />
        </div>
      </div>
    )}

    <div className="mt-2 text-xs text-gray-400">
      Last active: {formatDistanceToNow(new Date(agent.lastActivity), { addSuffix: true })}
    </div>
  </motion.div>
);

// Message Bubble Component
const MessageBubble: React.FC<{ message: ChatMessage }> = ({ message }) => {
  const isUser = message.type === 'user';

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`flex items-start space-x-2 max-w-[70%] ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
        <div className={`
          w-8 h-8 rounded-full flex items-center justify-center
          ${isUser ? 'bg-blue-600' : 'bg-gray-200'}
        `}>
          {isUser ? (
            <User className="w-4 h-4 text-white" />
          ) : (
            <Bot className="w-4 h-4 text-gray-600" />
          )}
        </div>

        <div className={`
          px-4 py-2 rounded-lg
          ${isUser
            ? 'bg-blue-600 text-white'
            : 'bg-white border border-gray-200 text-gray-900'
          }
        `}>
          <div className="text-sm whitespace-pre-wrap">
            {message.content}
          </div>

          <div className={`
            text-xs mt-1
            ${isUser ? 'text-blue-100' : 'text-gray-500'}
          `}>
            {formatDistanceToNow(new Date(message.timestamp), { addSuffix: true })}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Task Assignment Dialog Component
const TaskAssignmentDialog: React.FC<{
  onClose: () => void;
  onSubmit: () => void;
  taskInput: string;
  setTaskInput: (value: string) => void;
  agent: Agent | null;
}> = ({ onClose, onSubmit, taskInput, setTaskInput, agent }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    onClick={onClose}
  >
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      className="bg-white rounded-lg max-w-md w-full"
      onClick={(e) => e.stopPropagation()}
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Assign Task to {agent?.name}
          </h3>
          <Button variant="ghost" onClick={onClose}>×</Button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Task Description
            </label>
            <textarea
              value={taskInput}
              onChange={(e) => setTaskInput(e.target.value)}
              placeholder="Describe the task you want the agent to perform..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={onSubmit}
              disabled={!taskInput.trim()}
              icon={<Play className="w-4 h-4" />}
            >
              Assign Task
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  </motion.div>
);

// Agent Details Sidebar Component
const AgentDetailsSidebar: React.FC<{
  agent: Agent;
  tasks: Task[];
}> = ({ agent, tasks }) => {
  const [activeTab, setActiveTab] = useState('overview');

  const agentTasks = tasks.filter(task => task.agentId === agent.id);
  const activeTasks = agentTasks.filter(task => task.status === 'running' || task.status === 'pending');
  const completedTasks = agentTasks.filter(task => task.status === 'completed');

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'tasks', label: 'Tasks' },
    { id: 'performance', label: 'Performance' }
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Agent Details</h3>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex-1 py-2 px-3 text-sm font-medium border-b-2 transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
                }
              `}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            {/* Agent Status */}
            <Card title="Status">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Current Status</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                    {agent.status}
                  </span>
                </div>

                {agent.currentTask && (
                  <div>
                    <span className="text-sm text-gray-500">Current Task</span>
                    <p className="text-sm font-medium text-gray-900 mt-1">{agent.currentTask}</p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Last Active</span>
                  <span className="text-sm text-gray-900">
                    {formatDistanceToNow(new Date(agent.lastActivity), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </Card>

            {/* Quick Stats */}
            <Card title="Quick Stats">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{activeTasks.length}</div>
                  <div className="text-xs text-gray-500">Active Tasks</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{completedTasks.length}</div>
                  <div className="text-xs text-gray-500">Completed</div>
                </div>
              </div>
            </Card>

            {/* Capabilities */}
            {agent.capabilities && (
              <Card title="Capabilities">
                <div className="space-y-2">
                  {agent.capabilities.map((capability, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-gray-700">{capability}</span>
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>
        )}

        {activeTab === 'tasks' && (
          <div className="space-y-4">
            {/* Active Tasks */}
            {activeTasks.length > 0 && (
              <Card title="Active Tasks">
                <div className="space-y-3">
                  {activeTasks.map((task) => (
                    <TaskCard key={task.id} task={task} />
                  ))}
                </div>
              </Card>
            )}

            {/* Recent Completed Tasks */}
            {completedTasks.length > 0 && (
              <Card title="Recent Completed">
                <div className="space-y-3">
                  {completedTasks.slice(0, 5).map((task) => (
                    <TaskCard key={task.id} task={task} />
                  ))}
                </div>
              </Card>
            )}

            {agentTasks.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No tasks assigned yet</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="space-y-4">
            {agent.performance ? (
              <>
                {/* Performance Metrics */}
                <Card title="Performance Metrics">
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-500">Efficiency</span>
                        <span className="font-medium">{Math.round(agent.performance.efficiency * 100)}%</span>
                      </div>
                      <Progress value={agent.performance.efficiency} />
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-500">Success Rate</span>
                        <span className="font-medium">{Math.round(agent.performance.successRate * 100)}%</span>
                      </div>
                      <Progress value={agent.performance.successRate} />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Tasks Completed</span>
                        <div className="font-medium">{agent.performance.tasksCompleted}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Avg Duration</span>
                        <div className="font-medium">{agent.performance.averageDuration}s</div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Performance Chart */}
                <Card title="Recent Performance">
                  <div className="h-32 flex items-center justify-center text-gray-500">
                    <BarChart3 className="w-8 h-8 mr-2" />
                    Performance chart would go here
                  </div>
                </Card>
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No performance data available</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Task Card Component
const TaskCard: React.FC<{ task: Task }> = ({ task }) => {
  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-3 border border-gray-200 rounded-lg">
      <div className="flex items-start justify-between mb-2">
        <h4 className="font-medium text-gray-900 text-sm truncate">{task.title}</h4>
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTaskStatusColor(task.status)}`}>
          {task.status}
        </span>
      </div>

      {task.description && (
        <p className="text-xs text-gray-600 mb-2 line-clamp-2">{task.description}</p>
      )}

      {task.progress > 0 && (
        <div className="mb-2">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>Progress</span>
            <span>{Math.round(task.progress * 100)}%</span>
          </div>
          <Progress value={task.progress} className="h-1" />
        </div>
      )}

      <div className="flex items-center justify-between text-xs text-gray-500">
        <span>{formatDistanceToNow(new Date(task.createdAt), { addSuffix: true })}</span>
        {task.estimatedDuration > 0 && (
          <span>~{task.estimatedDuration}s</span>
        )}
      </div>
    </div>
  );
};

function getStatusColor(status: string): string {
  switch (status) {
    case 'idle': return 'text-green-600 bg-green-100';
    case 'busy': return 'text-yellow-600 bg-yellow-100';
    case 'working': return 'text-blue-600 bg-blue-100';
    case 'error': return 'text-red-600 bg-red-100';
    case 'offline': return 'text-gray-600 bg-gray-100';
    default: return 'text-gray-600 bg-gray-100';
  }
}

export default AgentInteractionPanel;
