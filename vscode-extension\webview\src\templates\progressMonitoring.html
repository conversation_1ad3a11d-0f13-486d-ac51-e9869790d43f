<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Aetherforge - Progress Monitoring</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            overflow: hidden;
        }
        
        #root {
            width: 100vw;
            height: 100vh;
            overflow: auto;
        }
        
        /* Loading spinner */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-progressBar-background);
            border-top: 4px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 16px;
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
        }
        
        /* Real-time indicator */
        .realtime-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 16px;
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            font-size: 12px;
        }
        
        .realtime-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--vscode-testing-iconPassed);
            animation: pulse 2s infinite;
        }
        
        .realtime-dot.inactive {
            background-color: var(--vscode-descriptionForeground);
            animation: none;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* Progress bars and charts */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--vscode-progressBar-background);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--vscode-progressBar-foreground);
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        .progress-fill.success {
            background-color: var(--vscode-testing-iconPassed);
        }
        
        .progress-fill.warning {
            background-color: var(--vscode-testing-iconQueued);
        }
        
        .progress-fill.error {
            background-color: var(--vscode-testing-iconFailed);
        }
        
        /* Status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-badge.completed {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        
        .status-badge.in-progress {
            background-color: var(--vscode-testing-iconQueued);
            color: white;
        }
        
        .status-badge.failed {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        
        .status-badge.paused {
            background-color: var(--vscode-testing-iconSkipped);
            color: white;
        }
        
        .status-badge.cancelled {
            background-color: var(--vscode-descriptionForeground);
            color: white;
        }
        
        /* Metrics cards */
        .metric-card {
            padding: 16px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            background-color: var(--vscode-panel-background);
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--vscode-editor-foreground);
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 8px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .metric-icon.primary {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }
        
        .metric-icon.success {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        
        .metric-icon.warning {
            background-color: var(--vscode-testing-iconQueued);
            color: white;
        }
        
        .metric-icon.info {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        /* Timeline styles */
        .timeline {
            position: relative;
            padding-left: 24px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: var(--vscode-panel-border);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 16px;
            padding-left: 24px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -7px;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--vscode-button-background);
            border: 2px solid var(--vscode-editor-background);
        }
        
        .timeline-item.success::before {
            background-color: var(--vscode-testing-iconPassed);
        }
        
        .timeline-item.error::before {
            background-color: var(--vscode-testing-iconFailed);
        }
        
        .timeline-item.warning::before {
            background-color: var(--vscode-testing-iconQueued);
        }
        
        .timeline-content {
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 12px;
        }
        
        .timeline-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .timeline-description {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 8px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        /* Pheromone trail visualization */
        .pheromone-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: var(--vscode-panel-background);
        }
        
        .pheromone-strength {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .pheromone-strength.high {
            background-color: var(--vscode-testing-iconFailed);
        }
        
        .pheromone-strength.medium {
            background-color: var(--vscode-testing-iconQueued);
        }
        
        .pheromone-strength.low {
            background-color: var(--vscode-testing-iconPassed);
        }
        
        .pheromone-content {
            flex: 1;
            min-width: 0;
        }
        
        .pheromone-type {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .pheromone-source {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 4px;
        }
        
        .pheromone-data {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-family: monospace;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .pheromone-time {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            text-align: right;
            flex-shrink: 0;
            margin-left: 12px;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="spinner"></div>
            <div class="loading-text">Loading Progress Monitor...</div>
        </div>
    </div>
    
    <div class="realtime-indicator" id="realtimeIndicator">
        <div class="realtime-dot" id="realtimeDot"></div>
        <span>Real-time Updates</span>
    </div>
    
    <script>
        // Initialize VS Code API
        const vscode = acquireVsCodeApi();
        
        // Real-time status management
        function updateRealtimeStatus(enabled) {
            const dot = document.getElementById('realtimeDot');
            if (enabled) {
                dot.classList.remove('inactive');
            } else {
                dot.classList.add('inactive');
            }
        }
        
        // Handle theme changes
        function updateTheme() {
            const body = document.body;
            const theme = body.getAttribute('data-vscode-theme-kind');
            
            body.className = '';
            if (theme === 'vscode-light') {
                body.classList.add('vscode-light');
            } else if (theme === 'vscode-dark') {
                body.classList.add('vscode-dark');
            } else if (theme === 'vscode-high-contrast') {
                body.classList.add('vscode-high-contrast');
            }
        }
        
        // Apply initial theme
        updateTheme();
        
        // Listen for theme changes
        new MutationObserver(updateTheme).observe(document.body, {
            attributes: true,
            attributeFilter: ['data-vscode-theme-kind']
        });
        
        // Message handling
        window.addEventListener('message', (event) => {
            const message = event.data;
            
            switch (message.command) {
                case 'realtimeStatus':
                    updateRealtimeStatus(message.enabled);
                    break;
                case 'projectUpdate':
                    // Handle project updates
                    break;
                case 'workflowUpdate':
                    // Handle workflow updates
                    break;
                case 'pheromoneUpdate':
                    // Handle pheromone updates
                    break;
            }
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Progress Monitoring Panel Error:', event.error);
            
            vscode.postMessage({
                command: 'error',
                data: {
                    message: event.error?.message || 'Progress monitoring panel error',
                    stack: event.error?.stack
                }
            });
        });
        
        // Notify extension that panel is ready
        vscode.postMessage({
            command: 'panelReady',
            data: { panel: 'progressMonitoring' }
        });
    </script>
</body>
</html>
