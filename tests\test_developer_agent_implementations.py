"""
Tests for newly implemented Developer Agent methods
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

import sys
sys.path.append(str(Path(__file__).parent.parent / "src"))

from developer_agent import DeveloperAgent, CodeGenerationContext, ProjectStructure, ProjectType
from architect_agent import SystemArchitecture, TechnologyChoice

class TestDeveloperAgentImplementations:
    """Test the newly implemented methods in DeveloperAgent"""
    
    @pytest.fixture
    def mock_architecture(self):
        """Create a mock system architecture"""
        architecture = Mock(spec=SystemArchitecture)
        architecture.project_name = "Test Project"
        architecture.description = "A test project"
        architecture.technology_stack = [
            Mock(spec=TechnologyChoice, name="React", category="frontend"),
            <PERSON><PERSON>(spec=TechnologyChoice, name="Node.js", category="backend"),
            <PERSON><PERSON>(spec=TechnologyChoice, name="TypeScript", category="language"),
            <PERSON><PERSON>(spec=TechnologyChoice, name="Express.js", category="backend"),
        ]
        return architecture
    
    @pytest.fixture
    def mock_context(self, mock_architecture):
        """Create a mock code generation context"""
        with tempfile.TemporaryDirectory() as temp_dir:
            context = Mock(spec=CodeGenerationContext)
            context.architecture = mock_architecture
            context.output_path = Path(temp_dir)
            context.requirements = ["User authentication", "API endpoints"]
            yield context
    
    @pytest.fixture
    def mock_structure(self):
        """Create a mock project structure"""
        structure = Mock(spec=ProjectStructure)
        structure.type = ProjectType.FULLSTACK
        structure.config_files = []
        return structure
    
    @pytest.fixture
    def developer_agent(self):
        """Create a DeveloperAgent instance"""
        return DeveloperAgent()
    
    @pytest.mark.asyncio
    async def test_generate_react_hooks(self, developer_agent, mock_context, mock_structure):
        """Test React hooks generation"""
        await developer_agent._generate_react_hooks(mock_context, mock_structure, True)
        
        # Check if hooks directory was created
        hooks_dir = mock_context.output_path / "src" / "hooks"
        assert hooks_dir.exists()
        
        # Check if useApi hook was created
        api_hook_file = hooks_dir / "useApi.ts"
        assert api_hook_file.exists()
        
        # Check content
        content = api_hook_file.read_text()
        assert "useApi" in content
        assert "useLocalStorage" in content
        assert "useDebounce" in content
        assert "useForm" in content
        assert "useState" in content
        assert "useEffect" in content
        
        # Check index file
        index_file = hooks_dir / "index.ts"
        assert index_file.exists()
        index_content = index_file.read_text()
        assert "export" in index_content
        assert "useApi" in index_content
    
    @pytest.mark.asyncio
    async def test_generate_react_services(self, developer_agent, mock_context, mock_structure):
        """Test React services generation"""
        await developer_agent._generate_react_services(mock_context, mock_structure, True)
        
        # Check if services directory was created
        services_dir = mock_context.output_path / "src" / "services"
        assert services_dir.exists()
        
        # Check if API service was created
        api_service_file = services_dir / "api.ts"
        assert api_service_file.exists()
        
        # Check content
        content = api_service_file.read_text()
        assert "ApiClient" in content
        assert "axios" in content
        assert "setToken" in content
        assert "clearToken" in content
        
        # Check auth service
        auth_service_file = services_dir / "auth.ts"
        assert auth_service_file.exists()
        auth_content = auth_service_file.read_text()
        assert "AuthService" in auth_content
        assert "login" in auth_content
        assert "register" in auth_content
        
        # Check index file
        index_file = services_dir / "index.ts"
        assert index_file.exists()
    
    @pytest.mark.asyncio
    async def test_generate_express_routes(self, developer_agent, mock_context, mock_structure):
        """Test Express routes generation"""
        await developer_agent._generate_express_routes(mock_context, mock_structure, True)
        
        # Check if routes directory was created
        routes_dir = mock_context.output_path / "src" / "routes"
        assert routes_dir.exists()
        
        # Check if auth routes were created
        auth_routes_file = routes_dir / "auth.ts"
        assert auth_routes_file.exists()
        
        # Check content
        content = auth_routes_file.read_text()
        assert "Router" in content
        assert "AuthController" in content
        assert "router.post('/login'" in content
        assert "router.post('/register'" in content
        assert "authMiddleware" in content
        
        # Check main routes
        index_file = routes_dir / "index.ts"
        assert index_file.exists()
        index_content = index_file.read_text()
        assert "health" in index_content
        assert "/auth" in index_content
    
    @pytest.mark.asyncio
    async def test_generate_express_controllers(self, developer_agent, mock_context, mock_structure):
        """Test Express controllers generation"""
        await developer_agent._generate_express_controllers(mock_context, mock_structure, True)
        
        # Check if controllers directory was created
        controllers_dir = mock_context.output_path / "src" / "controllers"
        assert controllers_dir.exists()
        
        # Check if base controller was created
        base_controller_file = controllers_dir / "BaseController.ts"
        assert base_controller_file.exists()
        
        # Check content
        content = base_controller_file.read_text()
        assert "BaseController" in content
        assert "sendSuccess" in content
        assert "sendError" in content
        assert "handleValidationErrors" in content
        
        # Check auth controller
        auth_controller_file = controllers_dir / "AuthController.ts"
        assert auth_controller_file.exists()
        auth_content = auth_controller_file.read_text()
        assert "AuthController" in auth_content
        assert "login =" in auth_content
        assert "register =" in auth_content
        assert "logout =" in auth_content
        
        # Check index file
        index_file = controllers_dir / "index.ts"
        assert index_file.exists()
    
    @pytest.mark.asyncio
    async def test_generate_express_services(self, developer_agent, mock_context, mock_structure):
        """Test Express services generation"""
        await developer_agent._generate_express_services(mock_context, mock_structure, True)
        
        # Check if services directory was created
        services_dir = mock_context.output_path / "src" / "services"
        assert services_dir.exists()
        
        # Check if base service was created
        base_service_file = services_dir / "BaseService.ts"
        assert base_service_file.exists()
        
        # Check content
        content = base_service_file.read_text()
        assert "BaseService" in content
        assert "handleServiceError" in content
        assert "validateRequired" in content
        
        # Check auth service
        auth_service_file = services_dir / "AuthService.ts"
        assert auth_service_file.exists()
        auth_content = auth_service_file.read_text()
        assert "AuthService" in auth_content
        assert "bcrypt" in auth_content
        assert "jwt" in auth_content
        assert "login" in auth_content
        assert "register" in auth_content
        assert "forgotPassword" in auth_content
    
    @pytest.mark.asyncio
    async def test_generate_express_models(self, developer_agent, mock_context, mock_structure):
        """Test Express models generation"""
        await developer_agent._generate_express_models(mock_context, mock_structure, True)
        
        # Check if models directory was created
        models_dir = mock_context.output_path / "src" / "models"
        assert models_dir.exists()
        
        # Check if base model was created
        base_model_file = models_dir / "BaseModel.ts"
        assert base_model_file.exists()
        
        # Check content
        content = base_model_file.read_text()
        assert "BaseModel" in content
        assert "Sequelize" in content
        assert "initModel" in content
        assert "associate" in content
        
        # Check user model
        user_model_file = models_dir / "User.ts"
        assert user_model_file.exists()
        user_content = user_model_file.read_text()
        assert "User" in user_content
        assert "email" in user_content
        assert "password" in user_content
        assert "firstName" in user_content
        assert "DataTypes.UUID" in user_content
        
        # Check index file
        index_file = models_dir / "index.ts"
        assert index_file.exists()
        index_content = index_file.read_text()
        assert "initializeModels" in index_content
    
    @pytest.mark.asyncio
    async def test_generate_express_middleware(self, developer_agent, mock_context, mock_structure):
        """Test Express middleware generation"""
        await developer_agent._generate_express_middleware(mock_context, mock_structure, True)
        
        # Check if middleware directory was created
        middleware_dir = mock_context.output_path / "src" / "middleware"
        assert middleware_dir.exists()
        
        # Check if auth middleware was created
        auth_middleware_file = middleware_dir / "auth.ts"
        assert auth_middleware_file.exists()
        
        # Check content
        content = auth_middleware_file.read_text()
        assert "authMiddleware" in content
        assert "optionalAuthMiddleware" in content
        assert "Bearer" in content
        assert "verifyToken" in content
        
        # Check validation middleware
        validation_middleware_file = middleware_dir / "validation.ts"
        assert validation_middleware_file.exists()
        validation_content = validation_middleware_file.read_text()
        assert "validateRequest" in validation_content
        assert "sanitizeInput" in validation_content
        
        # Check error middleware
        error_middleware_file = middleware_dir / "error.ts"
        assert error_middleware_file.exists()
        error_content = error_middleware_file.read_text()
        assert "errorHandler" in error_content
        assert "notFoundHandler" in error_content
        assert "asyncHandler" in error_content
        
        # Check index file
        index_file = middleware_dir / "index.ts"
        assert index_file.exists()
    
    @pytest.mark.asyncio
    async def test_javascript_generation(self, developer_agent, mock_context, mock_structure):
        """Test that JavaScript files are generated when TypeScript is False"""
        await developer_agent._generate_react_hooks(mock_context, mock_structure, False)
        
        hooks_dir = mock_context.output_path / "src" / "hooks"
        
        # Check if .js files were created instead of .ts
        api_hook_file = hooks_dir / "useApi.js"
        assert api_hook_file.exists()
        
        index_file = hooks_dir / "index.js"
        assert index_file.exists()
    
    def test_file_content_quality(self, developer_agent, mock_context, mock_structure):
        """Test that generated files have proper structure and imports"""
        # This is a synchronous test to check file content quality
        asyncio.run(developer_agent._generate_react_hooks(mock_context, mock_structure, True))
        
        hooks_dir = mock_context.output_path / "src" / "hooks"
        api_hook_file = hooks_dir / "useApi.ts"
        content = api_hook_file.read_text()
        
        # Check for proper imports
        assert "import { useState, useEffect, useCallback } from 'react';" in content
        
        # Check for proper TypeScript interfaces
        assert "interface UseApiOptions" in content
        assert "interface UseApiReturn" in content
        
        # Check for proper function exports
        assert "export function useApi" in content
        assert "export function useLocalStorage" in content
        
        # Check for proper error handling
        assert "try {" in content
        assert "catch" in content
        
        # Check for proper TypeScript generics
        assert "<T>" in content

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
