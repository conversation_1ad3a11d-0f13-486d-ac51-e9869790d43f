#!/bin/bash

# Aetherforge Backup Script
# Comprehensive backup solution for all system components

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_ROOT="${BACKUP_ROOT:-/opt/aetherforge/backups}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yml}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPRESSION_LEVEL="${COMPRESSION_LEVEL:-6}"

# Backup configuration
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_DIR="$BACKUP_ROOT/backup_$TIMESTAMP"
LOG_FILE="$BACKUP_DIR/backup.log"

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
    
    case $level in
        "ERROR")
            echo -e "${RED}❌ ${message}${NC}" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ ${message}${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ ${message}${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Create backup directory
create_backup_directory() {
    log "INFO" "Creating backup directory: $BACKUP_DIR"
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/volumes"
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$BACKUP_DIR/logs"
    
    # Create backup metadata
    cat > "$BACKUP_DIR/metadata.json" << EOF
{
    "backup_timestamp": "$TIMESTAMP",
    "backup_type": "full",
    "hostname": "$(hostname)",
    "aetherforge_version": "$(cat $PROJECT_ROOT/VERSION 2>/dev/null || echo 'unknown')",
    "environment": "${AETHERFORGE_ENV:-development}",
    "backup_size": "calculating...",
    "components": []
}
EOF
    
    log "SUCCESS" "Backup directory created"
}

# Backup PostgreSQL database
backup_database() {
    log "INFO" "Starting database backup..."
    
    # Check if PostgreSQL is running
    if ! docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log "WARNING" "PostgreSQL container is not running, skipping database backup"
        return 0
    fi
    
    # Create database dump
    log "INFO" "Creating PostgreSQL dump..."
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres pg_dumpall -U aetherforge > "$BACKUP_DIR/database/full_dump.sql"; then
        log "SUCCESS" "Database dump created"
    else
        error_exit "Failed to create database dump"
    fi
    
    # Create individual database dumps
    log "INFO" "Creating individual database dumps..."
    local databases
    databases=$(docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres psql -U aetherforge -t -c "SELECT datname FROM pg_database WHERE datistemplate = false;" | grep -v "^\s*$" | tr -d ' ')
    
    for db in $databases; do
        if [[ "$db" != "postgres" ]]; then
            log "INFO" "Backing up database: $db"
            docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres pg_dump -U aetherforge "$db" > "$BACKUP_DIR/database/${db}.sql"
        fi
    done
    
    # Compress database backups
    log "INFO" "Compressing database backups..."
    cd "$BACKUP_DIR/database"
    tar -czf "database_backup_$TIMESTAMP.tar.gz" *.sql
    rm -f *.sql
    
    log "SUCCESS" "Database backup completed"
}

# Backup Docker volumes
backup_volumes() {
    log "INFO" "Starting volume backup..."
    
    # Get list of volumes
    local volumes
    volumes=$(docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" config --volumes 2>/dev/null || echo "")
    
    if [[ -z "$volumes" ]]; then
        log "WARNING" "No volumes found to backup"
        return 0
    fi
    
    # Backup each volume
    for volume in $volumes; do
        local full_volume_name="${PWD##*/}_$volume"
        
        if docker volume inspect "$full_volume_name" &>/dev/null; then
            log "INFO" "Backing up volume: $volume"
            
            # Create volume backup using temporary container
            docker run --rm \
                -v "$full_volume_name":/data:ro \
                -v "$BACKUP_DIR/volumes":/backup \
                alpine tar czf "/backup/${volume}_$TIMESTAMP.tar.gz" -C /data .
            
            log "SUCCESS" "Volume $volume backed up"
        else
            log "WARNING" "Volume $full_volume_name not found, skipping"
        fi
    done
    
    log "SUCCESS" "Volume backup completed"
}

# Backup configuration files
backup_configuration() {
    log "INFO" "Starting configuration backup..."
    
    # Backup Docker Compose files
    cp "$PROJECT_ROOT"/*.yml "$BACKUP_DIR/config/" 2>/dev/null || true
    cp "$PROJECT_ROOT"/*.yaml "$BACKUP_DIR/config/" 2>/dev/null || true
    
    # Backup environment files
    cp "$PROJECT_ROOT"/.env* "$BACKUP_DIR/config/" 2>/dev/null || true
    
    # Backup configuration directories
    if [[ -d "$PROJECT_ROOT/config" ]]; then
        cp -r "$PROJECT_ROOT/config" "$BACKUP_DIR/config/app_config"
    fi
    
    if [[ -d "$PROJECT_ROOT/nginx" ]]; then
        cp -r "$PROJECT_ROOT/nginx" "$BACKUP_DIR/config/"
    fi
    
    # Backup scripts
    if [[ -d "$PROJECT_ROOT/scripts" ]]; then
        cp -r "$PROJECT_ROOT/scripts" "$BACKUP_DIR/config/"
    fi
    
    # Backup SSL certificates (if any)
    if [[ -d "$PROJECT_ROOT/ssl" ]]; then
        cp -r "$PROJECT_ROOT/ssl" "$BACKUP_DIR/config/"
    fi
    
    log "SUCCESS" "Configuration backup completed"
}

# Backup application logs
backup_logs() {
    log "INFO" "Starting log backup..."
    
    # Backup container logs
    local containers
    containers=$(docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps --services 2>/dev/null || echo "")
    
    for container in $containers; do
        local container_name="${PWD##*/}_${container}_1"
        
        if docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
            log "INFO" "Backing up logs for: $container"
            docker logs "$container_name" > "$BACKUP_DIR/logs/${container}.log" 2>&1 || true
        fi
    done
    
    # Backup system logs
    if [[ -d "/var/log/aetherforge" ]]; then
        cp -r "/var/log/aetherforge" "$BACKUP_DIR/logs/system_logs" 2>/dev/null || true
    fi
    
    # Compress logs
    cd "$BACKUP_DIR/logs"
    if [[ -n "$(ls -A . 2>/dev/null)" ]]; then
        tar -czf "logs_$TIMESTAMP.tar.gz" * 2>/dev/null || true
        rm -f *.log 2>/dev/null || true
        rm -rf system_logs 2>/dev/null || true
    fi
    
    log "SUCCESS" "Log backup completed"
}

# Backup monitoring data
backup_monitoring() {
    log "INFO" "Starting monitoring data backup..."
    
    # Backup Prometheus data
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps prometheus | grep -q "Up"; then
        log "INFO" "Backing up Prometheus data..."
        docker run --rm \
            -v "${PWD##*/}_prometheus-data":/data:ro \
            -v "$BACKUP_DIR/volumes":/backup \
            alpine tar czf "/backup/prometheus_data_$TIMESTAMP.tar.gz" -C /data . 2>/dev/null || true
    fi
    
    # Backup Grafana data
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps grafana | grep -q "Up"; then
        log "INFO" "Backing up Grafana data..."
        docker run --rm \
            -v "${PWD##*/}_grafana-data":/data:ro \
            -v "$BACKUP_DIR/volumes":/backup \
            alpine tar czf "/backup/grafana_data_$TIMESTAMP.tar.gz" -C /data . 2>/dev/null || true
    fi
    
    log "SUCCESS" "Monitoring data backup completed"
}

# Create backup archive
create_backup_archive() {
    log "INFO" "Creating backup archive..."
    
    cd "$BACKUP_ROOT"
    
    # Calculate backup size
    local backup_size
    backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    
    # Update metadata
    local temp_metadata=$(mktemp)
    jq --arg size "$backup_size" '.backup_size = $size' "$BACKUP_DIR/metadata.json" > "$temp_metadata"
    mv "$temp_metadata" "$BACKUP_DIR/metadata.json"
    
    # Create compressed archive
    log "INFO" "Compressing backup archive..."
    tar -czf "aetherforge_backup_$TIMESTAMP.tar.gz" "backup_$TIMESTAMP"
    
    if [[ $? -eq 0 ]]; then
        log "SUCCESS" "Backup archive created: aetherforge_backup_$TIMESTAMP.tar.gz"
        log "INFO" "Backup size: $backup_size"
        
        # Remove uncompressed backup directory
        rm -rf "$BACKUP_DIR"
        
        # Create symlink to latest backup
        ln -sf "aetherforge_backup_$TIMESTAMP.tar.gz" "latest_backup.tar.gz"
        
        return 0
    else
        error_exit "Failed to create backup archive"
    fi
}

# Cleanup old backups
cleanup_old_backups() {
    log "INFO" "Cleaning up old backups (retention: $RETENTION_DAYS days)..."
    
    cd "$BACKUP_ROOT"
    
    # Find and remove old backup files
    local old_backups
    old_backups=$(find . -name "aetherforge_backup_*.tar.gz" -mtime +$RETENTION_DAYS 2>/dev/null || true)
    
    if [[ -n "$old_backups" ]]; then
        echo "$old_backups" | while read -r backup_file; do
            log "INFO" "Removing old backup: $backup_file"
            rm -f "$backup_file"
        done
        
        log "SUCCESS" "Old backups cleaned up"
    else
        log "INFO" "No old backups to clean up"
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_file="$BACKUP_ROOT/aetherforge_backup_$TIMESTAMP.tar.gz"
    
    log "INFO" "Verifying backup integrity..."
    
    if [[ -f "$backup_file" ]]; then
        # Test archive integrity
        if tar -tzf "$backup_file" >/dev/null 2>&1; then
            log "SUCCESS" "Backup archive integrity verified"
            
            # Calculate and store checksum
            local checksum
            checksum=$(sha256sum "$backup_file" | cut -d' ' -f1)
            echo "$checksum  aetherforge_backup_$TIMESTAMP.tar.gz" > "$backup_file.sha256"
            
            log "INFO" "Backup checksum: $checksum"
            return 0
        else
            error_exit "Backup archive is corrupted"
        fi
    else
        error_exit "Backup file not found: $backup_file"
    fi
}

# Send backup notification
send_backup_notification() {
    local status=$1
    local webhook_url="${BACKUP_WEBHOOK_URL:-}"
    
    if [[ -n "$webhook_url" ]]; then
        local backup_size=$(du -sh "$BACKUP_ROOT/aetherforge_backup_$TIMESTAMP.tar.gz" 2>/dev/null | cut -f1 || echo "unknown")
        local message="Aetherforge backup $status on $(hostname) - Size: $backup_size"
        
        curl -X POST -H "Content-Type: application/json" \
             -d "{\"text\":\"$message\"}" \
             "$webhook_url" &>/dev/null || true
    fi
}

# Main backup function
main() {
    local backup_type=${1:-"full"}
    
    log "INFO" "Starting Aetherforge backup (type: $backup_type)..."
    
    # Create backup directory
    create_backup_directory
    
    case $backup_type in
        "full")
            backup_database
            backup_volumes
            backup_configuration
            backup_logs
            backup_monitoring
            ;;
        "data")
            backup_database
            backup_volumes
            ;;
        "config")
            backup_configuration
            ;;
        "logs")
            backup_logs
            ;;
        *)
            error_exit "Unknown backup type: $backup_type"
            ;;
    esac
    
    # Create archive and cleanup
    create_backup_archive
    verify_backup
    cleanup_old_backups
    
    # Send notification
    send_backup_notification "completed successfully"
    
    log "SUCCESS" "Aetherforge backup completed successfully!"
    log "INFO" "Backup location: $BACKUP_ROOT/aetherforge_backup_$TIMESTAMP.tar.gz"
}

# Show usage
show_usage() {
    cat << EOF
Aetherforge Backup Script

Usage: $0 [BACKUP_TYPE]

Backup Types:
    full        Complete backup (default) - database, volumes, config, logs
    data        Data only - database and volumes
    config      Configuration files only
    logs        Log files only

Environment Variables:
    BACKUP_ROOT         Backup directory (default: /opt/aetherforge/backups)
    RETENTION_DAYS      Backup retention in days (default: 30)
    BACKUP_WEBHOOK_URL  Webhook URL for notifications

Examples:
    $0              # Full backup
    $0 full         # Full backup
    $0 data         # Data backup only
    $0 config       # Configuration backup only

EOF
}

# Make script executable
chmod +x "$0"

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case ${1:-} in
        "-h"|"--help"|"help")
            show_usage
            ;;
        *)
            main "$@"
            ;;
    esac
fi
