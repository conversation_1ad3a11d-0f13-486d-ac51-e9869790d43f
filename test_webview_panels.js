#!/usr/bin/env node
/**
 * Comprehensive test script for Aetherforge VS Code Extension Webview Panels
 * Validates React components, TypeScript configuration, and build system
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 AETHERFORGE WEBVIEW PANELS VERIFICATION');
console.log('============================================================');

/**
 * Test webview directory structure
 */
function testWebviewStructure() {
    console.log('\n📁 Testing Webview Structure...');
    
    const requiredFiles = [
        'webview/package.json',
        'webview/webpack.config.js',
        'webview/tsconfig.json',
        'webview/src/types/index.ts',
        'webview/src/utils/vscode.ts',
        'webview/src/store/index.ts',
        'webview/src/components/common/Button.tsx',
        'webview/src/components/common/Card.tsx',
        'webview/src/components/common/Input.tsx',
        'webview/src/components/common/Progress.tsx',
        'webview/src/panels/ProjectConfigPanel.tsx',
        'webview/src/panels/AgentInteractionPanel.tsx',
        'webview/src/panels/ProgressMonitoringPanel.tsx',
        'webview/src/templates/projectConfig.html',
        'webview/src/templates/agentInteraction.html',
        'webview/src/templates/progressMonitoring.html'
    ];
    
    let missingFiles = [];
    requiredFiles.forEach(file => {
        const filePath = path.join('vscode-extension', file);
        if (!fs.existsSync(filePath)) {
            missingFiles.push(file);
        } else {
            console.log(`   ✅ ${file}`);
        }
    });
    
    if (missingFiles.length > 0) {
        console.log(`   ❌ Missing files: ${missingFiles.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${requiredFiles.length} required files present`);
    return true;
}

/**
 * Test package.json configuration
 */
function testPackageConfiguration() {
    console.log('\n📦 Testing Package Configuration...');
    
    const packagePath = path.join('vscode-extension', 'webview', 'package.json');
    
    if (!fs.existsSync(packagePath)) {
        console.log('❌ webview/package.json not found');
        return false;
    }
    
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Test dependencies
    const requiredDeps = [
        'react', 'react-dom', 'lucide-react', 'recharts', 'react-flow-renderer',
        'framer-motion', 'react-hook-form', 'react-select', 'react-hot-toast',
        'zustand', 'date-fns'
    ];
    
    const missingDeps = requiredDeps.filter(dep => !packageContent.dependencies[dep]);
    
    if (missingDeps.length > 0) {
        console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${requiredDeps.length} required dependencies present`);
    
    // Test dev dependencies
    const requiredDevDeps = [
        '@types/react', '@types/react-dom', 'typescript', 'webpack', 'webpack-cli'
    ];
    
    const missingDevDeps = requiredDevDeps.filter(dep => !packageContent.devDependencies[dep]);
    
    if (missingDevDeps.length > 0) {
        console.log(`❌ Missing dev dependencies: ${missingDevDeps.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${requiredDevDeps.length} required dev dependencies present`);
    
    // Test scripts
    const requiredScripts = ['build', 'dev', 'clean'];
    const missingScripts = requiredScripts.filter(script => !packageContent.scripts[script]);
    
    if (missingScripts.length > 0) {
        console.log(`❌ Missing scripts: ${missingScripts.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${requiredScripts.length} required scripts present`);
    
    return true;
}

/**
 * Test React component structure
 */
function testReactComponents() {
    console.log('\n⚛️  Testing React Components...');
    
    const components = [
        { file: 'webview/src/panels/ProjectConfigPanel.tsx', name: 'ProjectConfigPanel' },
        { file: 'webview/src/panels/AgentInteractionPanel.tsx', name: 'AgentInteractionPanel' },
        { file: 'webview/src/panels/ProgressMonitoringPanel.tsx', name: 'ProgressMonitoringPanel' },
        { file: 'webview/src/components/common/Button.tsx', name: 'Button' },
        { file: 'webview/src/components/common/Card.tsx', name: 'Card' },
        { file: 'webview/src/components/common/Input.tsx', name: 'Input' },
        { file: 'webview/src/components/common/Progress.tsx', name: 'Progress' }
    ];
    
    let passedTests = 0;
    
    components.forEach(component => {
        const filePath = path.join('vscode-extension', component.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`   ❌ ${component.name} - File not found`);
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for React component structure
        const hasReactImport = content.includes('import React') || content.includes('from \'react\'');
        const hasComponentExport = content.includes(`export default ${component.name}`) || 
                                  content.includes(`const ${component.name}`) ||
                                  content.includes(`function ${component.name}`);
        const hasTypeScript = content.includes('interface') || content.includes('type ');
        const hasJSX = content.includes('return (') || content.includes('<div') || content.includes('<motion.');
        
        if (hasReactImport && hasComponentExport && hasTypeScript && hasJSX) {
            console.log(`   ✅ ${component.name} - Complete React component`);
            passedTests++;
        } else {
            console.log(`   ❌ ${component.name} - Missing: ${[
                !hasReactImport && 'React import',
                !hasComponentExport && 'Component export',
                !hasTypeScript && 'TypeScript types',
                !hasJSX && 'JSX structure'
            ].filter(Boolean).join(', ')}`);
        }
    });
    
    console.log(`\n📊 React Components: ${passedTests}/${components.length} tests passed`);
    
    return passedTests === components.length;
}

/**
 * Test TypeScript configuration
 */
function testTypeScriptConfig() {
    console.log('\n🔷 Testing TypeScript Configuration...');
    
    const tsconfigPath = path.join('vscode-extension', 'webview', 'tsconfig.json');
    
    if (!fs.existsSync(tsconfigPath)) {
        console.log('❌ tsconfig.json not found');
        return false;
    }
    
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    
    // Check essential TypeScript options
    const requiredOptions = {
        'target': 'ES2020',
        'lib': ['DOM', 'DOM.Iterable', 'ES6'],
        'jsx': 'react-jsx',
        'moduleResolution': 'node',
        'strict': true
    };
    
    let missingOptions = [];
    Object.entries(requiredOptions).forEach(([option, expectedValue]) => {
        const actualValue = tsconfig.compilerOptions[option];
        if (Array.isArray(expectedValue)) {
            if (!Array.isArray(actualValue) || !expectedValue.every(val => actualValue.includes(val))) {
                missingOptions.push(option);
            }
        } else if (actualValue !== expectedValue) {
            missingOptions.push(option);
        }
    });
    
    if (missingOptions.length > 0) {
        console.log(`❌ Missing/incorrect TypeScript options: ${missingOptions.join(', ')}`);
        return false;
    }
    
    console.log('✅ TypeScript configuration is correct');
    
    // Check path mapping
    if (tsconfig.compilerOptions.paths && tsconfig.compilerOptions.paths['@/*']) {
        console.log('✅ Path mapping configured');
    } else {
        console.log('⚠️  Path mapping not configured');
    }
    
    return true;
}

/**
 * Test HTML templates
 */
function testHTMLTemplates() {
    console.log('\n🌐 Testing HTML Templates...');
    
    const templates = [
        'webview/src/templates/projectConfig.html',
        'webview/src/templates/agentInteraction.html',
        'webview/src/templates/progressMonitoring.html'
    ];
    
    let passedTests = 0;
    
    templates.forEach(template => {
        const filePath = path.join('vscode-extension', template);
        
        if (!fs.existsSync(filePath)) {
            console.log(`   ❌ ${path.basename(template)} - File not found`);
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for essential HTML structure
        const hasDoctype = content.includes('<!DOCTYPE html>');
        const hasVSCodeAPI = content.includes('acquireVsCodeApi()');
        const hasCSP = content.includes('Content-Security-Policy');
        const hasErrorHandling = content.includes('window.addEventListener(\'error\'');
        const hasThemeSupport = content.includes('data-vscode-theme-kind');
        
        if (hasDoctype && hasVSCodeAPI && hasCSP && hasErrorHandling && hasThemeSupport) {
            console.log(`   ✅ ${path.basename(template)} - Complete HTML template`);
            passedTests++;
        } else {
            console.log(`   ❌ ${path.basename(template)} - Missing: ${[
                !hasDoctype && 'DOCTYPE',
                !hasVSCodeAPI && 'VS Code API',
                !hasCSP && 'CSP',
                !hasErrorHandling && 'Error handling',
                !hasThemeSupport && 'Theme support'
            ].filter(Boolean).join(', ')}`);
        }
    });
    
    console.log(`\n📊 HTML Templates: ${passedTests}/${templates.length} tests passed`);
    
    return passedTests === templates.length;
}

/**
 * Test store and utilities
 */
function testStoreAndUtils() {
    console.log('\n🏪 Testing Store and Utilities...');
    
    const files = [
        { file: 'webview/src/store/index.ts', checks: ['zustand', 'useProjectConfigStore', 'useAgentInteractionStore', 'useProgressMonitoringStore'] },
        { file: 'webview/src/utils/vscode.ts', checks: ['VSCodeAPI', 'vscode', 'useVSCode', 'WebviewErrorBoundary'] },
        { file: 'webview/src/types/index.ts', checks: ['ProjectConfig', 'Agent', 'ChatMessage', 'Task', 'Project', 'Workflow'] }
    ];
    
    let passedTests = 0;
    
    files.forEach(({ file, checks }) => {
        const filePath = path.join('vscode-extension', file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`   ❌ ${path.basename(file)} - File not found`);
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        const missingChecks = checks.filter(check => !content.includes(check));
        
        if (missingChecks.length === 0) {
            console.log(`   ✅ ${path.basename(file)} - All exports present`);
            passedTests++;
        } else {
            console.log(`   ❌ ${path.basename(file)} - Missing: ${missingChecks.join(', ')}`);
        }
    });
    
    console.log(`\n📊 Store and Utils: ${passedTests}/${files.length} tests passed`);
    
    return passedTests === files.length;
}

/**
 * Main test execution
 */
async function runTests() {
    console.log('🚀 Starting Webview Panels Verification...\n');
    
    const tests = [
        { name: 'Webview Structure', test: testWebviewStructure },
        { name: 'Package Configuration', test: testPackageConfiguration },
        { name: 'React Components', test: testReactComponents },
        { name: 'TypeScript Config', test: testTypeScriptConfig },
        { name: 'HTML Templates', test: testHTMLTemplates },
        { name: 'Store and Utils', test: testStoreAndUtils }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            const result = test.test();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ ${test.name} failed with error: ${error.message}`);
        }
    }
    
    console.log('\n============================================================');
    console.log('🎯 WEBVIEW PANELS VERIFICATION SUMMARY');
    console.log('============================================================');
    console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`📊 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL TESTS PASSED!');
        console.log('✅ Webview panels are fully implemented and ready for use');
        console.log('\n📋 Webview Features:');
        console.log('   • Project Configuration Panel with React components');
        console.log('   • Agent Interaction Panel with real-time chat');
        console.log('   • Progress Monitoring Panel with live updates');
        console.log('   • Comprehensive TypeScript type definitions');
        console.log('   • Zustand state management stores');
        console.log('   • VS Code API integration utilities');
        console.log('   • Responsive React components with Framer Motion');
        console.log('   • HTML templates with VS Code theming');
        console.log('   • Webpack build configuration');
        console.log('   • Complete development environment');
        
        return true;
    } else {
        console.log('\n⚠️  Some tests failed. Please review the issues above.');
        return false;
    }
}

// Run the tests
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runTests };
