import { Request, Response, NextFunction } from 'express';
import { indexController } from '../controllers/index';
import { indexService } from '../services/indexService';

// Mock the service
jest.mock('../services/indexService');

describe('indexController', () => {
  let controller: indexController;
  let mockService: jest.Mocked<indexService>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    controller = new indexController();
    mockService = new indexService() as jest.Mocked<indexService>;

    mockRequest = {
      body: {},
      params: {},
      query: {},
      user: { id: 'user123' }
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('createindex', () => {
    it('should create a new index successfully', async () => {
      const mockData = { name: 'Test index', description: 'Test description' };
      const mockResult = { id: '123', ...mockData };

      mockRequest.body = mockData;
      mockService.create.mockResolvedValue(mockResult);

      await controller.createindex(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.create).toHaveBeenCalledWith(mockData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult
      });
    });

    it('should handle validation errors', async () => {
      mockRequest.body = { /* invalid data */ };
      const validationError = new Error('Validation failed');
      validationError.name = 'ValidationError';

      mockService.create.mockRejectedValue(validationError);

      await controller.createindex(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(validationError);
    });
  });

  describe('getindexById', () => {
    it('should return index by id', async () => {
      const mockId = '123';
      const mockResult = { id: mockId, name: 'Test index' };

      mockRequest.params = { id: mockId };
      mockService.findById.mockResolvedValue(mockResult);

      await controller.getindexById(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.findById).toHaveBeenCalledWith(mockId);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult
      });
    });

    it('should handle not found error', async () => {
      const mockId = 'nonexistent';
      mockRequest.params = { id: mockId };

      mockService.findById.mockResolvedValue(null);

      await controller.getindexById(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'index not found'
      });
    });
  });

  describe('updateindex', () => {
    it('should update index successfully', async () => {
      const mockId = '123';
      const updateData = { name: 'Updated index' };
      const mockResult = { id: mockId, ...updateData };

      mockRequest.params = { id: mockId };
      mockRequest.body = updateData;
      mockService.update.mockResolvedValue(mockResult);

      await controller.updateindex(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.update).toHaveBeenCalledWith(mockId, updateData);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult
      });
    });
  });

  describe('deleteindex', () => {
    it('should delete index successfully', async () => {
      const mockId = '123';
      mockRequest.params = { id: mockId };
      mockService.delete.mockResolvedValue(true);

      await controller.deleteindex(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.delete).toHaveBeenCalledWith(mockId);
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.send).toHaveBeenCalled();
    });
  });
});