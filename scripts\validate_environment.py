#!/usr/bin/env python3
"""
TaoForge Environment Validation and Setup Script
Validates and automatically configures the development/production environment
"""

import os
import sys
import platform
import subprocess
import json
import shutil
import secrets
import string
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class EnvironmentValidator:
    """Comprehensive environment validation and setup"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.is_windows = self.platform == "windows"
        
        # Validation results
        self.validation_results = {}
        self.issues_found = []
        self.fixes_applied = []
        
    def log(self, level: str, message: str):
        """Cross-platform logging"""
        icons = {
            "ERROR": "X",
            "SUCCESS": "OK",
            "WARNING": "!",
            "INFO": "i"
        }
        
        icon = icons.get(level, "")
        print(f"{icon} {message}")
    
    def validate_python_environment(self) -> bool:
        """Validate Python installation and version"""
        self.log("INFO", "Validating Python environment...")
        
        try:
            # Check Python version
            version = sys.version_info
            if version.major != 3 or version.minor < 11:
                self.issues_found.append("Python 3.11+ required")
                return False
            
            self.log("SUCCESS", f"Python {version.major}.{version.minor}.{version.micro} is compatible")
            
            # Check pip
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                self.issues_found.append("pip is not available")
                return False
            
            self.log("SUCCESS", "pip is available")
            return True
            
        except Exception as e:
            self.issues_found.append(f"Python validation failed: {e}")
            return False
    
    def validate_docker_environment(self) -> bool:
        """Validate Docker installation"""
        self.log("INFO", "Validating Docker environment...")
        
        try:
            # Check Docker
            result = subprocess.run(["docker", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.issues_found.append("Docker is not installed or not in PATH")
                return False
            
            docker_version = result.stdout.strip()
            self.log("SUCCESS", f"Docker is available: {docker_version}")
            
            # Check Docker Compose
            for cmd in [["docker", "compose"], ["docker-compose"]]:
                try:
                    result = subprocess.run(cmd + ["--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        compose_version = result.stdout.strip()
                        self.log("SUCCESS", f"Docker Compose is available: {compose_version}")
                        return True
                except FileNotFoundError:
                    continue
            
            self.issues_found.append("Docker Compose is not available")
            return False
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.issues_found.append("Docker is not installed or not accessible")
            return False
    
    def validate_required_files(self) -> bool:
        """Validate required project files exist"""
        self.log("INFO", "Validating required project files...")
        
        required_files = [
            "requirements.txt",
            "docker-compose.yml", 
            "docker-compose.prod.yml",
            "src/orchestrator.py",
            "scripts/deploy.sh",
            "scripts/health_check.sh"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.issues_found.extend([f"Missing file: {f}" for f in missing_files])
            return False
        
        self.log("SUCCESS", "All required files are present")
        return True
    
    def validate_environment_variables(self) -> bool:
        """Validate environment variables"""
        self.log("INFO", "Validating environment variables...")
        
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if not env_file.exists():
            if env_example.exists():
                self.log("WARNING", ".env file missing, will create from .env.example")
                return self.create_env_file()
            else:
                self.issues_found.append(".env and .env.example files are missing")
                return False
        
        # Validate .env content
        try:
            env_vars = self.parse_env_file(env_file)
            
            required_vars = [
                "OPENAI_API_KEY",
                "POSTGRES_PASSWORD", 
                "JWT_SECRET"
            ]
            
            missing_vars = []
            for var in required_vars:
                if var not in env_vars or not env_vars[var] or env_vars[var].startswith("your_"):
                    missing_vars.append(var)
            
            if missing_vars:
                self.log("WARNING", f"Environment variables need configuration: {', '.join(missing_vars)}")
                return self.fix_environment_variables(env_vars, missing_vars)
            
            self.log("SUCCESS", "Environment variables are configured")
            return True
            
        except Exception as e:
            self.issues_found.append(f"Failed to validate environment variables: {e}")
            return False
    
    def parse_env_file(self, env_file: Path) -> Dict[str, str]:
        """Parse .env file into dictionary"""
        env_vars = {}
        
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip().strip('"\'')
        
        return env_vars
    
    def create_env_file(self) -> bool:
        """Create .env file from .env.example"""
        try:
            env_example = self.project_root / ".env.example"
            env_file = self.project_root / ".env"
            
            shutil.copy2(env_example, env_file)
            self.fixes_applied.append("Created .env file from .env.example")
            self.log("SUCCESS", "Created .env file from .env.example")
            
            # Now validate and fix the new file
            env_vars = self.parse_env_file(env_file)
            return self.fix_environment_variables(env_vars, [])
            
        except Exception as e:
            self.issues_found.append(f"Failed to create .env file: {e}")
            return False
    
    def fix_environment_variables(self, env_vars: Dict[str, str], missing_vars: List[str]) -> bool:
        """Automatically fix environment variables where possible"""
        try:
            env_file = self.project_root / ".env"
            
            # Generate secure values for missing variables
            if "POSTGRES_PASSWORD" in missing_vars or not env_vars.get("POSTGRES_PASSWORD"):
                env_vars["POSTGRES_PASSWORD"] = self.generate_secure_password()
                self.fixes_applied.append("Generated secure PostgreSQL password")
            
            if "JWT_SECRET" in missing_vars or not env_vars.get("JWT_SECRET"):
                env_vars["JWT_SECRET"] = self.generate_jwt_secret()
                self.fixes_applied.append("Generated JWT secret")
            
            # Write updated .env file
            self.write_env_file(env_file, env_vars)
            
            # Check if OPENAI_API_KEY still needs manual configuration
            if "OPENAI_API_KEY" in missing_vars or not env_vars.get("OPENAI_API_KEY") or env_vars.get("OPENAI_API_KEY", "").startswith("your_"):
                self.log("WARNING", "OPENAI_API_KEY requires manual configuration")
                self.log("INFO", "Please edit .env file and set your OpenAI API key")
                return False
            
            self.log("SUCCESS", "Environment variables configured automatically")
            return True
            
        except Exception as e:
            self.issues_found.append(f"Failed to fix environment variables: {e}")
            return False
    
    def generate_secure_password(self, length: int = 32) -> str:
        """Generate a secure random password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def generate_jwt_secret(self, length: int = 64) -> str:
        """Generate a secure JWT secret"""
        return secrets.token_urlsafe(length)
    
    def write_env_file(self, env_file: Path, env_vars: Dict[str, str]):
        """Write environment variables to .env file"""
        with open(env_file, 'w') as f:
            f.write("# TaoForge Environment Configuration\n")
            f.write("# Generated automatically - edit as needed\n\n")
            
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
    
    def validate_dependencies(self) -> bool:
        """Validate Python dependencies"""
        self.log("INFO", "Validating Python dependencies...")
        
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            self.issues_found.append("requirements.txt not found")
            return False
        
        try:
            # Try to import key dependencies
            import_tests = [
                ("fastapi", "FastAPI"),
                ("uvicorn", "Uvicorn"),
                ("sqlalchemy", "SQLAlchemy"),
                ("redis", "Redis"),
                ("openai", "OpenAI")
            ]
            
            missing_deps = []
            for package, name in import_tests:
                try:
                    __import__(package)
                    self.log("SUCCESS", f"{name} is available")
                except ImportError:
                    missing_deps.append(package)
                    self.log("WARNING", f"{name} is not installed")
            
            if missing_deps:
                self.log("INFO", "Installing missing dependencies...")
                return self.install_dependencies()
            
            return True
            
        except Exception as e:
            self.issues_found.append(f"Dependency validation failed: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        try:
            requirements_file = self.project_root / "requirements.txt"
            
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.fixes_applied.append("Installed Python dependencies")
                self.log("SUCCESS", "Dependencies installed successfully")
                return True
            else:
                self.issues_found.append(f"Failed to install dependencies: {result.stderr}")
                return False
                
        except Exception as e:
            self.issues_found.append(f"Failed to install dependencies: {e}")
            return False
    
    def validate_directories(self) -> bool:
        """Validate and create required directories"""
        self.log("INFO", "Validating project directories...")
        
        required_dirs = [
            "projects",
            "logs", 
            "backups",
            "monitoring/grafana/dashboards",
            "nginx/ssl"
        ]
        
        created_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                try:
                    full_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_path)
                except Exception as e:
                    self.issues_found.append(f"Failed to create directory {dir_path}: {e}")
                    return False
        
        if created_dirs:
            self.fixes_applied.extend([f"Created directory: {d}" for d in created_dirs])
            self.log("SUCCESS", f"Created {len(created_dirs)} missing directories")
        else:
            self.log("SUCCESS", "All required directories exist")
        
        return True
    
    def run_validation(self, auto_fix: bool = True) -> bool:
        """Run complete environment validation"""
        self.log("INFO", f"Starting environment validation on {platform.system()}...")
        
        validations = [
            ("Python Environment", self.validate_python_environment),
            ("Docker Environment", self.validate_docker_environment),
            ("Required Files", self.validate_required_files),
            ("Project Directories", self.validate_directories),
            ("Environment Variables", self.validate_environment_variables),
            ("Python Dependencies", self.validate_dependencies)
        ]
        
        all_valid = True
        
        for name, validator in validations:
            self.log("INFO", f"Validating: {name}")
            try:
                result = validator()
                self.validation_results[name] = result
                if not result:
                    all_valid = False
            except Exception as e:
                self.log("ERROR", f"Validation failed for {name}: {e}")
                self.validation_results[name] = False
                all_valid = False
        
        # Generate report
        self.generate_validation_report()
        
        return all_valid
    
    def generate_validation_report(self):
        """Generate validation report"""
        self.log("INFO", "Environment Validation Report")
        print("=" * 50)
        
        for name, result in self.validation_results.items():
            status = "PASS" if result else "FAIL"
            print(f"{name}: {status}")

        if self.fixes_applied:
            print("\nFixes Applied:")
            for fix in self.fixes_applied:
                print(f"  + {fix}")

        if self.issues_found:
            print("\nIssues Found:")
            for issue in self.issues_found:
                print(f"  - {issue}")

        print("=" * 50)

        if all(self.validation_results.values()):
            self.log("SUCCESS", "Environment validation passed! TaoForge is ready to run.")
        else:
            self.log("WARNING", "Environment validation found issues. Please address them before proceeding.")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TaoForge environment validation and setup")
    parser.add_argument("--no-auto-fix", action="store_true", help="Don't automatically fix issues")
    
    args = parser.parse_args()
    
    validator = EnvironmentValidator()
    success = validator.run_validation(auto_fix=not args.no_auto_fix)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
