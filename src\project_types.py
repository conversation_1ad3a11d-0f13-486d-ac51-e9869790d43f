"""
Project Types and Shared Data Structures
Contains enums and dataclasses used across the project generation system
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List
from enum import Enum

class ProjectType(Enum):
    """Supported project types"""
    WEB_APPLICATION = "web_application"
    MOBILE_APPLICATION = "mobile_application"
    API_SERVICE = "api_service"
    DATA_PLATFORM = "data_platform"
    DESKTOP_APPLICATION = "desktop_application"
    MICROSERVICE = "microservice"
    FULLSTACK = "fullstack"
    FRONTEND = "frontend"
    BACKEND = "backend"
    LIBRARY = "library"
    CLI_TOOL = "cli_tool"

class PackageFormat(Enum):
    """Supported package formats"""
    ZIP = "zip"
    TAR_GZ = "tar.gz"
    TAR_BZ2 = "tar.bz2"
    DIRECTORY = "directory"

class OutputFormat(Enum):
    """Output delivery formats"""
    LOCAL_DIRECTORY = "local_directory"
    COMPRESSED_ARCHIVE = "compressed_archive"
    GIT_REPOSITORY = "git_repository"
    DOCKER_IMAGE = "docker_image"

@dataclass
class ProjectTemplate:
    """Template configuration for different project types"""
    name: str
    project_type: ProjectType
    directories: List[str] = field(default_factory=list)
    files: Dict[str, str] = field(default_factory=dict)
    dependencies: Dict[str, List[str]] = field(default_factory=dict)
    build_commands: List[str] = field(default_factory=list)
    test_commands: List[str] = field(default_factory=list)
    deployment_config: Dict[str, Any] = field(default_factory=dict)

@dataclass
class GenerationConfig:
    """Configuration for project generation"""
    project_type: ProjectType
    package_format: PackageFormat = PackageFormat.DIRECTORY
    output_format: OutputFormat = OutputFormat.LOCAL_DIRECTORY
    include_tests: bool = True
    include_docs: bool = True
    include_ci_cd: bool = True
    include_docker: bool = False
    include_deployment: bool = False
    code_quality_level: str = "standard"  # basic, standard, high, enterprise
    security_level: str = "standard"  # basic, standard, high, enterprise
