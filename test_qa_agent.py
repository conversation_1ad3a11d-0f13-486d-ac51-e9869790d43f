#!/usr/bin/env python3
"""
Test script for the QA Agent
Demonstrates comprehensive testing and validation capabilities
"""

import asyncio
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from qa_agent import QAAgent, QAContext, QualityLevel, QAAgentExecutor
from analyst_agent import ProjectSpecification
from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier

async def test_qa_agent():
    """Test the QA agent with sample generated code"""
    print("🧪 Testing QA Agent Capabilities")
    print("=" * 60)
    
    # Create test cases
    test_cases = [
        {
            "name": "React TypeScript Project",
            "project_type": "frontend",
            "quality_level": QualityLevel.STANDARD,
            "description": "Testing a React TypeScript project with Jest tests"
        },
        {
            "name": "Express.js API Project", 
            "project_type": "backend",
            "quality_level": QualityLevel.COMPREHENSIVE,
            "description": "Testing an Express.js API with comprehensive test suite"
        }
    ]
    
    qa_agent = QAAgent()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {test_case['name'].upper()}")
        print(f"{'='*60}")
        print(f"Project Type: {test_case['project_type']}")
        print(f"Quality Level: {test_case['quality_level'].value}")
        print(f"Description: {test_case['description']}")
        
        try:
            # Create temporary project structure
            temp_dir = Path(tempfile.mkdtemp())
            project_path = temp_dir / f"test_project_{i}"
            
            # Generate sample project structure
            await create_sample_project(project_path, test_case['project_type'])
            
            # Create QA context
            context = QAContext(
                project_path=project_path,
                quality_level=test_case['quality_level'],
                project_specification=create_sample_specification(test_case['name'])
            )
            
            print(f"\n🔍 Starting QA analysis...")
            
            # Execute QA process
            report = await qa_agent.execute_qa_process(context)
            
            # Display results
            print(f"\n✅ QA Analysis Complete!")
            print(f"   Quality Score: {report.quality_score:.1f}/100")
            print(f"   Test Coverage: {report.overall_coverage:.1f}%")
            print(f"   Requirements Compliance: {report.requirements_compliance:.1f}")
            print(f"   Security Score: {report.security_score:.1f}")
            print(f"   Performance Score: {report.performance_score:.1f}")
            print(f"   Test Suites: {len(report.test_suites)}")
            
            # Show test suite details
            for suite in report.test_suites:
                print(f"   📋 {suite.name}: {suite.passed_tests}/{suite.total_tests} passed")
            
            # Show recommendations
            if report.recommendations:
                print(f"\n💡 Recommendations:")
                for rec in report.recommendations[:3]:  # Show top 3
                    print(f"   • {rec}")
            
            # Save results
            results_file = project_path / "qa_results.json"
            print(f"💾 Results saved to: {results_file}")
            
            # Cleanup
            shutil.rmtree(temp_dir, ignore_errors=True)
            
        except Exception as e:
            print(f"❌ Test case failed: {e}")
            import traceback
            traceback.print_exc()

async def create_sample_project(project_path: Path, project_type: str):
    """Create a sample project structure for testing"""
    project_path.mkdir(parents=True, exist_ok=True)
    
    if project_type == "frontend":
        await create_react_project(project_path)
    elif project_type == "backend":
        await create_express_project(project_path)

async def create_react_project(project_path: Path):
    """Create a sample React TypeScript project"""
    
    # Create package.json
    package_json = {
        "name": "test-react-app",
        "version": "1.0.0",
        "dependencies": {
            "react": "^18.2.0",
            "react-dom": "^18.2.0",
            "typescript": "^5.0.0"
        },
        "devDependencies": {
            "jest": "^29.0.0",
            "@testing-library/react": "^13.4.0",
            "@testing-library/jest-dom": "^5.16.5",
            "@types/react": "^18.0.0"
        },
        "scripts": {
            "test": "jest",
            "test:coverage": "jest --coverage"
        }
    }
    
    with open(project_path / "package.json", 'w') as f:
        json.dump(package_json, f, indent=2)
    
    # Create src directory and files
    src_dir = project_path / "src"
    src_dir.mkdir(exist_ok=True)
    
    # App.tsx
    app_content = '''import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Test React App</h1>
        <p>Generated for QA testing</p>
      </header>
    </div>
  );
}

export default App;'''
    
    with open(src_dir / "App.tsx", 'w') as f:
        f.write(app_content)
    
    # Create test files
    tests_dir = src_dir / "__tests__"
    tests_dir.mkdir(exist_ok=True)
    
    test_content = '''import React from 'react';
import { render, screen } from '@testing-library/react';
import App from '../App';

test('renders test react app', () => {
  render(<App />);
  const linkElement = screen.getByText(/test react app/i);
  expect(linkElement).toBeInTheDocument();
});

test('renders header text', () => {
  render(<App />);
  const headerElement = screen.getByText(/generated for qa testing/i);
  expect(headerElement).toBeInTheDocument();
});'''
    
    with open(tests_dir / "App.test.tsx", 'w') as f:
        f.write(test_content)
    
    # Jest config
    jest_config = '''module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};'''
    
    with open(project_path / "jest.config.js", 'w') as f:
        f.write(jest_config)

async def create_express_project(project_path: Path):
    """Create a sample Express.js project"""
    
    # Create package.json
    package_json = {
        "name": "test-express-api",
        "version": "1.0.0",
        "dependencies": {
            "express": "^4.18.2",
            "cors": "^2.8.5",
            "helmet": "^7.0.0",
            "bcryptjs": "^2.4.3",
            "jsonwebtoken": "^9.0.0"
        },
        "devDependencies": {
            "jest": "^29.0.0",
            "supertest": "^6.3.0",
            "@types/express": "^4.17.17",
            "typescript": "^5.0.0"
        },
        "scripts": {
            "test": "jest",
            "test:coverage": "jest --coverage"
        }
    }
    
    with open(project_path / "package.json", 'w') as f:
        json.dump(package_json, f, indent=2)
    
    # Create src directory
    src_dir = project_path / "src"
    src_dir.mkdir(exist_ok=True)
    
    # app.js
    app_content = '''const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.get('/api/users', (req, res) => {
  res.json({ users: [] });
});

app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password required' });
  }
  res.json({ token: 'mock-jwt-token', user: { email } });
});

module.exports = app;'''
    
    with open(src_dir / "app.js", 'w') as f:
        f.write(app_content)
    
    # Create test files
    tests_dir = project_path / "tests"
    tests_dir.mkdir(exist_ok=True)
    
    test_content = '''const request = require('supertest');
const app = require('../src/app');

describe('API Tests', () => {
  test('GET /health should return OK', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200);
    
    expect(response.body.status).toBe('OK');
    expect(response.body.timestamp).toBeDefined();
  });

  test('GET /api/users should return users array', async () => {
    const response = await request(app)
      .get('/api/users')
      .expect(200);
    
    expect(response.body.users).toEqual([]);
  });

  test('POST /api/auth/login should require email and password', async () => {
    await request(app)
      .post('/api/auth/login')
      .send({})
      .expect(400);
  });

  test('POST /api/auth/login should return token with valid credentials', async () => {
    const response = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' })
      .expect(200);
    
    expect(response.body.token).toBeDefined();
    expect(response.body.user.email).toBe('<EMAIL>');
  });
});'''
    
    with open(tests_dir / "api.test.js", 'w') as f:
        f.write(test_content)

def create_sample_specification(project_name: str) -> ProjectSpecification:
    """Create a sample project specification"""
    return ProjectSpecification(
        project_name=project_name,
        description="A comprehensive web application with modern features",
        requirements={
            "functional": [
                "User authentication system",
                "User interface components",
                "API endpoints for data access",
                "Error handling and validation"
            ]
        },
        user_stories=[
            {
                "id": "US001",
                "title": "User Login",
                "description": "As a user, I want to log in to access the application",
                "acceptance_criteria": ["Valid credentials accepted", "Invalid credentials rejected"]
            },
            {
                "id": "US002",
                "title": "View Dashboard",
                "description": "As a user, I want to view my dashboard after login",
                "acceptance_criteria": ["Dashboard loads after login", "User data displayed"]
            }
        ],
        technical_stack={},
        architecture={},
        constraints=[],
        success_metrics=[],
        risks=[]
    )

async def test_orchestrator_integration():
    """Test QA agent integration with orchestrator"""
    print("\n🔗 Testing Orchestrator Integration...")
    
    try:
        executor = QAAgentExecutor()
        
        # Test with sample data
        result = await executor.execute_qa_agent(
            project_id="test_project_123",
            project_path="./test_developer_output/case_1",  # Use existing generated project
            quality_level="standard"
        )
        
        print(f"✅ Orchestrator integration test complete!")
        print(f"   Success: {result['success']}")
        if result['success']:
            print(f"   Quality Score: {result['quality_score']:.1f}")
            print(f"   Coverage: {result['overall_coverage']:.1f}%")
            print(f"   Test Suites: {result['test_suites']}")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Orchestrator integration test failed: {e}")

def print_usage():
    """Print usage information"""
    print("""
🧪 QA Agent Test Suite

Usage:
    python test_qa_agent.py [command]

Commands:
    test        Run comprehensive QA testing (default)
    integration Test orchestrator integration
    help        Show this help message

Examples:
    python test_qa_agent.py test
    python test_qa_agent.py integration

The QA agent will:
1. 🔍 Analyze project structure and code quality
2. 🧪 Execute unit, integration, and E2E tests
3. 🔒 Perform security vulnerability analysis
4. ⚡ Analyze performance characteristics
5. 📋 Validate against original requirements
6. 📊 Generate comprehensive quality reports
7. 💡 Provide improvement recommendations

Quality metrics include:
- Test coverage analysis
- Code quality assessment
- Security vulnerability scanning
- Performance optimization review
- Requirements compliance validation
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "integration":
        await test_orchestrator_integration()
    elif command == "test":
        await test_qa_agent()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
