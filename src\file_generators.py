"""
File Generators for Project Generation
Contains all the file generation methods for different project types and configurations
"""

import json
from typing import Dict, Any
from .project_types import ProjectType, ProjectTemplate


class FileGenerators:
    """Collection of file generation methods"""
    
    @staticmethod
    async def generate_package_json(project_name: str, template: ProjectTemplate) -> str:
        """Generate package.json for Node.js projects"""
        
        package_data = {
            "name": project_name.lower().replace(" ", "-"),
            "version": "1.0.0",
            "description": f"Generated {template.name} project",
            "main": "src/index.js" if template.project_type == ProjectType.API_SERVICE else "src/App.tsx",
            "scripts": {
                "dev": "vite" if template.project_type == ProjectType.WEB_APPLICATION else "nodemon src/server.ts",
                "build": "vite build" if template.project_type == ProjectType.WEB_APPLICATION else "tsc",
                "start": "node dist/server.js" if template.project_type == ProjectType.API_SERVICE else "vite preview",
                "test": "jest",
                "lint": "eslint src --ext .ts,.tsx,.js,.jsx",
                "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix",
                "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}"
            },
            "dependencies": {},
            "devDependencies": {},
            "keywords": ["aetherforge", "generated", template.project_type.value],
            "author": "Aetherforge AI",
            "license": "MIT"
        }
        
        # Add dependencies based on template
        for dep_type, deps in template.dependencies.items():
            if dep_type in ["runtime", "frontend", "backend"]:
                for dep in deps:
                    package_data["dependencies"][dep] = "latest"
            else:
                for dep in deps:
                    package_data["devDependencies"][dep] = "latest"
        
        return json.dumps(package_data, indent=2)
    
    @staticmethod
    async def generate_dockerfile(project_type: ProjectType) -> str:
        """Generate Dockerfile based on project type"""
        
        if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE, ProjectType.MOBILE_APPLICATION]:
            return """# Node.js Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Start application
CMD ["npm", "start"]
"""
        elif project_type == ProjectType.DATA_PLATFORM:
            return """# Python Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Expose port
EXPOSE 8000

# Start application
CMD ["python", "src/main.py"]
"""
        else:
            return """# Generic Dockerfile
FROM alpine:latest

WORKDIR /app

COPY . .

CMD ["echo", "Application ready"]
"""
    
    @staticmethod
    async def generate_docker_compose(project_type: ProjectType, template: ProjectTemplate) -> str:
        """Generate docker-compose.yml"""
        
        if project_type == ProjectType.WEB_APPLICATION:
            return """version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: app_db
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: app_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
"""
        elif project_type == ProjectType.API_SERVICE:
            return """version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************/api_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: api_db
      POSTGRES_USER: api_user
      POSTGRES_PASSWORD: api_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
"""
        elif project_type == ProjectType.DATA_PLATFORM:
            return """version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/data_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
      - elasticsearch

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: data_db
      POSTGRES_USER: data_user
      POSTGRES_PASSWORD: data_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
  elasticsearch_data:
"""
        else:
            return """version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
"""
    
    @staticmethod
    async def generate_gitignore(project_type: ProjectType) -> str:
        """Generate .gitignore file"""
        
        base_ignore = """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Temporary folders
tmp/
temp/
"""
        
        if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE, ProjectType.MOBILE_APPLICATION]:
            return base_ignore + """
# Node.js specific
.npm
.eslintcache

# React/Next.js specific
.next/
.vercel/

# TypeScript
*.tsbuildinfo

# Storybook
storybook-static/
"""
        elif project_type == ProjectType.DATA_PLATFORM:
            return base_ignore + """
# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# Data files
data/raw/
data/processed/
*.csv
*.parquet
*.h5

# ML models
models/
*.pkl
*.joblib

# Airflow
airflow.cfg
airflow.db
"""
        else:
            return base_ignore
    
    @staticmethod
    async def generate_readme(project_name: str, prompt: str, project_id: str, 
                            project_type: ProjectType, template: ProjectTemplate) -> str:
        """Generate comprehensive README.md"""
        
        return f"""# {project_name}

{prompt}

## 🤖 Generated by Aetherforge

This project was autonomously generated by Aetherforge AI agents using the **{template.name}** template.

**Project ID**: `{project_id}`  
**Project Type**: `{project_type.value}`  
**Created**: {template.name}  
**Status**: 🔄 **Generating...**

## 📁 Project Structure

```
{project_name}/
├── src/                 # Source code
├── docs/               # Documentation
├── tests/              # Test files
├── config/             # Configuration files
├── scripts/            # Build and deployment scripts
└── README.md          # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ (for web/API projects)
- Python 3.11+ (for data projects)
- Docker (optional)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd {project_name.lower().replace(' ', '-')}

# Install dependencies
{"npm install" if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else "pip install -r requirements.txt"}

# Start development server
{"npm run dev" if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else "python src/main.py"}
```

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up --build
```

## 🧪 Testing

```bash
# Run tests
{"npm test" if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else "pytest"}

# Run with coverage
{"npm run test:coverage" if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else "pytest --cov"}
```

## 📚 Documentation

- [API Documentation](docs/api/README.md)
- [User Guide](docs/user-guide/getting-started.md)
- [Architecture](docs/architecture.md)
- [Deployment](docs/deployment.md)

## 🛠️ Development

### Available Scripts

{chr(10).join(f"- `{cmd}` - {desc}" for cmd, desc in [
    ("npm run dev", "Start development server") if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else ("python src/main.py", "Start application"),
    ("npm run build", "Build for production") if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else ("python setup.py build", "Build package"),
    ("npm test", "Run tests") if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else ("pytest", "Run tests"),
    ("npm run lint", "Lint code") if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE] else ("flake8 src/", "Lint code")
])}

### Code Quality

This project includes:

- ✅ ESLint/Flake8 for code linting
- ✅ Prettier/Black for code formatting
- ✅ Jest/Pytest for testing
- ✅ Husky for git hooks
- ✅ CI/CD pipeline

## 🚀 Deployment

See [deployment documentation](docs/deployment.md) for detailed deployment instructions.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📞 Support

For support and questions, please refer to the documentation or create an issue in the repository.

---

*Generated with ❤️ by [Aetherforge](https://github.com/aetherforge) - Autonomous AI Development Platform*
"""

    @staticmethod
    async def generate_license() -> str:
        """Generate MIT License"""

        return """MIT License

Copyright (c) 2024 Aetherforge AI

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""

    @staticmethod
    async def generate_env_example(project_type: ProjectType) -> str:
        """Generate .env.example file"""

        base_env = """# Environment Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# API Keys
API_KEY=your-api-key-here
"""

        if project_type == ProjectType.WEB_APPLICATION:
            return base_env + """
# Frontend Configuration
VITE_API_URL=http://localhost:8000/api
VITE_APP_NAME=Your App Name

# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
"""
        elif project_type == ProjectType.API_SERVICE:
            return base_env + """
# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
"""
        elif project_type == ProjectType.DATA_PLATFORM:
            return """# Data Platform Configuration
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/data_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200

# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-data-bucket

# ML Configuration
MLFLOW_TRACKING_URI=http://localhost:5000
WANDB_API_KEY=your-wandb-key

# Airflow Configuration
AIRFLOW_HOME=/opt/airflow
AIRFLOW__CORE__EXECUTOR=LocalExecutor
"""
        else:
            return base_env

    @staticmethod
    async def generate_tsconfig(project_type: ProjectType) -> str:
        """Generate TypeScript configuration"""

        base_config = {
            "compilerOptions": {
                "target": "ES2020",
                "lib": ["ES2020", "DOM", "DOM.Iterable"],
                "allowJs": True,
                "skipLibCheck": True,
                "esModuleInterop": True,
                "allowSyntheticDefaultImports": True,
                "strict": True,
                "forceConsistentCasingInFileNames": True,
                "moduleResolution": "node",
                "resolveJsonModule": True,
                "isolatedModules": True,
                "noEmit": False,
                "declaration": True,
                "outDir": "./dist",
                "rootDir": "./src"
            },
            "include": ["src/**/*"],
            "exclude": ["node_modules", "dist", "build"]
        }

        if project_type == ProjectType.WEB_APPLICATION:
            base_config["compilerOptions"].update({
                "jsx": "react-jsx",
                "module": "ESNext",
                "noEmit": True
            })
            base_config["compilerOptions"]["lib"].append("ES6")

        elif project_type == ProjectType.API_SERVICE:
            base_config["compilerOptions"].update({
                "module": "CommonJS",
                "types": ["node", "jest"]
            })

        return json.dumps(base_config, indent=2)

    @staticmethod
    async def generate_eslint_config(project_type: ProjectType) -> str:
        """Generate ESLint configuration"""

        base_config = {
            "env": {
                "browser": True,
                "es2021": True,
                "node": True,
                "jest": True
            },
            "extends": [
                "eslint:recommended",
                "@typescript-eslint/recommended"
            ],
            "parser": "@typescript-eslint/parser",
            "parserOptions": {
                "ecmaVersion": 12,
                "sourceType": "module"
            },
            "plugins": ["@typescript-eslint"],
            "rules": {
                "indent": ["error", 2],
                "linebreak-style": ["error", "unix"],
                "quotes": ["error", "single"],
                "semi": ["error", "always"],
                "@typescript-eslint/no-unused-vars": "error",
                "@typescript-eslint/explicit-function-return-type": "warn"
            }
        }

        if project_type == ProjectType.WEB_APPLICATION:
            base_config["extends"].extend([
                "plugin:react/recommended",
                "plugin:react-hooks/recommended"
            ])
            base_config["plugins"].extend(["react", "react-hooks"])
            base_config["parserOptions"]["ecmaFeatures"] = {"jsx": True}
            base_config["settings"] = {"react": {"version": "detect"}}

        return json.dumps(base_config, indent=2)

    @staticmethod
    async def generate_prettier_config() -> str:
        """Generate Prettier configuration"""

        config = {
            "semi": True,
            "trailingComma": "es5",
            "singleQuote": True,
            "printWidth": 80,
            "tabWidth": 2,
            "useTabs": False
        }

        return json.dumps(config, indent=2)

    @staticmethod
    async def generate_jest_config(project_type: ProjectType) -> str:
        """Generate Jest configuration"""

        if project_type == ProjectType.WEB_APPLICATION:
            return """module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
"""
        else:
            return """module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
"""
