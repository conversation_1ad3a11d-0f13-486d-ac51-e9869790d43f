#!/usr/bin/env python3
"""
Test script for the Architect Agent
Demonstrates the comprehensive architecture design capabilities
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from architect_agent import ArchitectAgent, SystemArchitecture, ArchitecturePattern, ScalabilityTier
from analyst_config import AnalystIntegration

async def test_architect_agent():
    """Test the architect agent with sample analyst output"""
    
    # Sample analyst output (simulating what the analyst agent would produce)
    sample_analyst_outputs = [
        {
            "project_name": "E-commerce Platform",
            "description": "Create a comprehensive e-commerce platform with user authentication, product catalog, shopping cart, payment processing, and order management",
            "functional_requirements": [
                {
                    "id": "REQ-001",
                    "title": "User Authentication",
                    "description": "System shall provide user registration and login functionality",
                    "priority": "High",
                    "category": "Authentication"
                },
                {
                    "id": "REQ-002",
                    "title": "Product Catalog",
                    "description": "System shall provide product browsing and search functionality",
                    "priority": "High",
                    "category": "Core Features"
                },
                {
                    "id": "REQ-003",
                    "title": "Shopping Cart",
                    "description": "System shall provide shopping cart management",
                    "priority": "High",
                    "category": "Core Features"
                },
                {
                    "id": "REQ-004",
                    "title": "Payment Processing",
                    "description": "System shall process payments securely",
                    "priority": "High",
                    "category": "Payment"
                }
            ],
            "non_functional_requirements": [
                {
                    "id": "NFR-001",
                    "title": "Performance",
                    "description": "System shall respond to user requests within 2 seconds",
                    "category": "Performance",
                    "metric": "Response time < 2s"
                },
                {
                    "id": "NFR-002",
                    "title": "Scalability",
                    "description": "System shall support 10,000 concurrent users",
                    "category": "Scalability",
                    "metric": "Support 10,000+ concurrent users"
                }
            ],
            "user_stories": [
                {
                    "id": "US-001",
                    "title": "User Registration",
                    "role": "customer",
                    "action": "register for an account",
                    "benefit": "I can make purchases and track orders",
                    "priority": "High",
                    "story_points": 8
                },
                {
                    "id": "US-002",
                    "title": "Product Search",
                    "role": "customer",
                    "action": "search for products",
                    "benefit": "I can find items I want to purchase",
                    "priority": "High",
                    "story_points": 13
                },
                {
                    "id": "US-003",
                    "title": "Real-time Inventory",
                    "role": "customer",
                    "action": "see real-time inventory status",
                    "benefit": "I know if items are available",
                    "priority": "Medium",
                    "story_points": 8
                }
            ],
            "technical_preferences": {
                "frontend": {
                    "framework": "React",
                    "language": "TypeScript"
                },
                "backend": {
                    "runtime": "Node.js",
                    "framework": "Express.js"
                },
                "database": {
                    "primary": "PostgreSQL"
                }
            },
            "constraints": [
                "Development timeline must accommodate MVP delivery",
                "Budget constraints may limit third-party service usage",
                "Team has React and Node.js expertise"
            ],
            "success_metrics": [
                "Support 10,000+ concurrent users",
                "Page load times < 2 seconds",
                "99.9% uptime",
                "Customer satisfaction > 4.0/5.0"
            ]
        },
        {
            "project_name": "Task Management API",
            "description": "Build a REST API for task management with user authentication, project organization, and team collaboration",
            "functional_requirements": [
                {
                    "id": "REQ-001",
                    "title": "API Authentication",
                    "description": "API shall provide JWT-based authentication",
                    "priority": "High",
                    "category": "Authentication"
                },
                {
                    "id": "REQ-002",
                    "title": "Task CRUD Operations",
                    "description": "API shall provide task creation, reading, updating, and deletion",
                    "priority": "High",
                    "category": "Core Features"
                }
            ],
            "non_functional_requirements": [
                {
                    "id": "NFR-001",
                    "title": "API Performance",
                    "description": "API shall respond within 500ms",
                    "category": "Performance",
                    "metric": "Response time < 500ms"
                }
            ],
            "user_stories": [
                {
                    "id": "US-001",
                    "title": "API Authentication",
                    "role": "developer",
                    "action": "authenticate with the API",
                    "benefit": "I can access protected resources",
                    "priority": "High",
                    "story_points": 5
                }
            ],
            "technical_preferences": {
                "backend": {
                    "runtime": "Node.js",
                    "framework": "Express.js"
                },
                "database": {
                    "primary": "PostgreSQL"
                }
            },
            "constraints": [
                "API-only service, no frontend",
                "Must be RESTful and well-documented"
            ],
            "success_metrics": [
                "API response times < 500ms",
                "99.5% uptime",
                "Comprehensive API documentation"
            ]
        }
    ]
    
    architect = ArchitectAgent()
    
    for i, analyst_output in enumerate(sample_analyst_outputs, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {analyst_output['project_name'].upper()}")
        print(f"{'='*60}")
        print(f"Description: {analyst_output['description']}")
        
        try:
            # Design architecture
            print("\n🏗️ Starting architecture design...")
            architecture = await architect.design_architecture(analyst_output)
            
            print(f"✅ Architecture design complete!")
            print(f"   Project: {architecture.project_name}")
            print(f"   Pattern: {architecture.architecture_pattern.value}")
            print(f"   Scalability: {architecture.scalability_tier.value}")
            print(f"   Components: {len(architecture.components)}")
            print(f"   Technology Stack Categories: {len(architecture.technology_stack)}")
            
            # Display architecture summary
            print(f"\n🏗️ Architecture Summary:")
            print(f"   Pattern: {architecture.architecture_pattern.value.replace('_', ' ').title()}")
            print(f"   Scalability Tier: {architecture.scalability_tier.value.title()}")
            
            # Display components
            print(f"\n📦 System Components:")
            for component in architecture.components:
                print(f"   - {component.name}: {len(component.responsibilities)} responsibilities")
            
            # Display technology choices
            print(f"\n🛠️ Technology Stack:")
            for category, technologies in architecture.technology_stack.items():
                if technologies:
                    tech_names = [f"{tech.name} {tech.version}" for tech in technologies]
                    print(f"   {category.title()}: {', '.join(tech_names)}")
            
            # Display quality attributes
            print(f"\n📊 Quality Attributes:")
            for attr, details in architecture.quality_attributes.items():
                print(f"   {attr.title()}: {len(details)} requirements")
            
            # Generate documentation
            print(f"\n📄 Generating architecture documentation...")
            output_path = Path(f"test_architect_output/case_{i}")
            generated_files = await architect.generate_architecture_documents(architecture, output_path)
            
            print(f"✅ Generated {len(generated_files)} documentation files:")
            for file in generated_files:
                print(f"   - {file}")
            
            # Save architecture as JSON for integration testing
            arch_json_path = output_path / "architecture.json"
            arch_json_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert architecture to JSON-serializable format
            arch_dict = {
                "project_name": architecture.project_name,
                "architecture_pattern": architecture.architecture_pattern.value,
                "scalability_tier": architecture.scalability_tier.value,
                "components": [
                    {
                        "name": comp.name,
                        "description": comp.description,
                        "responsibilities": comp.responsibilities,
                        "technologies": [
                            {
                                "name": tech.name,
                                "version": tech.version,
                                "category": tech.category,
                                "justification": tech.justification
                            } for tech in comp.technologies
                        ]
                    } for comp in architecture.components
                ],
                "technology_stack": {
                    category: [
                        {
                            "name": tech.name,
                            "version": tech.version,
                            "category": tech.category,
                            "justification": tech.justification,
                            "learning_curve": tech.learning_curve,
                            "community_support": tech.community_support
                        } for tech in technologies
                    ] for category, technologies in architecture.technology_stack.items()
                },
                "quality_attributes": architecture.quality_attributes,
                "constraints": architecture.constraints,
                "assumptions": architecture.assumptions,
                "risks": architecture.risks
            }
            
            with open(arch_json_path, 'w') as f:
                json.dump(arch_dict, f, indent=2)
            
            print(f"💾 Architecture saved to: {arch_json_path}")
            
        except Exception as e:
            print(f"❌ Test case {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎉 All test cases completed!")
    print("📁 Check the test_architect_output/ directory for generated documentation")
    print(f"{'='*60}")

async def test_integration_with_analyst():
    """Test integration between analyst and architect agents"""
    print("\n🔗 Testing Analyst-Architect Integration...")
    
    try:
        # Import analyst agent
        from analyst_agent import AnalystAgent
        
        # Create a sample prompt
        prompt = "Create a social media platform with user profiles, posts, comments, likes, and real-time notifications"
        
        print(f"📝 Analyzing prompt: {prompt}")
        
        # Run analyst agent
        analyst = AnalystAgent()
        specification = await analyst.analyze_prompt(prompt, "web_application")
        
        print(f"✅ Analyst complete: {specification.project_name}")
        
        # Format for architect
        architect_input = AnalystIntegration.format_for_architect(specification)
        
        print(f"🔄 Formatted for architect agent")
        
        # Run architect agent
        architect = ArchitectAgent()
        architecture = await architect.design_architecture(architect_input)
        
        print(f"✅ Architect complete: {architecture.project_name}")
        print(f"   Pattern: {architecture.architecture_pattern.value}")
        print(f"   Components: {len(architecture.components)}")
        
        # Generate combined documentation
        output_path = Path("integration_test_output")
        
        # Generate analyst docs
        analyst_files = await analyst.generate_project_documents(specification, output_path)
        print(f"📄 Generated {len(analyst_files)} analyst documents")
        
        # Generate architect docs
        architect_files = await architect.generate_architecture_documents(architecture, output_path)
        print(f"📄 Generated {len(architect_files)} architect documents")
        
        print(f"🎉 Integration test complete! Check {output_path} for all documents.")
        
    except ImportError:
        print("❌ Analyst agent not available for integration test")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

def print_usage():
    """Print usage information"""
    print("""
🏗️ Architect Agent Test Suite

Usage:
    python test_architect_agent.py [command]

Commands:
    test        Run comprehensive architecture design tests (default)
    integration Test integration with analyst agent
    help        Show this help message

Examples:
    python test_architect_agent.py test
    python test_architect_agent.py integration

The architect agent will:
1. 🏗️ Analyze analyst output and requirements
2. 🎯 Select appropriate architecture patterns
3. 🛠️ Choose and justify technology stack
4. 📦 Design detailed system components
5. 🔒 Design security and data architecture
6. 📄 Generate comprehensive architecture documentation

Generated files include:
- System Architecture Document
- Technology Selection Document
- Component Specifications
- Deployment Architecture
- Security Architecture
- Data Architecture
- Integration Patterns
- Quality Attributes
- Architecture Decision Records
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "integration":
        await test_integration_with_analyst()
    elif command == "test":
        await test_architect_agent()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
