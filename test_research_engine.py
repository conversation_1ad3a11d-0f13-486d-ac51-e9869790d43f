#!/usr/bin/env python3
"""
Test script for the Research Engine
Demonstrates comprehensive research and crawling capabilities
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from research_engine import (
    ResearchEngine, 
    ResearchQuery, 
    ResearchContext, 
    CrawlRequest,
    ResearchType, 
    ResearchPriority,
    ResearchEngineExecutor
)

async def test_research_engine():
    """Test the research engine with sample queries"""
    print("🔬 Testing Research Engine Capabilities")
    print("=" * 60)
    
    # Create test cases
    test_cases = [
        {
            "name": "React Web Application Research",
            "project_type": "web_application",
            "technology_stack": ["React", "TypeScript", "Node.js"],
            "description": "Research for a modern React web application"
        },
        {
            "name": "Python API Service Research",
            "project_type": "api_service", 
            "technology_stack": ["Python", "FastAPI", "PostgreSQL"],
            "description": "Research for a Python-based API service"
        },
        {
            "name": "Mobile App Development Research",
            "project_type": "mobile_application",
            "technology_stack": ["React Native", "TypeScript"],
            "description": "Research for cross-platform mobile development"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {test_case['name'].upper()}")
        print(f"{'='*60}")
        print(f"Project Type: {test_case['project_type']}")
        print(f"Technology Stack: {', '.join(test_case['technology_stack'])}")
        print(f"Description: {test_case['description']}")
        
        try:
            # Create research queries
            queries = [
                ResearchQuery(
                    query=f"{test_case['technology_stack'][0]} best practices",
                    research_type=ResearchType.BEST_PRACTICES,
                    priority=ResearchPriority.HIGH,
                    max_results=5
                ),
                ResearchQuery(
                    query=f"{test_case['technology_stack'][0]} code examples",
                    research_type=ResearchType.CODE_EXAMPLES,
                    priority=ResearchPriority.MEDIUM,
                    max_results=3,
                    include_code=True
                ),
                ResearchQuery(
                    query=f"{test_case['project_type']} architecture patterns",
                    research_type=ResearchType.ARCHITECTURE,
                    priority=ResearchPriority.HIGH,
                    max_results=5
                )
            ]
            
            # Create research context
            context = ResearchContext(
                project_type=test_case['project_type'],
                technology_stack=test_case['technology_stack'],
                requirements=["scalable", "secure", "maintainable"],
                complexity_level="intermediate"
            )
            
            print(f"\n🔍 Starting research with {len(queries)} queries...")
            
            # Execute research
            async with ResearchEngine() as engine:
                results = await engine.conduct_research(queries, context)
                
                # Display results
                print(f"\n✅ Research Complete!")
                total_results = sum(len(query_results) for query_results in results.values())
                print(f"   Total Results: {total_results}")
                print(f"   Queries Processed: {len(results)}")
                
                # Show detailed results for each query
                for query, query_results in results.items():
                    print(f"\n📋 Query: {query}")
                    print(f"   Results: {len(query_results)}")
                    
                    for j, result in enumerate(query_results[:2], 1):  # Show top 2 results
                        print(f"   {j}. {result.source_domain} (Score: {result.relevance_score:.2f})")
                        print(f"      Type: {result.research_type.value}")
                        if result.code_examples:
                            print(f"      Code Examples: {len(result.code_examples)}")
                        print(f"      Content: {result.content[:100]}...")
                
        except Exception as e:
            print(f"❌ Test case failed: {e}")
            import traceback
            traceback.print_exc()

async def test_crawling_capabilities():
    """Test crawling and indexing capabilities"""
    print("\n🕷️ Testing Crawling Capabilities")
    print("=" * 60)
    
    # Test URLs for crawling
    test_urls = [
        {
            "url": "https://reactjs.org/docs/getting-started.html",
            "type": "single",
            "description": "React documentation page"
        },
        {
            "url": "https://fastapi.tiangolo.com/",
            "type": "smart",
            "description": "FastAPI documentation site"
        }
    ]
    
    try:
        async with ResearchEngine() as engine:
            # Create crawl requests
            crawl_requests = []
            for url_info in test_urls:
                request = CrawlRequest(
                    url=url_info["url"],
                    crawl_type=url_info["type"],
                    max_pages=5,
                    include_code=True
                )
                crawl_requests.append(request)
            
            print(f"🔍 Starting crawl operation for {len(crawl_requests)} URLs...")
            
            # Execute crawling
            crawl_results = await engine.crawl_and_index(crawl_requests)
            
            # Display results
            print(f"\n✅ Crawling Complete!")
            print(f"   Total Requests: {crawl_results['total_requests']}")
            print(f"   Successful Crawls: {crawl_results['successful_crawls']}")
            print(f"   Failed Crawls: {crawl_results['failed_crawls']}")
            
            # Show detailed results
            for url, result in crawl_results['results'].items():
                print(f"\n📄 URL: {url}")
                if result['success']:
                    print(f"   ✅ Success")
                    print(f"   Pages Crawled: {result.get('pages_crawled', 0)}")
                    print(f"   Content Indexed: {result.get('content_indexed', False)}")
                    print(f"   Source ID: {result.get('source_id', 'N/A')}")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    
    except Exception as e:
        print(f"❌ Crawling test failed: {e}")

async def test_knowledge_base_search():
    """Test knowledge base search functionality"""
    print("\n🔍 Testing Knowledge Base Search")
    print("=" * 60)
    
    search_queries = [
        "React hooks best practices",
        "Python async programming",
        "API authentication methods",
        "database optimization techniques"
    ]
    
    try:
        async with ResearchEngine() as engine:
            # Get available sources
            sources = await engine.get_available_sources()
            print(f"📚 Available Sources: {len(sources)}")
            if sources:
                print(f"   Sample sources: {sources[:3]}")
            
            # Test search queries
            for query in search_queries:
                print(f"\n🔍 Searching: {query}")
                
                results = await engine.search_knowledge_base(
                    query=query,
                    max_results=3,
                    include_code=True
                )
                
                print(f"   Results: {len(results)}")
                for i, result in enumerate(results, 1):
                    print(f"   {i}. {result.source_domain} (Score: {result.relevance_score:.2f})")
                    print(f"      Type: {result.research_type.value}")
                    if result.code_examples:
                        print(f"      Has Code Examples: Yes")
                        
    except Exception as e:
        print(f"❌ Knowledge base search test failed: {e}")

async def test_orchestrator_integration():
    """Test research engine integration with orchestrator"""
    print("\n🔗 Testing Orchestrator Integration")
    print("=" * 60)
    
    try:
        executor = ResearchEngineExecutor()
        
        # Test research execution
        research_queries = [
            {
                "query": "modern web development frameworks",
                "research_type": "documentation",
                "priority": "high",
                "max_results": 5,
                "include_code": True
            },
            {
                "query": "API security best practices",
                "research_type": "best_practices", 
                "priority": "critical",
                "max_results": 3,
                "include_code": False
            }
        ]
        
        context_data = {
            "project_type": "web_application",
            "technology_stack": ["React", "Node.js", "Express"],
            "requirements": ["scalable", "secure"],
            "constraints": ["budget", "timeline"]
        }
        
        result = await executor.execute_research(
            project_id="test_project_123",
            research_queries=research_queries,
            context_data=context_data
        )
        
        print(f"✅ Orchestrator integration test complete!")
        print(f"   Success: {result['success']}")
        if result['success']:
            print(f"   Total Queries: {result['total_queries']}")
            print(f"   Total Results: {result['total_results']}")
            print(f"   Results Keys: {list(result['results'].keys())}")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Orchestrator integration test failed: {e}")

def print_usage():
    """Print usage information"""
    print("""
🔬 Research Engine Test Suite

Usage:
    python test_research_engine.py [command]

Commands:
    test        Run comprehensive research tests (default)
    crawl       Test crawling capabilities only
    search      Test knowledge base search only
    integration Test orchestrator integration
    help        Show this help message

Examples:
    python test_research_engine.py test
    python test_research_engine.py crawl
    python test_research_engine.py search

The Research Engine will:
1. 🔍 Connect to MCP-Crawl4AI-RAG service
2. 🕷️ Crawl and index documentation
3. 📚 Build searchable knowledge base
4. 🎯 Perform context-aware research
5. 💡 Provide intelligent recommendations
6. 🔗 Integrate with Aetherforge orchestrator

Research capabilities include:
- Live documentation crawling
- Code example extraction
- Best practices research
- Architecture pattern discovery
- Security guideline analysis
- Performance optimization research
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "crawl":
        await test_crawling_capabilities()
    elif command == "search":
        await test_knowledge_base_search()
    elif command == "integration":
        await test_orchestrator_integration()
    elif command == "test":
        await test_research_engine()
        await test_crawling_capabilities()
        await test_knowledge_base_search()
        await test_orchestrator_integration()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
