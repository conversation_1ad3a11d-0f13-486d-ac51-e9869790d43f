# Quick Start Tutorial: Build a Task Management App

In this tutorial, you'll learn how to use TaoForge to create a complete task management application in under 30 minutes. No coding required - just describe what you want, and watch <PERSON>For<PERSON> build it for you!

## 🎯 What You'll Build

By the end of this tutorial, you'll have:
- A fully functional task management web application
- User authentication and registration
- Task creation, editing, and deletion
- Task categories and priorities
- Real-time updates
- Mobile-responsive design
- Complete backend API
- Database with proper schemas
- Deployment-ready configuration

## 📋 Prerequisites

Before starting, ensure you have:
- TaoForge installed and configured ([Installation Guide](../installation.md))
- VS Code with TaoForge extension (recommended)
- Basic understanding of web applications
- 30 minutes of time

## 🚀 Step 1: Project Planning

### Define Your Requirements

Let's start by clearly defining what we want to build:

**Project Vision:**
> "A task management application where users can create accounts, organize tasks into categories, set priorities and due dates, mark tasks as complete, and collaborate with team members."

**Key Features:**
- User authentication (register, login, logout)
- Task CRUD operations (Create, Read, Update, Delete)
- Task categories and tags
- Priority levels (Low, Medium, High, Urgent)
- Due dates and reminders
- Task status tracking (Todo, In Progress, Complete)
- Team collaboration (share tasks, assign to users)
- Dashboard with analytics
- Mobile-responsive design

### Choose Your Tech Stack

For this tutorial, we'll use:
- **Frontend:** React with TypeScript
- **Backend:** Node.js with Express
- **Database:** PostgreSQL
- **Authentication:** JWT tokens
- **Styling:** Tailwind CSS
- **Real-time:** WebSocket for live updates

## 🎨 Step 2: Create the Project

### Method 1: Using VS Code Extension (Recommended)

1. **Open VS Code**
2. **Press `Ctrl+Shift+P`** (or `Cmd+Shift+P` on Mac)
3. **Type "TaoForge: Create Project"** and press Enter
4. **Fill in the project details:**

```
Project Name: TaskMaster
Project Type: fullstack
Description: A comprehensive task management application with user authentication, task organization, team collaboration, and real-time updates. Users can create tasks with categories, priorities, and due dates. Include a dashboard with analytics and mobile-responsive design.
```

5. **Configure preferences:**
   - Frontend Framework: React with TypeScript
   - Backend Framework: Node.js with Express
   - Database: PostgreSQL
   - Styling: Tailwind CSS
   - Authentication: JWT

6. **Click "Create Project"**

### Method 2: Using Command Line

```bash
taoforge create "A comprehensive task management application with user authentication, task organization, team collaboration, and real-time updates. Users can create tasks with categories, priorities, and due dates. Include a dashboard with analytics and mobile-responsive design." \
  --name TaskMaster \
  --type fullstack \
  --frontend react-typescript \
  --backend nodejs-express \
  --database postgresql \
  --auth jwt \
  --styling tailwind
```

### Method 3: Using Web Interface

1. Open http://localhost:8000 in your browser
2. Click "Create New Project"
3. Fill in the form with the details above
4. Click "Generate Project"

## 📊 Step 3: Monitor the Development Process

### Watch the BMAD Process

TaoForge follows the BMAD methodology. You'll see four phases:

#### Phase 1: Business Analysis (3-5 minutes)
The Analyst Agent will:
- Parse your requirements
- Create detailed user stories
- Define acceptance criteria
- Plan the feature set

**What you'll see:**
```
📋 Analyst Agent: Analyzing requirements...
✅ Created user stories (12 stories)
✅ Defined acceptance criteria
✅ Planned feature architecture
✅ Generated requirements.md
```

#### Phase 2: Model & Architecture Design (5-8 minutes)
The Architect Agent will:
- Design system architecture
- Create database schemas
- Plan API endpoints
- Select optimal technologies

**What you'll see:**
```
🏗️ Architect Agent: Designing system architecture...
✅ Created database schema (5 tables)
✅ Designed REST API (15 endpoints)
✅ Planned component architecture
✅ Generated architecture.md
```

#### Phase 3: Development (15-20 minutes)
The Developer Agent will:
- Generate frontend components
- Create backend services
- Implement authentication
- Set up database migrations

**What you'll see:**
```
💻 Developer Agent: Implementing application...
✅ Created React components (18 components)
✅ Implemented Express routes (15 routes)
✅ Set up authentication system
✅ Created database migrations
✅ Configured WebSocket server
```

#### Phase 4: Quality Assurance (3-5 minutes)
The QA Agent will:
- Generate test suites
- Validate code quality
- Create deployment configuration
- Ensure requirements are met

**What you'll see:**
```
🧪 QA Agent: Validating and testing...
✅ Generated unit tests (45 tests)
✅ Created integration tests (12 tests)
✅ Validated code quality
✅ Generated deployment config
```

## 📁 Step 4: Explore the Generated Project

After completion, your project structure will look like this:

```
TaskMaster/
├── 📄 README.md                    # Project documentation
├── 📄 package.json                 # Root dependencies
├── 📄 docker-compose.yml           # Development environment
├── 📄 .env.example                 # Environment variables template
├── 📁 docs/                        # Generated documentation
│   ├── requirements.md             # Business requirements
│   ├── architecture.md             # Technical architecture
│   ├── api-documentation.md        # API reference
│   └── deployment.md               # Deployment guide
├── 📁 client/                      # React frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── TaskCard.tsx        # Individual task display
│   │   │   ├── TaskForm.tsx        # Task creation/editing
│   │   │   ├── TaskList.tsx        # Task listing
│   │   │   ├── Dashboard.tsx       # Analytics dashboard
│   │   │   └── Navigation.tsx      # App navigation
│   │   ├── pages/
│   │   │   ├── Login.tsx           # Login page
│   │   │   ├── Register.tsx        # Registration page
│   │   │   ├── Tasks.tsx           # Main tasks page
│   │   │   └── Profile.tsx         # User profile
│   │   ├── services/
│   │   │   ├── api.ts              # API client
│   │   │   ├── auth.ts             # Authentication
│   │   │   └── websocket.ts        # Real-time updates
│   │   ├── hooks/
│   │   │   ├── useAuth.ts          # Authentication hook
│   │   │   ├── useTasks.ts         # Task management hook
│   │   │   └── useWebSocket.ts     # WebSocket hook
│   │   └── types/
│   │       ├── Task.ts             # Task type definitions
│   │       └── User.ts             # User type definitions
│   ├── public/
│   └── package.json
├── 📁 server/                      # Node.js backend
│   ├── src/
│   │   ├── routes/
│   │   │   ├── auth.js             # Authentication routes
│   │   │   ├── tasks.js            # Task management routes
│   │   │   ├── users.js            # User management routes
│   │   │   └── analytics.js        # Analytics routes
│   │   ├── models/
│   │   │   ├── User.js             # User model
│   │   │   ├── Task.js             # Task model
│   │   │   └── Category.js         # Category model
│   │   ├── middleware/
│   │   │   ├── auth.js             # Authentication middleware
│   │   │   ├── validation.js       # Input validation
│   │   │   └── errorHandler.js     # Error handling
│   │   ├── services/
│   │   │   ├── authService.js      # Authentication logic
│   │   │   ├── taskService.js      # Task business logic
│   │   │   └── emailService.js     # Email notifications
│   │   └── utils/
│   │       ├── database.js         # Database connection
│   │       └── websocket.js        # WebSocket server
│   ├── tests/
│   └── package.json
├── 📁 database/                    # Database configuration
│   ├── migrations/
│   │   ├── 001_create_users.sql
│   │   ├── 002_create_categories.sql
│   │   └── 003_create_tasks.sql
│   └── seeds/
│       └── sample_data.sql
└── 📁 tests/                       # Integration tests
    ├── auth.test.js
    ├── tasks.test.js
    └── api.test.js
```

## 🏃‍♂️ Step 5: Run Your Application

### Quick Start

```bash
# Navigate to your project
cd TaskMaster

# Install all dependencies
npm run install:all

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# Start the database (using Docker)
docker-compose up -d postgres

# Run database migrations
npm run db:migrate

# Seed with sample data (optional)
npm run db:seed

# Start development servers
npm run dev
```

### What Happens Next

1. **Frontend starts** at http://localhost:3000
2. **Backend API starts** at http://localhost:3001
3. **Database** runs on localhost:5432
4. **WebSocket server** enables real-time updates

## 🎯 Step 6: Test Your Application

### 1. User Registration

1. Open http://localhost:3000
2. Click "Sign Up"
3. Create a new account:
   - Name: John Doe
   - Email: <EMAIL>
   - Password: SecurePass123!

### 2. Create Your First Task

1. After login, you'll see the dashboard
2. Click "New Task"
3. Fill in the details:
   - Title: "Complete TaoForge tutorial"
   - Description: "Follow the quick start guide"
   - Category: "Learning"
   - Priority: "High"
   - Due Date: Tomorrow

### 3. Explore Features

**Task Management:**
- Create, edit, and delete tasks
- Mark tasks as complete
- Filter by category and priority
- Search tasks

**Dashboard:**
- View task statistics
- See upcoming deadlines
- Track completion rates
- Analyze productivity trends

**Team Collaboration:**
- Invite team members
- Assign tasks to others
- Share task lists
- Real-time updates

## 🎨 Step 7: Customize Your App

### Change the Theme

1. Open `client/src/styles/theme.css`
2. Modify the color variables:

```css
:root {
  --primary-color: #3b82f6;    /* Blue */
  --secondary-color: #10b981;  /* Green */
  --accent-color: #f59e0b;     /* Amber */
  --background-color: #f8fafc; /* Light gray */
}
```

### Add a New Feature

Use TaoForge to add features:

```bash
taoforge enhance "Add a calendar view to show tasks by date with drag-and-drop functionality"
```

### Modify Task Categories

1. Open `server/src/models/Category.js`
2. Add new default categories:

```javascript
const defaultCategories = [
  'Work',
  'Personal',
  'Learning',
  'Health',
  'Finance',
  'Shopping'  // New category
];
```

## 🚀 Step 8: Deploy Your Application

### Option 1: Deploy to Heroku

```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create taskmaster-app

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Deploy
git add .
git commit -m "Initial deployment"
git push heroku main
```

### Option 2: Deploy with Docker

```bash
# Build the application
docker-compose -f docker-compose.prod.yml build

# Deploy to your server
docker-compose -f docker-compose.prod.yml up -d
```

### Option 3: Deploy to Vercel (Frontend) + Railway (Backend)

```bash
# Deploy frontend to Vercel
cd client
npx vercel --prod

# Deploy backend to Railway
cd ../server
# Connect to Railway and deploy
```

## 🎉 Congratulations!

You've successfully created a complete task management application using TaoForge! In just 30 minutes, you have:

✅ **A production-ready web application**
✅ **User authentication and authorization**
✅ **Complete CRUD operations for tasks**
✅ **Real-time updates with WebSocket**
✅ **Mobile-responsive design**
✅ **Comprehensive test suite**
✅ **Deployment-ready configuration**
✅ **Complete documentation**

## 📚 Next Steps

### Learn More
- **[Developer Guide](../user-guides/developers-guide.md)** - Advanced features and customization
- **[API Documentation](../api/README.md)** - Integrate with other services
- **[Deployment Guide](../deployment/README.md)** - Production deployment strategies

### Enhance Your App
- Add email notifications
- Implement file attachments
- Create mobile apps
- Add advanced analytics
- Integrate with external calendars

### Join the Community
- **[Community Forum](../support/community.md)** - Connect with other users
- **[GitHub](https://github.com/taoforge/taoforge)** - Contribute to the project
- **[Discord](https://discord.gg/taoforge)** - Real-time chat and support

## 💡 Pro Tips

1. **Be Specific:** The more detailed your description, the better the result
2. **Iterate:** Use TaoForge to add features incrementally
3. **Review Code:** Always review and understand the generated code
4. **Customize:** Make the application truly yours with custom styling and features
5. **Share:** Deploy your app and share it with others!

**Happy building with TaoForge!** 🚀
