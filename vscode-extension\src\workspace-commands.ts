import * as vscode from 'vscode';
import * as path from 'path';
import { FileSystemService, SearchOptions, ProjectConfig, ProjectType } from './file-system-service';

export class WorkspaceCommands {
  private fileSystemService: FileSystemService;

  constructor(context: vscode.ExtensionContext) {
    this.fileSystemService = new FileSystemService(context);
    this.registerCommands(context);
  }

  private registerCommands(context: vscode.ExtensionContext): void {
    // Project creation commands
    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.createProject', () => this.createProject())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.createProjectFromTemplate', () => this.createProjectFromTemplate())
    );

    // File management commands
    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.createFile', () => this.createFile())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.createMultipleFiles', () => this.createMultipleFiles())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.searchInFiles', () => this.searchInFiles())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.backupFiles', () => this.backupFiles())
    );

    // Workspace management commands
    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.showWorkspaceInfo', () => this.showWorkspaceInfo())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.listProjectFiles', () => this.listProjectFiles())
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.watchFiles', () => this.setupFileWatcher())
    );

    // Operation monitoring commands
    context.subscriptions.push(
      vscode.commands.registerCommand('aetherforge.showOperations', () => this.showOperations())
    );
  }

  /**
   * Create a new project with user input
   */
  async createProject(): Promise<void> {
    try {
      const projectName = await vscode.window.showInputBox({
        prompt: 'Enter project name',
        placeHolder: 'my-awesome-project',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Project name is required';
          }
          if (!/^[a-zA-Z0-9-_]+$/.test(value)) {
            return 'Project name can only contain letters, numbers, hyphens, and underscores';
          }
          return null;
        }
      });

      if (!projectName) return;

      const description = await vscode.window.showInputBox({
        prompt: 'Enter project description (optional)',
        placeHolder: 'A brief description of your project'
      });

      const projectTypes = Object.values(ProjectType).map((type: string) => ({
        label: type.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
        value: type
      }));

      const selectedType = await vscode.window.showQuickPick(projectTypes, {
        placeHolder: 'Select project type'
      });

      if (!selectedType) return;

      const config: ProjectConfig = {
        name: projectName,
        description: description || '',
        type: selectedType.value as ProjectType,
        version: '1.0.0',
        author: await this.getGitUserName(),
        license: 'MIT'
      };

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Creating project "${projectName}"...`,
        cancellable: false
      }, async (progress) => {
        progress.report({ increment: 0 });
        
        const operationId = await this.fileSystemService.createProject(config);
        
        // Monitor operation progress
        const operation = this.fileSystemService.getOperation(operationId);
        if (operation) {
          progress.report({ increment: operation.progress });
        }
      });

      vscode.window.showInformationMessage(`✅ Project "${projectName}" created successfully!`);

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create project: ${error}`);
    }
  }

  /**
   * Create project from template
   */
  async createProjectFromTemplate(): Promise<void> {
    try {
      const templates = this.fileSystemService.getProjectTemplates();
      
      const templateItems = templates.map(template => ({
        label: template.name,
        description: template.description,
        detail: `Type: ${template.type}`,
        template
      }));

      const selectedTemplate = await vscode.window.showQuickPick(templateItems, {
        placeHolder: 'Select a project template'
      });

      if (!selectedTemplate) return;

      const projectName = await vscode.window.showInputBox({
        prompt: 'Enter project name',
        placeHolder: 'my-project',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Project name is required';
          }
          return null;
        }
      });

      if (!projectName) return;

      const description = await vscode.window.showInputBox({
        prompt: 'Enter project description (optional)',
        placeHolder: selectedTemplate.template.description
      });

      const config: ProjectConfig = {
        name: projectName,
        description: description || selectedTemplate.template.description,
        type: selectedTemplate.template.type,
        version: '1.0.0',
        author: await this.getGitUserName(),
        license: 'MIT'
      };

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Creating project from template...`,
        cancellable: false
      }, async () => {
        await this.fileSystemService.createProject(config, selectedTemplate.template.id);
      });

      vscode.window.showInformationMessage(`✅ Project "${projectName}" created from template!`);

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create project from template: ${error}`);
    }
  }

  /**
   * Create a single file
   */
  async createFile(): Promise<void> {
    try {
      const filePath = await vscode.window.showInputBox({
        prompt: 'Enter file path (relative to workspace root)',
        placeHolder: 'src/components/MyComponent.tsx',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'File path is required';
          }
          return null;
        }
      });

      if (!filePath) return;

      const content = await vscode.window.showInputBox({
        prompt: 'Enter initial file content (optional)',
        placeHolder: 'Leave empty for blank file'
      });

      const workspaceManager = (this.fileSystemService as any).workspaceManager;
      const fileUri = await workspaceManager.createFile(filePath, content || '');

      // Open the created file
      const document = await vscode.workspace.openTextDocument(fileUri);
      await vscode.window.showTextDocument(document);

      vscode.window.showInformationMessage(`✅ File "${filePath}" created successfully!`);

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create file: ${error}`);
    }
  }

  /**
   * Create multiple files at once
   */
  async createMultipleFiles(): Promise<void> {
    try {
      const input = await vscode.window.showInputBox({
        prompt: 'Enter file paths separated by commas',
        placeHolder: 'src/utils/helpers.ts, src/types/index.ts, tests/unit.test.ts',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'At least one file path is required';
          }
          return null;
        }
      });

      if (!input) return;

      const filePaths = input.split(',').map(path => path.trim()).filter(path => path.length > 0);
      
      const files = filePaths.map(path => ({
        path,
        content: `// ${path}\n// Created by Aetherforge\n\n`
      }));

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Creating ${files.length} files...`,
        cancellable: false
      }, async () => {
        await this.fileSystemService.createFiles(files);
      });

      vscode.window.showInformationMessage(`✅ Created ${files.length} files successfully!`);

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create files: ${error}`);
    }
  }

  /**
   * Search in files
   */
  async searchInFiles(): Promise<void> {
    try {
      const searchTerm = await vscode.window.showInputBox({
        prompt: 'Enter search term',
        placeHolder: 'function myFunction'
      });

      if (!searchTerm) return;

      const options: SearchOptions = {
        pattern: searchTerm,
        maxResults: 100
      };

      const results = await this.fileSystemService.searchInFiles(options);

      if (results.length === 0) {
        vscode.window.showInformationMessage('No results found');
        return;
      }

      // Show results in a quick pick
      const resultItems = results.map(result => ({
        label: `${result.file}:${result.line}:${result.column}`,
        description: result.text.trim(),
        detail: `Match: "${result.match}"`,
        result
      }));

      const selectedResult = await vscode.window.showQuickPick(resultItems, {
        placeHolder: `Found ${results.length} results. Select one to open.`
      });

      if (selectedResult) {
        const workspaceManager = (this.fileSystemService as any).workspaceManager;
        const editor = await workspaceManager.openFile(selectedResult.result.file);
        
        // Navigate to the specific line
        const position = new vscode.Position(selectedResult.result.line - 1, selectedResult.result.column - 1);
        editor.selection = new vscode.Selection(position, position);
        editor.revealRange(new vscode.Range(position, position));
      }

    } catch (error) {
      vscode.window.showErrorMessage(`Search failed: ${error}`);
    }
  }

  /**
   * Backup selected files
   */
  async backupFiles(): Promise<void> {
    try {
      const workspaceInfo = this.fileSystemService.getWorkspaceInfo();
      if (!workspaceInfo) {
        vscode.window.showErrorMessage('No workspace folder is open');
        return;
      }

      // Get list of files to backup
      const fileInfos = await this.fileSystemService.listFiles('', false, (file) => 
        !file.startsWith('.') && !file.includes('node_modules')
      );

      const fileItems = fileInfos
        .filter(info => !info.isDirectory)
        .map(info => ({
          label: info.name,
          description: info.path,
          detail: `${(info.size / 1024).toFixed(1)} KB`,
          picked: false
        }));

      const selectedFiles = await vscode.window.showQuickPick(fileItems, {
        placeHolder: 'Select files to backup',
        canPickMany: true
      });

      if (!selectedFiles || selectedFiles.length === 0) return;

      const filePaths = selectedFiles.map(item => item.description);

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Backing up ${filePaths.length} files...`,
        cancellable: false
      }, async () => {
        const backupDir = await this.fileSystemService.backupFiles(filePaths);
        vscode.window.showInformationMessage(`✅ Files backed up to: ${backupDir}`);
      });

    } catch (error) {
      vscode.window.showErrorMessage(`Backup failed: ${error}`);
    }
  }

  /**
   * Show workspace information
   */
  async showWorkspaceInfo(): Promise<void> {
    const workspaceInfo = this.fileSystemService.getWorkspaceInfo();
    
    if (!workspaceInfo) {
      vscode.window.showInformationMessage('No workspace folder is open');
      return;
    }

    const info = [
      `**Workspace Information**`,
      ``,
      `**Root Path:** ${workspaceInfo.rootPath}`,
      `**Folders:** ${workspaceInfo.folders.length}`,
      `**Open Files:** ${workspaceInfo.openFiles.length}`,
      `**Active Editor:** ${workspaceInfo.activeEditor?.document.fileName || 'None'}`,
      ``,
      `**Folders:**`,
      ...workspaceInfo.folders.map(folder => `- ${folder.name}: ${folder.uri.fsPath}`)
    ].join('\n');

    const document = await vscode.workspace.openTextDocument({
      content: info,
      language: 'markdown'
    });

    await vscode.window.showTextDocument(document);
  }

  /**
   * List project files
   */
  async listProjectFiles(): Promise<void> {
    try {
      const fileInfos = await this.fileSystemService.listFiles('', true);
      
      const fileItems = fileInfos.map(info => ({
        label: info.name,
        description: info.path,
        detail: info.isDirectory ? 'Directory' : `${(info.size / 1024).toFixed(1)} KB`,
        iconPath: info.isDirectory ? 
          new vscode.ThemeIcon('folder') : 
          new vscode.ThemeIcon('file')
      }));

      const selectedFile = await vscode.window.showQuickPick(fileItems, {
        placeHolder: `Found ${fileInfos.length} files and directories`
      });

      if (selectedFile && !selectedFile.detail?.includes('Directory')) {
        const workspaceManager = (this.fileSystemService as any).workspaceManager;
        await workspaceManager.openFile(selectedFile.description);
      }

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to list files: ${error}`);
    }
  }

  /**
   * Setup file watcher
   */
  async setupFileWatcher(): Promise<void> {
    const pattern = await vscode.window.showInputBox({
      prompt: 'Enter file pattern to watch',
      placeHolder: '**/*.{js,ts,jsx,tsx}',
      value: '**/*.{js,ts,jsx,tsx}'
    });

    if (!pattern) return;

    const disposable = this.fileSystemService.watchFiles(pattern, {
      onCreate: (uri) => {
        vscode.window.showInformationMessage(`📁 File created: ${path.basename(uri.fsPath)}`);
      },
      onChange: (uri) => {
        vscode.window.showInformationMessage(`📝 File changed: ${path.basename(uri.fsPath)}`);
      },
      onDelete: (uri) => {
        vscode.window.showInformationMessage(`🗑️ File deleted: ${path.basename(uri.fsPath)}`);
      }
    });

    vscode.window.showInformationMessage(`👀 Watching files matching: ${pattern}`);

    // Auto-dispose after 5 minutes for demo purposes
    setTimeout(() => {
      disposable.dispose();
      vscode.window.showInformationMessage('File watcher stopped');
    }, 5 * 60 * 1000);
  }

  /**
   * Show operations status
   */
  async showOperations(): Promise<void> {
    const operations = this.fileSystemService.getAllOperations();
    
    if (operations.length === 0) {
      vscode.window.showInformationMessage('No operations found');
      return;
    }

    const operationItems = operations.map(op => ({
      label: `${op.type.toUpperCase()} - ${op.status}`,
      description: `${op.progress}% complete`,
      detail: `${op.timestamp.toLocaleString()} - ${op.error || 'No errors'}`,
      operation: op
    }));

    await vscode.window.showQuickPick(operationItems, {
      placeHolder: `${operations.length} operations found`
    });
  }

  /**
   * Get Git user name for project author
   */
  private async getGitUserName(): Promise<string> {
    try {
      const gitExtension = vscode.extensions.getExtension('vscode.git');
      if (gitExtension) {
        const git = gitExtension.exports.getAPI(1);
        const config = await git.getGlobalConfig();
        return config.get('user.name') || 'Unknown';
      }
    } catch (error) {
      // Ignore git errors
    }
    return 'Unknown';
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.fileSystemService.dispose();
  }
}
