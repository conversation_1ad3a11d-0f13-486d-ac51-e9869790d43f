"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useProgressMonitoringStore = exports.useAgentInteractionStore = exports.useProjectConfigStore = void 0;
const zustand_1 = require("zustand");
const middleware_1 = require("zustand/middleware");
const defaultProjectConfig = {
    name: '',
    description: '',
    type: 'fullstack',
    workflow: 'default',
    requirements: [],
    priority: 'normal',
    agentBehavior: 'balanced',
    features: {
        enableParallelExecution: true,
        enableCodeReview: true,
        enableTesting: true,
        enableDocumentation: true,
        enableCICD: false,
        enableDocker: false,
        enableDeployment: false,
        enableAIOptimization: true,
        enableSecurityScan: true,
        enablePerformanceOptimization: false,
        enableMonitoring: false,
        enableAnalytics: false
    },
    technologies: {
        programmingLanguages: [],
        frameworks: [],
        databases: [],
        cloudProviders: [],
        tools: []
    },
    deployment: {
        targets: [],
        environment: 'development',
        scalability: 'single',
        monitoring: false
    },
    quality: {
        codeQualityLevel: 'standard',
        testCoverageTarget: 0.8,
        documentationLevel: 'standard',
        securityLevel: 'standard'
    }
};
exports.useProjectConfigStore = (0, zustand_1.create)()((0, middleware_1.subscribeWithSelector)((set, get) => ({
    config: defaultProjectConfig,
    templates: [],
    projectTypes: [],
    workflows: [],
    isLoading: false,
    error: null,
    updateConfig: (updates) => {
        set(state => ({
            config: { ...state.config, ...updates }
        }));
    },
    resetConfig: () => {
        set({ config: defaultProjectConfig });
    },
    loadTemplates: async () => {
        set({ isLoading: true, error: null });
        try {
            // Implementation will be handled by VS Code API
            const templates = await vscode.sendRequest('getProjectTemplates');
            set({ templates, isLoading: false });
        }
        catch (error) {
            set({ error: error.message, isLoading: false });
        }
    },
    loadProjectTypes: async () => {
        try {
            const projectTypes = await vscode.sendRequest('getProjectTypes');
            set({ projectTypes });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    loadWorkflows: async () => {
        try {
            const workflows = await vscode.sendRequest('getWorkflows');
            set({ workflows });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    validateConfig: () => {
        const { config } = get();
        return !!(config.name && config.description && config.type);
    },
    createProject: async () => {
        const { config } = get();
        set({ isLoading: true, error: null });
        try {
            await vscode.sendRequest('createProject', config);
            set({ isLoading: false });
        }
        catch (error) {
            set({ error: error.message, isLoading: false });
        }
    },
    previewProject: async () => {
        const { config } = get();
        try {
            return await vscode.sendRequest('previewProject', config);
        }
        catch (error) {
            set({ error: error.message });
            throw error;
        }
    }
})));
exports.useAgentInteractionStore = (0, zustand_1.create)()((0, middleware_1.subscribeWithSelector)((set, get) => ({
    agents: [],
    selectedAgent: null,
    messages: [],
    tasks: [],
    isLoading: false,
    error: null,
    connectionStatus: 'disconnected',
    loadAgents: async () => {
        set({ isLoading: true, error: null });
        try {
            const agents = await vscode.sendRequest('getAgents');
            set({ agents, isLoading: false, connectionStatus: 'connected' });
        }
        catch (error) {
            set({ error: error.message, isLoading: false, connectionStatus: 'disconnected' });
        }
    },
    selectAgent: (agent) => {
        set({ selectedAgent: agent });
        get().loadMessages(agent.id);
    },
    sendMessage: async (content) => {
        const { selectedAgent } = get();
        if (!selectedAgent)
            return;
        const message = {
            id: Date.now().toString(),
            agentId: selectedAgent.id,
            content,
            type: 'user',
            timestamp: new Date().toISOString()
        };
        set(state => ({
            messages: [...state.messages, message]
        }));
        try {
            const response = await vscode.sendRequest('sendMessageToAgent', {
                agentId: selectedAgent.id,
                message: content
            });
            if (response.reply) {
                const agentMessage = {
                    id: (Date.now() + 1).toString(),
                    agentId: selectedAgent.id,
                    content: response.reply,
                    type: 'agent',
                    timestamp: new Date().toISOString()
                };
                set(state => ({
                    messages: [...state.messages, agentMessage]
                }));
            }
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    executeTask: async (task) => {
        const { selectedAgent } = get();
        if (!selectedAgent)
            return;
        try {
            const result = await vscode.sendRequest('executeAgentTask', {
                agentId: selectedAgent.id,
                task
            });
            set(state => ({
                tasks: [...state.tasks, result]
            }));
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    loadMessages: async (agentId) => {
        try {
            const messages = await vscode.sendRequest('getAgentMessages', { agentId });
            set({ messages });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    loadTasks: async () => {
        try {
            const tasks = await vscode.sendRequest('getAgentTasks');
            set({ tasks });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    updateAgentStatus: (agentId, status) => {
        set(state => ({
            agents: state.agents.map(agent => agent.id === agentId ? { ...agent, ...status } : agent)
        }));
    },
    addMessage: (message) => {
        set(state => ({
            messages: [...state.messages, message]
        }));
    },
    updateTask: (taskId, updates) => {
        set(state => ({
            tasks: state.tasks.map(task => task.id === taskId ? { ...task, ...updates } : task)
        }));
    }
})));
exports.useProgressMonitoringStore = (0, zustand_1.create)()((0, middleware_1.subscribeWithSelector)((set, get) => ({
    projects: [],
    selectedProject: null,
    workflows: [],
    pheromones: [],
    systemStatus: null,
    isLoading: false,
    error: null,
    realTimeEnabled: true,
    loadProjects: async () => {
        set({ isLoading: true, error: null });
        try {
            const projects = await vscode.sendRequest('getProjects');
            set({ projects, isLoading: false });
        }
        catch (error) {
            set({ error: error.message, isLoading: false });
        }
    },
    selectProject: (project) => {
        set({ selectedProject: project });
    },
    loadWorkflows: async () => {
        try {
            const workflows = await vscode.sendRequest('getWorkflows');
            set({ workflows });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    loadPheromones: async (filters) => {
        try {
            const pheromones = await vscode.sendRequest('getPheromones', filters);
            set({ pheromones });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    loadSystemStatus: async () => {
        try {
            const systemStatus = await vscode.sendRequest('getSystemStatus');
            set({ systemStatus });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    updateProject: (projectId, updates) => {
        set(state => ({
            projects: state.projects.map(project => project.id === projectId ? { ...project, ...updates } : project),
            selectedProject: state.selectedProject?.id === projectId
                ? { ...state.selectedProject, ...updates }
                : state.selectedProject
        }));
    },
    updateWorkflow: (workflowId, updates) => {
        set(state => ({
            workflows: state.workflows.map(workflow => workflow.id === workflowId ? { ...workflow, ...updates } : workflow)
        }));
    },
    addPheromone: (pheromone) => {
        set(state => ({
            pheromones: [pheromone, ...state.pheromones].slice(0, 1000) // Keep last 1000
        }));
    },
    toggleRealTime: () => {
        set(state => ({ realTimeEnabled: !state.realTimeEnabled }));
    },
    pauseProject: async (projectId) => {
        try {
            await vscode.sendRequest('pauseProject', { projectId });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    resumeProject: async (projectId) => {
        try {
            await vscode.sendRequest('resumeProject', { projectId });
        }
        catch (error) {
            set({ error: error.message });
        }
    },
    cancelProject: async (projectId) => {
        try {
            await vscode.sendRequest('cancelProject', { projectId });
        }
        catch (error) {
            set({ error: error.message });
        }
    }
})));
//# sourceMappingURL=index.js.map