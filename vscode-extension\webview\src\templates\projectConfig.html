<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Aetherforge - Project Configuration</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            overflow: hidden;
        }
        
        #root {
            width: 100vw;
            height: 100vh;
            overflow: auto;
        }
        
        /* Loading spinner */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-progressBar-background);
            border-top: 4px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 16px;
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
        }
        
        /* Error state */
        .error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            padding: 20px;
            text-align: center;
        }
        
        .error-icon {
            font-size: 48px;
            color: var(--vscode-errorForeground);
            margin-bottom: 16px;
        }
        
        .error-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--vscode-errorForeground);
            margin-bottom: 8px;
        }
        
        .error-message {
            color: var(--vscode-descriptionForeground);
            margin-bottom: 16px;
        }
        
        .error-button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .error-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        /* VS Code theme integration */
        .vscode-light {
            --color-primary: #0066cc;
            --color-success: #28a745;
            --color-warning: #ffc107;
            --color-danger: #dc3545;
        }
        
        .vscode-dark {
            --color-primary: #4fc3f7;
            --color-success: #4caf50;
            --color-warning: #ff9800;
            --color-danger: #f44336;
        }
        
        .vscode-high-contrast {
            --color-primary: #ffffff;
            --color-success: #00ff00;
            --color-warning: #ffff00;
            --color-danger: #ff0000;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="spinner"></div>
            <div class="loading-text">Loading Project Configuration...</div>
        </div>
    </div>
    
    <script>
        // Initialize VS Code API
        const vscode = acquireVsCodeApi();
        
        // Handle theme changes
        function updateTheme() {
            const body = document.body;
            const theme = body.getAttribute('data-vscode-theme-kind');
            
            body.className = '';
            if (theme === 'vscode-light') {
                body.classList.add('vscode-light');
            } else if (theme === 'vscode-dark') {
                body.classList.add('vscode-dark');
            } else if (theme === 'vscode-high-contrast') {
                body.classList.add('vscode-high-contrast');
            }
        }
        
        // Apply initial theme
        updateTheme();
        
        // Listen for theme changes
        new MutationObserver(updateTheme).observe(document.body, {
            attributes: true,
            attributeFilter: ['data-vscode-theme-kind']
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Project Config Panel Error:', event.error);
            
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">Failed to Load Project Configuration</div>
                    <div class="error-message">${event.error?.message || 'An unexpected error occurred'}</div>
                    <button class="error-button" onclick="window.location.reload()">
                        Reload Panel
                    </button>
                </div>
            `;
            
            vscode.postMessage({
                command: 'error',
                data: {
                    message: event.error?.message || 'Project config panel error',
                    stack: event.error?.stack
                }
            });
        });
        
        // Unhandled promise rejection handling
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled Promise Rejection:', event.reason);
            
            vscode.postMessage({
                command: 'error',
                data: {
                    message: event.reason?.message || 'Unhandled promise rejection',
                    stack: event.reason?.stack
                }
            });
        });
        
        // Notify extension that panel is ready
        vscode.postMessage({
            command: 'panelReady',
            data: { panel: 'projectConfig' }
        });
    </script>
</body>
</html>
