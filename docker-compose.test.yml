version: '3.8'

services:
  # Test database
  postgres-test:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=aetherforge_test
      - POSTGRES_USER=aetherforge
      - POSTGRES_PASSWORD=test_password
    ports:
      - "5433:5432"
    volumes:
      - postgres-test-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aetherforge -d aetherforge_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Test Redis
  redis-test:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis-test-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network
    command: redis-server --appendonly yes --databases 16

  # Test runner container
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=test
      - DATABASE_URL=*********************************************************/aetherforge_test
      - REDIS_URL=redis://redis-test:6379/1
      - PYTHONPATH=/app
      - OPENAI_API_KEY=test_key_12345
      - PROJECTS_DIR=/app/test_projects
      - PHEROMONE_FILE=/app/test_pheromones.json
      - LOG_LEVEL=debug
      - DEBUG=true
    volumes:
      - .:/app
      - test-results:/app/test_results
      - coverage-reports:/app/coverage_reports
      - test-projects:/app/test_projects
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    working_dir: /app
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 5 &&
        echo 'Running comprehensive test suite...' &&
        python -m pytest tests/ -v 
          --cov=src 
          --cov-report=html:/app/coverage_reports 
          --cov-report=xml:/app/coverage_reports/coverage.xml 
          --html=/app/test_results/report.html 
          --self-contained-html
          --timeout=300
          --maxfail=5
          -x
      "

  # Unit tests only
  unit-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=test
      - PYTHONPATH=/app
      - OPENAI_API_KEY=test_key_12345
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - test-results:/app/test_results
      - coverage-reports:/app/coverage_reports
    working_dir: /app
    networks:
      - test-network
    command: >
      python -m pytest tests/ -v 
        --ignore=tests/test_integration_comprehensive.py
        --ignore=tests/test_complete_integration.py
        --ignore=tests/test_end_to_end.py
        --ignore=tests/test_production_integration.py
        --cov=src 
        --cov-report=html:/app/coverage_reports/unit
        --html=/app/test_results/unit_report.html
        --timeout=60

  # Integration tests
  integration-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=test
      - DATABASE_URL=*********************************************************/aetherforge_test
      - REDIS_URL=redis://redis-test:6379/1
      - PYTHONPATH=/app
      - OPENAI_API_KEY=test_key_12345
      - PROJECTS_DIR=/app/test_projects
      - PHEROMONE_FILE=/app/test_pheromones.json
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - test-results:/app/test_results
      - test-projects:/app/test_projects
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    working_dir: /app
    networks:
      - test-network
    command: >
      python -m pytest 
        tests/test_integration_comprehensive.py
        tests/test_complete_integration.py
        -v --timeout=300

  # Production readiness tests
  production-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=production
      - PYTHONPATH=/app
      - OPENAI_API_KEY=test_key_12345
      - POSTGRES_PASSWORD=test_password
    volumes:
      - .:/app
      - test-results:/app/test_results
      - /var/run/docker.sock:/var/run/docker.sock:ro
    working_dir: /app
    networks:
      - test-network
    command: >
      python -m pytest 
        tests/test_production_integration.py
        -v --timeout=180
        --html=/app/test_results/production_report.html

  # Performance tests
  performance-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AETHERFORGE_ENV=test
      - DATABASE_URL=*********************************************************/aetherforge_test
      - REDIS_URL=redis://redis-test:6379/2
      - PYTHONPATH=/app
      - OPENAI_API_KEY=test_key_12345
      - PROJECTS_DIR=/app/test_projects
    volumes:
      - .:/app
      - test-results:/app/test_results
      - test-projects:/app/test_projects
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    working_dir: /app
    networks:
      - test-network
    command: >
      python -m pytest 
        tests/test_performance.py
        -v --timeout=600
        --html=/app/test_results/performance_report.html

  # Code quality checks
  code-quality:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - test-results:/app/test_results
    working_dir: /app
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running code quality checks...' &&
        echo 'Black formatting check:' &&
        black --check --diff src/ tests/ &&
        echo 'isort import sorting check:' &&
        isort --check-only --diff src/ tests/ &&
        echo 'Flake8 linting:' &&
        flake8 src/ tests/ --max-line-length=88 --extend-ignore=E203,W503 &&
        echo 'MyPy type checking:' &&
        mypy src/ --ignore-missing-imports &&
        echo 'Bandit security check:' &&
        bandit -r src/ -f json -o /app/test_results/bandit-report.json &&
        echo 'Safety dependency check:' &&
        safety check --json --output /app/test_results/safety-report.json &&
        echo 'All code quality checks passed!'
      "

volumes:
  postgres-test-data:
  redis-test-data:
  test-results:
  coverage-reports:
  test-projects:

networks:
  test-network:
    driver: bridge
