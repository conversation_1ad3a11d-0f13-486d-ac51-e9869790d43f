#!/usr/bin/env python3
"""
Comprehensive test suite for the Archon Integration
Tests the actual ArchonIntegration class and its methods
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
import sys
import os
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from archon_integration import (
    ArchonIntegration,
    ArchonIntegrationExecutor,
    AgentSpecification,
    AgentMetrics,
    AgentEvolutionPlan,
    AgentTeamComposition,
    AgentType,
    OptimizationStrategy,
    AgentLifecycleStage,
    ArchonIntegrationError,
    AgentGenerationError,
    AgentOptimizationError,
    AgentEvaluationError
)
from archon_config import ArchonConfig, ArchonMode


class TestArchonIntegration:
    """Test the ArchonIntegration class"""
    
    def setup_method(self):
        """Setup test environment"""
        self.archon_url = "http://localhost:8100"
        self.openai_api_key = "test-key"
        
    def test_archon_integration_initialization(self):
        """Test ArchonIntegration initialization"""
        archon = ArchonIntegration(self.archon_url, self.openai_api_key)
        
        assert archon is not None
        assert archon.archon_url == self.archon_url
        assert archon.openai_api_key == self.openai_api_key
        assert hasattr(archon, 'active_agents')
        assert hasattr(archon, 'agent_metrics')
        assert hasattr(archon, 'evolution_plans')
        assert hasattr(archon, 'active_teams')
    
    def test_agent_specification_creation(self):
        """Test AgentSpecification creation"""
        agent = AgentSpecification(
            agent_id="test_001",
            agent_type=AgentType.DEVELOPER,
            name="Test Developer",
            description="Test developer agent",
            capabilities=["coding", "debugging"],
            tools=["ide", "debugger"],
            performance_targets={"quality": 0.9}
        )
        
        assert agent.agent_id == "test_001"
        assert agent.agent_type == AgentType.DEVELOPER
        assert agent.name == "Test Developer"
        assert "coding" in agent.capabilities
        assert "ide" in agent.tools
        assert agent.performance_targets["quality"] == 0.9
    
    def test_agent_metrics_creation(self):
        """Test AgentMetrics creation"""
        metrics = AgentMetrics(
            agent_id="test_001",
            performance_score=0.85,
            accuracy_score=0.90,
            efficiency_score=0.80,
            collaboration_score=0.75,
            adaptability_score=0.85,
            overall_score=0.83,
            execution_time=1.5,
            error_rate=0.05,
            success_rate=0.95
        )
        
        assert metrics.agent_id == "test_001"
        assert metrics.performance_score == 0.85
        assert metrics.overall_score == 0.83
        assert metrics.error_rate == 0.05
        assert metrics.success_rate == 0.95
    
    def test_agent_evolution_plan_creation(self):
        """Test AgentEvolutionPlan creation"""
        plan = AgentEvolutionPlan(
            agent_id="test_001",
            current_version="1.0.0",
            target_version="1.1.0",
            optimization_strategies=[OptimizationStrategy.PERFORMANCE],
            improvement_areas=["speed", "accuracy"],
            expected_improvements={"performance": 0.2},
            implementation_steps=["step1", "step2"],
            validation_criteria=["test1", "test2"],
            rollback_plan="rollback procedure"
        )
        
        assert plan.agent_id == "test_001"
        assert plan.current_version == "1.0.0"
        assert plan.target_version == "1.1.0"
        assert OptimizationStrategy.PERFORMANCE in plan.optimization_strategies
        assert "speed" in plan.improvement_areas
        assert plan.expected_improvements["performance"] == 0.2
    
    def test_agent_team_composition_creation(self):
        """Test AgentTeamComposition creation"""
        agents = [
            AgentSpecification(
                agent_id="agent_001",
                agent_type=AgentType.ANALYST,
                name="Analyst",
                description="Requirements analyst"
            ),
            AgentSpecification(
                agent_id="agent_002",
                agent_type=AgentType.DEVELOPER,
                name="Developer",
                description="Code developer"
            )
        ]
        
        team = AgentTeamComposition(
            team_id="team_001",
            project_id="project_001",
            agents=agents,
            coordination_strategy="collaborative",
            communication_patterns=["direct", "async"]
        )
        
        assert team.team_id == "team_001"
        assert team.project_id == "project_001"
        assert len(team.agents) == 2
        assert team.coordination_strategy == "collaborative"
        assert "direct" in team.communication_patterns
    
    def test_fallback_team_generation(self):
        """Test fallback team generation"""
        archon = ArchonIntegration()
        
        # Test fallback team generation
        project_prompt = "Build a web application"
        project_context = {
            "project_id": "test_project",
            "project_type": "web_application",
            "technology_stack": ["React", "Node.js"]
        }
        
        # This would normally be async, but we're testing the structure
        # In a real async test, we'd use asyncio.run()
        assert hasattr(archon, '_fallback_team_generation')
    
    def test_parse_team_composition(self):
        """Test team composition parsing"""
        archon = ArchonIntegration()
        
        data = {
            "team_id": "team_001",
            "project_id": "project_001",
            "agents": [
                {
                    "agent_id": "agent_001",
                    "agent_type": "analyst",
                    "name": "Requirements Analyst",
                    "description": "Analyzes requirements",
                    "capabilities": ["analysis", "documentation"],
                    "tools": ["research_tools"]
                }
            ],
            "coordination_strategy": "collaborative"
        }
        
        team = archon._parse_team_composition(data)
        
        assert team.team_id == "team_001"
        assert team.project_id == "project_001"
        assert len(team.agents) == 1
        assert team.agents[0].agent_type == AgentType.ANALYST
        assert team.coordination_strategy == "collaborative"
    
    def test_parse_evolution_plan(self):
        """Test evolution plan parsing"""
        archon = ArchonIntegration()
        
        data = {
            "agent_id": "agent_001",
            "current_version": "1.0.0",
            "target_version": "1.1.0",
            "optimization_strategies": ["performance", "efficiency"],
            "improvement_areas": ["speed", "accuracy"],
            "expected_improvements": {"performance": 0.2},
            "implementation_steps": ["step1", "step2"],
            "validation_criteria": ["test1"],
            "rollback_plan": "rollback"
        }
        
        plan = archon._parse_evolution_plan(data)
        
        assert plan.agent_id == "agent_001"
        assert plan.current_version == "1.0.0"
        assert plan.target_version == "1.1.0"
        assert len(plan.optimization_strategies) == 2
        assert "speed" in plan.improvement_areas
        assert plan.expected_improvements["performance"] == 0.2
    
    def test_parse_agent_metrics(self):
        """Test agent metrics parsing"""
        archon = ArchonIntegration()
        
        data = {
            "agent_id": "agent_001",
            "performance_score": 0.85,
            "accuracy_score": 0.90,
            "efficiency_score": 0.80,
            "collaboration_score": 0.75,
            "adaptability_score": 0.85,
            "overall_score": 0.83,
            "execution_time": 1.5,
            "error_rate": 0.05,
            "success_rate": 0.95
        }
        
        metrics = archon._parse_agent_metrics(data)
        
        assert metrics.agent_id == "agent_001"
        assert metrics.performance_score == 0.85
        assert metrics.accuracy_score == 0.90
        assert metrics.overall_score == 0.83
        assert metrics.execution_time == 1.5
    
    def test_determine_lifecycle_stage(self):
        """Test lifecycle stage determination"""
        archon = ArchonIntegration()
        
        # Test with high performance metrics
        agent_state = {
            "metrics": {
                "overall_score": 0.9,
                "performance_score": 0.85
            }
        }
        stage = archon._determine_lifecycle_stage(agent_state)
        assert stage == AgentLifecycleStage.MONITORING
        
        # Test with low performance metrics
        agent_state = {
            "metrics": {
                "overall_score": 0.4,
                "performance_score": 0.3
            }
        }
        stage = archon._determine_lifecycle_stage(agent_state)
        assert stage == AgentLifecycleStage.RETIREMENT
        
        # Test with medium performance and evolution plan
        agent_state = {
            "metrics": {
                "overall_score": 0.6,
                "performance_score": 0.5
            },
            "evolution_plan": {
                "agent_id": "test"
            }
        }
        stage = archon._determine_lifecycle_stage(agent_state)
        assert stage == AgentLifecycleStage.EVOLUTION
    
    def test_assess_lifecycle_health(self):
        """Test lifecycle health assessment"""
        archon = ArchonIntegration()
        
        # Test excellent health
        monitoring_data = {
            "performance_indicators": {
                "overall_performance": 0.9,
                "efficiency": 0.85,
                "accuracy": 0.9
            },
            "alerts": []
        }
        health = archon._assess_lifecycle_health(monitoring_data)
        assert health == "excellent"
        
        # Test poor health
        monitoring_data = {
            "performance_indicators": {
                "overall_performance": 0.3,
                "efficiency": 0.2,
                "accuracy": 0.4
            },
            "alerts": ["alert1", "alert2", "alert3"]
        }
        health = archon._assess_lifecycle_health(monitoring_data)
        assert health == "poor"


class TestArchonIntegrationExecutor:
    """Test the Archon Integration Executor"""
    
    def test_executor_initialization(self):
        """Test ArchonIntegrationExecutor initialization"""
        executor = ArchonIntegrationExecutor()
        
        assert executor is not None
        assert executor.archon_integration is None  # Not initialized until execution
    
    def test_execute_agent_generation_data_structure(self):
        """Test agent generation data structure"""
        project_context = {
            "project_id": "test_project",
            "project_type": "web_application",
            "technology_stack": ["React", "Node.js"],
            "team_size": 4
        }
        
        # Test data structure without actual execution
        assert project_context["project_id"] == "test_project"
        assert project_context["team_size"] == 4
        assert "React" in project_context["technology_stack"]


class TestArchonConfig:
    """Test the Archon Configuration"""
    
    def test_archon_config_initialization(self):
        """Test ArchonConfig initialization"""
        config = ArchonConfig()
        
        assert config.archon_url == "http://localhost:8100"
        assert config.archon_timeout == 300
        assert config.enable_lifecycle_monitoring is True
        assert config.max_agents_per_team == 8
    
    def test_archon_mode_config(self):
        """Test Archon mode configuration"""
        basic_config = ArchonConfig.get_archon_mode_config(ArchonMode.BASIC)
        enterprise_config = ArchonConfig.get_archon_mode_config(ArchonMode.ENTERPRISE)
        
        assert basic_config["max_agents"] < enterprise_config["max_agents"]
        assert basic_config["optimization_enabled"] is False
        assert enterprise_config["optimization_enabled"] is True
        assert "advanced_analytics" in enterprise_config
    
    def test_agent_type_templates(self):
        """Test agent type template configuration"""
        templates = ArchonConfig.get_agent_type_templates()
        
        assert "analyst" in templates
        assert "developer" in templates
        assert "qa" in templates
        
        analyst_template = templates["analyst"]
        assert "requirement_analysis" in analyst_template["base_capabilities"]
        assert "research_engine" in analyst_template["required_tools"]
        assert "accuracy" in analyst_template["performance_targets"]
    
    def test_optimization_strategies(self):
        """Test optimization strategy configuration"""
        strategies = ArchonConfig.get_optimization_strategies()
        
        assert "performance" in strategies
        assert "accuracy" in strategies
        assert "efficiency" in strategies
        
        performance_strategy = strategies["performance"]
        assert "execution_speed" in performance_strategy["focus_areas"]
        assert "response_time" in performance_strategy["metrics"]
        assert "speed" in performance_strategy["improvement_targets"]
    
    def test_lifecycle_stage_configs(self):
        """Test lifecycle stage configuration"""
        stage_configs = ArchonConfig.get_lifecycle_stage_configs()
        
        assert "conception" in stage_configs
        assert "deployment" in stage_configs
        assert "monitoring" in stage_configs
        
        deployment_config = stage_configs["deployment"]
        assert deployment_config["duration_hours"] == 1
        assert "validated_agent" in deployment_config["required_inputs"]
        assert "deployed_agent" in deployment_config["outputs"]


class TestAgentTypes:
    """Test agent type enums and data structures"""
    
    def test_agent_type_enum(self):
        """Test AgentType enum values"""
        assert AgentType.ANALYST.value == "analyst"
        assert AgentType.ARCHITECT.value == "architect"
        assert AgentType.DEVELOPER.value == "developer"
        assert AgentType.QA.value == "qa"
        assert AgentType.RESEARCHER.value == "researcher"
    
    def test_optimization_strategy_enum(self):
        """Test OptimizationStrategy enum values"""
        assert OptimizationStrategy.PERFORMANCE.value == "performance"
        assert OptimizationStrategy.ACCURACY.value == "accuracy"
        assert OptimizationStrategy.EFFICIENCY.value == "efficiency"
        assert OptimizationStrategy.ADAPTABILITY.value == "adaptability"
    
    def test_agent_lifecycle_stage_enum(self):
        """Test AgentLifecycleStage enum values"""
        assert AgentLifecycleStage.CONCEPTION.value == "conception"
        assert AgentLifecycleStage.GENERATION.value == "generation"
        assert AgentLifecycleStage.OPTIMIZATION.value == "optimization"
        assert AgentLifecycleStage.EVALUATION.value == "evaluation"
        assert AgentLifecycleStage.DEPLOYMENT.value == "deployment"
        assert AgentLifecycleStage.MONITORING.value == "monitoring"
        assert AgentLifecycleStage.EVOLUTION.value == "evolution"
        assert AgentLifecycleStage.RETIREMENT.value == "retirement"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
