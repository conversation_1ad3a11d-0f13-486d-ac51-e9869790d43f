# Development Guide

## Blog Platform API Development

### Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Copy environment variables: `cp .env.example .env`
4. Start development server: `npm run dev`

### Project Structure

```
blog-platform-api/
├── src/                 # Source code
├── tests/              # Test files
├── docs/               # Documentation
├── package.json        # Dependencies
└── README.md          # Project overview
```

### Development Workflow

1. Create feature branch
2. Make changes
3. Add tests
4. Run tests: `npm test`
5. Submit pull request

### Code Standards

- Use TypeScript for type safety
- Follow ESLint rules
- Maintain test coverage above 80%
- Write meaningful commit messages

### Testing

- Unit tests: `npm test`
- Integration tests: `npm run test:integration`
- E2E tests: `npm run test:e2e`
- Coverage: `npm run test:coverage`

### Debugging

Use VS Code debugger or:
```bash
npm run dev:debug
```

### Contributing

See CONTRIBUTING.md for detailed guidelines.
