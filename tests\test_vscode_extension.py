"""
Comprehensive test suite for VS Code Extension functionality
Tests extension commands, UI interactions, and API communication
"""

import pytest
import json
import tempfile
import os
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, AsyncMock

# Mock VS Code API since we can't import it in tests
class MockVSCode:
    class commands:
        registered_commands = {}

        @classmethod
        def registerCommand(cls, command, callback):
            cls.registered_commands[command] = callback
            return Mock()

        @classmethod
        def executeCommand(cls, command, *args):
            if command in cls.registered_commands:
                return cls.registered_commands[command](*args)
            return Mock()

    class window:
        @staticmethod
        def showInformationMessage(message, *options):
            return Mock()

        @staticmethod
        def showErrorMessage(message, *options):
            return Mock()

        @staticmethod
        def showInputBox(options=None):
            return Mock()

        @staticmethod
        def showQuickPick(items, options=None):
            return Mock()

        @staticmethod
        def createWebviewPanel(viewType, title, showOptions, options=None):
            panel = Mock()
            panel.webview = Mock()
            panel.webview.html = ""
            panel.webview.postMessage = Mock()
            panel.onDidReceiveMessage = Mock()
            return panel

    class workspace:
        @staticmethod
        def getConfiguration(section=None):
            config = Mock()
            config.get = Mock(return_value=None)
            config.update = Mock()
            return config

        @staticmethod
        def getWorkspaceFolder(uri):
            return Mock()

        workspaceFolders = [Mock()]

    class Uri:
        @staticmethod
        def file(path):
            uri = Mock()
            uri.fsPath = path
            return uri

    class StatusBarAlignment:
        Left = 1
        Right = 2

    class TreeDataProvider:
        pass

# Mock the VS Code module
import sys
sys.modules['vscode'] = MockVSCode()

# Now we can import our extension code (simulated)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'vscode-extension', 'src'))


class TestVSCodeExtensionCore:
    """Test core VS Code extension functionality"""

    def test_extension_activation(self):
        """Test extension activation process"""
        # Mock extension context
        context = Mock()
        context.subscriptions = []
        context.extensionPath = "/mock/extension/path"

        # Mock the activate function behavior
        def mock_activate(context):
            # Simulate command registration
            MockVSCode.commands.registerCommand('aetherforge.createProject', lambda: None)
            MockVSCode.commands.registerCommand('aetherforge.quickCreate', lambda: None)
            MockVSCode.commands.registerCommand('aetherforge.showAgentPanel', lambda: None)

            return True

        result = mock_activate(context)
        assert result is True

        # Verify commands were registered
        assert 'aetherforge.createProject' in MockVSCode.commands.registered_commands
        assert 'aetherforge.quickCreate' in MockVSCode.commands.registered_commands
        assert 'aetherforge.showAgentPanel' in MockVSCode.commands.registered_commands

    def test_configuration_loading(self):
        """Test loading extension configuration"""
        # Mock configuration
        config = Mock()
        config.get.side_effect = lambda key, default=None: {
            'orchestratorUrl': 'http://localhost:8000',
            'autoConnect': True,
            'logLevel': 'info',
            'defaultProjectType': 'fullstack'
        }.get(key, default)

        with patch.object(MockVSCode.workspace, 'getConfiguration', return_value=config):
            # Simulate configuration loading
            orchestrator_url = config.get('orchestratorUrl', 'http://localhost:8000')
            auto_connect = config.get('autoConnect', False)
            log_level = config.get('logLevel', 'info')

            assert orchestrator_url == 'http://localhost:8000'
            assert auto_connect is True
            assert log_level == 'info'

    def test_status_bar_initialization(self):
        """Test status bar item initialization"""
        # Mock status bar item
        status_bar_item = Mock()
        status_bar_item.text = ""
        status_bar_item.tooltip = ""
        status_bar_item.command = ""
        status_bar_item.show = Mock()
        status_bar_item.hide = Mock()

        # Simulate status bar setup
        status_bar_item.text = "$(rocket) Aetherforge"
        status_bar_item.tooltip = "Aetherforge AI Development Assistant"
        status_bar_item.command = "aetherforge.showSystemStatus"
        status_bar_item.show()

        assert status_bar_item.text == "$(rocket) Aetherforge"
        assert "Aetherforge" in status_bar_item.tooltip
        assert status_bar_item.command == "aetherforge.showSystemStatus"
        status_bar_item.show.assert_called_once()


class TestProjectCreationCommands:
    """Test project creation commands"""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace"""
        workspace = tempfile.mkdtemp(prefix="vscode_test_")
        yield workspace
        import shutil
        shutil.rmtree(workspace, ignore_errors=True)

    def test_create_project_command(self, temp_workspace):
        """Test the create project command"""
        # Mock user inputs
        project_name = "TestProject"
        project_type = "fullstack"
        project_prompt = "Create a web application with user authentication"

        # Mock VS Code input dialogs
        with patch.object(MockVSCode.window, 'showInputBox') as mock_input:
            with patch.object(MockVSCode.window, 'showQuickPick') as mock_pick:
                # Setup mock responses
                mock_input.side_effect = [project_name, project_prompt]
                mock_pick.return_value = project_type

                # Mock API call to orchestrator
                with patch('requests.post') as mock_post:
                    mock_response = Mock()
                    mock_response.status_code = 200
                    mock_response.json.return_value = {
                        "status": "success",
                        "project_id": "test_project_123",
                        "project_slug": "test-project"
                    }
                    mock_post.return_value = mock_response

                    # Simulate command execution
                    def create_project_command():
                        name = mock_input()
                        prompt = mock_input()
                        proj_type = mock_pick()

                        # API call
                        response = mock_post()

                        if response.status_code == 200:
                            return response.json()
                        return None

                    result = create_project_command()

                    assert result is not None
                    assert result["status"] == "success"
                    assert "project_id" in result

    def test_quick_create_command(self):
        """Test the quick create command"""
        # Mock quick create with predefined templates
        templates = [
            {"name": "React App", "type": "frontend", "description": "React application"},
            {"name": "Express API", "type": "api", "description": "Express.js API"},
            {"name": "Full Stack", "type": "fullstack", "description": "Complete web app"}
        ]

        with patch.object(MockVSCode.window, 'showQuickPick') as mock_pick:
            mock_pick.return_value = templates[0]  # Select React App

            # Simulate quick create
            def quick_create_command():
                selected_template = mock_pick()
                if selected_template:
                    return {
                        "template": selected_template["name"],
                        "type": selected_template["type"]
                    }
                return None

            result = quick_create_command()

            assert result is not None
            assert result["template"] == "React App"
            assert result["type"] == "frontend"

    def test_create_from_template_command(self):
        """Test creating project from template"""
        # Mock template selection and customization
        available_templates = [
            "E-commerce Platform",
            "Blog Application",
            "Task Management System",
            "Social Media App"
        ]

        with patch.object(MockVSCode.window, 'showQuickPick') as mock_pick:
            with patch.object(MockVSCode.window, 'showInputBox') as mock_input:
                mock_pick.return_value = available_templates[0]  # E-commerce Platform
                mock_input.return_value = "MyEcommerceStore"  # Custom name

                def create_from_template_command():
                    template = mock_pick()
                    custom_name = mock_input()

                    if template and custom_name:
                        return {
                            "template": template,
                            "project_name": custom_name,
                            "customized": True
                        }
                    return None

                result = create_from_template_command()

                assert result is not None
                assert result["template"] == "E-commerce Platform"
                assert result["project_name"] == "MyEcommerceStore"
                assert result["customized"] is True


class TestAgentInteractionPanel:
    """Test agent interaction panel functionality"""

    def test_agent_panel_creation(self):
        """Test creating the agent interaction panel"""
        # Mock webview panel
        panel = Mock()
        panel.webview = Mock()
        panel.webview.html = ""
        panel.webview.postMessage = Mock()
        panel.onDidReceiveMessage = Mock()

        with patch.object(MockVSCode.window, 'createWebviewPanel', return_value=panel):
            # Simulate panel creation
            def create_agent_panel():
                webview_panel = MockVSCode.window.createWebviewPanel(
                    'aetherforgeAgents',
                    'Aetherforge Agents',
                    MockVSCode.window,
                    {'enableScripts': True}
                )

                # Set initial HTML content
                webview_panel.webview.html = """
                <html>
                <head><title>Aetherforge Agents</title></head>
                <body>
                    <div id="agent-container">
                        <h2>Active Agents</h2>
                        <div id="agent-list"></div>
                    </div>
                </body>
                </html>
                """

                return webview_panel

            agent_panel = create_agent_panel()

            assert agent_panel is not None
            assert "Aetherforge Agents" in agent_panel.webview.html
            assert "agent-container" in agent_panel.webview.html

    def test_agent_status_updates(self):
        """Test updating agent status in the panel"""
        # Mock agent status data
        agent_status = {
            "analyst": {"status": "active", "progress": 75, "current_task": "Requirements analysis"},
            "architect": {"status": "waiting", "progress": 0, "current_task": "Waiting for requirements"},
            "developer": {"status": "idle", "progress": 0, "current_task": "Not started"},
            "qa": {"status": "idle", "progress": 0, "current_task": "Not started"}
        }

        # Mock webview panel
        panel = Mock()
        panel.webview = Mock()
        panel.webview.postMessage = Mock()

        # Simulate status update
        def update_agent_status(panel, status_data):
            panel.webview.postMessage({
                "command": "updateAgentStatus",
                "data": status_data
            })

        update_agent_status(panel, agent_status)

        # Verify message was sent
        panel.webview.postMessage.assert_called_once()
        call_args = panel.webview.postMessage.call_args[0][0]
        assert call_args["command"] == "updateAgentStatus"
        assert call_args["data"]["analyst"]["status"] == "active"
        assert call_args["data"]["analyst"]["progress"] == 75
        assert options['enableScripts'] is True
        assert options['retainContextWhenHidden'] is True
    
    def test_webview_content_generation(self):
        """Test webview HTML content generation"""
        from src.aetherforge import getWebviewContent
        
        content = getWebviewContent()
        
        # Check that content is valid HTML
        assert content.startswith('<!DOCTYPE html>')
        assert '<html' in content
        assert '</html>' in content
        
        # Check for required elements
        assert 'Aetherforge' in content
        assert 'Create Project' in content
        assert 'System Status' in content
        assert 'Projects' in content
        assert 'Settings' in content
        
        # Check for required form elements
        assert 'id="prompt"' in content
        assert 'id="projectName"' in content
        assert 'id="projectType"' in content
        
        # Check for JavaScript functionality
        assert 'createProject()' in content
        assert 'checkSystemStatus()' in content
        assert 'refreshProjects()' in content
    
    @patch('src.aetherforge.axios')
    def test_create_project_success(self, mock_axios):
        """Test successful project creation"""
        from src.aetherforge import createProject
        
        # Mock successful API response
        mock_response = {
            'data': {
                'status': 'success',
                'project_id': 'test-123',
                'project_slug': 'TestProject',
                'project_path': '/projects/TestProject'
            }
        }
        mock_axios.post.return_value = mock_response
        
        # Call createProject
        createProject(
            'Create a test application',
            self.mock_webview,
            'TestProject',
            'fullstack'
        )
        
        # Verify API was called correctly
        mock_axios.post.assert_called_once()
        call_args = mock_axios.post.call_args
        
        assert 'projects' in call_args[0][0]  # URL contains 'projects'
        
        request_data = call_args[0][1]
        assert request_data['prompt'] == 'Create a test application'
        assert request_data['project_name'] == 'TestProject'
        assert request_data['project_type'] == 'fullstack'
        
        # Verify webview messages
        assert self.mock_webview.postMessage.call_count >= 2
        
        # Check for success message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        success_messages = [msg for msg in messages if msg.get('command') == 'projectCreated']
        assert len(success_messages) == 1
    
    @patch('src.aetherforge.axios')
    def test_create_project_api_failure(self, mock_axios):
        """Test project creation when API fails"""
        from src.aetherforge import createProject
        
        # Mock API failure
        mock_axios.post.side_effect = Exception('Connection refused')
        
        # Call createProject
        createProject(
            'Create a test application',
            self.mock_webview,
            'TestProject',
            'fullstack'
        )
        
        # Verify error message was sent to webview
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        error_messages = [msg for msg in messages if msg.get('command') == 'error']
        assert len(error_messages) >= 1
    
    @patch('src.aetherforge.axios')
    def test_check_system_status(self, mock_axios):
        """Test system status checking"""
        from src.aetherforge import checkSystemStatus
        
        # Mock API response
        mock_response = {
            'data': {
                'components': {
                    'orchestrator': 'running',
                    'archon': 'offline',
                    'mcp-crawl4ai': 'unknown'
                }
            }
        }
        mock_axios.get.return_value = mock_response
        
        # Call checkSystemStatus
        checkSystemStatus(self.mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify webview received status update
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        status_messages = [msg for msg in messages if msg.get('command') == 'systemStatus']
        assert len(status_messages) == 1
        
        status_data = status_messages[0]['data']
        assert 'components' in status_data
        assert status_data['components']['orchestrator'] == 'running'
    
    @patch('src.aetherforge.axios')
    def test_refresh_projects(self, mock_axios):
        """Test project list refresh"""
        from src.aetherforge import refreshProjects
        
        # Mock API response
        mock_response = {
            'data': [
                {
                    'name': 'Project1',
                    'path': '/projects/Project1',
                    'created': '2023-01-01T00:00:00Z'
                },
                {
                    'name': 'Project2',
                    'path': '/projects/Project2',
                    'created': '2023-01-02T00:00:00Z'
                }
            ]
        }
        mock_axios.get.return_value = mock_response
        
        # Call refreshProjects
        refreshProjects(self.mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify webview received project list
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        project_messages = [msg for msg in messages if msg.get('command') == 'projectsList']
        assert len(project_messages) == 1
        
        projects_data = project_messages[0]['data']
        assert len(projects_data) == 2
        assert projects_data[0]['name'] == 'Project1'
    
    @patch('src.aetherforge.vscode')
    def test_save_settings(self, mock_vscode_module):
        """Test settings save functionality"""
        from src.aetherforge import saveSettings
        
        # Mock workspace configuration
        mock_config = Mock()
        mock_vscode_module.workspace.getConfiguration.return_value = mock_config
        mock_vscode_module.ConfigurationTarget.Workspace = 'workspace'
        
        settings = {
            'orchestratorUrl': 'http://localhost:8000',
            'projectsPath': './projects'
        }
        
        # Call saveSettings
        saveSettings(settings, self.mock_webview)
        
        # Verify configuration was updated
        mock_config.update.assert_called()
        
        # Verify success message was sent
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        success_messages = [msg for msg in messages if 'Settings saved' in msg.get('message', '')]
        assert len(success_messages) >= 1
    
    @patch('src.aetherforge.axios')
    def test_test_connection_success(self, mock_axios):
        """Test successful connection test"""
        from src.aetherforge import testConnection
        
        # Mock successful response
        mock_response = {
            'data': {'status': 'healthy'}
        }
        mock_axios.get.return_value = mock_response
        
        # Call testConnection
        testConnection('http://localhost:8000', self.mock_webview)
        
        # Verify API was called with health endpoint
        mock_axios.get.assert_called_once()
        call_args = mock_axios.get.call_args
        assert '/health' in call_args[0][0]
        
        # Verify success message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        connection_messages = [msg for msg in messages if msg.get('command') == 'connectionTest']
        assert len(connection_messages) == 1
        assert connection_messages[0]['success'] is True
    
    @patch('src.aetherforge.axios')
    def test_test_connection_failure(self, mock_axios):
        """Test failed connection test"""
        from src.aetherforge import testConnection
        
        # Mock connection failure
        mock_axios.get.side_effect = Exception('Connection failed')
        
        # Call testConnection
        testConnection('http://invalid-url:8000', self.mock_webview)
        
        # Verify failure message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        connection_messages = [msg for msg in messages if msg.get('command') == 'connectionTest']
        assert len(connection_messages) == 1
        assert connection_messages[0]['success'] is False
        assert 'error' in connection_messages[0]


class TestVSCodeExtensionHelpers:
    """Test helper functions in VS Code extension"""
    
    @patch('src.aetherforge.fs')
    @patch('src.aetherforge.vscode')
    def test_open_projects_folder(self, mock_vscode_module, mock_fs):
        """Test opening projects folder"""
        from src.aetherforge import openProjectsFolder
        
        # Mock file system and VS Code
        mock_fs.existsSync.return_value = True
        mock_vscode_module.workspace.workspaceFolders = [
            Mock(uri=Mock(fsPath='/workspace'))
        ]
        mock_vscode_module.Uri.file.return_value = Mock()
        
        # Call openProjectsFolder
        openProjectsFolder()
        
        # Verify VS Code command was executed
        mock_vscode_module.commands.executeCommand.assert_called_once()
        call_args = mock_vscode_module.commands.executeCommand.call_args
        assert call_args[0][0] == 'vscode.openFolder'
    
    @patch('src.aetherforge.vscode')
    def test_open_project(self, mock_vscode_module):
        """Test opening a specific project"""
        from src.aetherforge import openProject
        
        # Mock VS Code
        mock_vscode_module.Uri.file.return_value = Mock()
        
        # Call openProject
        openProject('/path/to/project')
        
        # Verify VS Code command was executed
        mock_vscode_module.commands.executeCommand.assert_called_once()
        call_args = mock_vscode_module.commands.executeCommand.call_args
        assert call_args[0][0] == 'vscode.openFolder'
    
    @patch('src.aetherforge.axios')
    def test_view_project(self, mock_axios):
        """Test viewing project details"""
        from src.aetherforge import viewProject
        
        # Mock API response
        mock_response = {
            'data': [
                {
                    'name': 'TestProject',
                    'created': '2023-01-01T00:00:00Z',
                    'status': 'completed'
                }
            ]
        }
        mock_axios.get.return_value = mock_response
        
        mock_webview = Mock()
        
        # Call viewProject
        viewProject('TestProject', mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify status message was sent
        messages = [call[0][0] for call in mock_webview.postMessage.call_args_list]
        status_messages = [msg for msg in messages if msg.get('command') == 'updateStatus']
        assert len(status_messages) >= 1


class TestVSCodeExtensionMessageHandling:
    """Test message handling in VS Code extension"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_webview = Mock()
        self.mock_context = Mock()
        self.mock_context.subscriptions = []
    
    @patch('src.aetherforge.createProject')
    @patch('src.aetherforge.checkSystemStatus')
    @patch('src.aetherforge.refreshProjects')
    def test_message_routing(self, mock_refresh, mock_status, mock_create):
        """Test that messages are routed to correct handlers"""
        from src.aetherforge import setupWebviewMessageHandling
        
        # Create mock panel
        mock_panel = Mock()
        mock_panel.webview = self.mock_webview
        
        # Setup message handling
        setupWebviewMessageHandling(mock_panel, self.mock_context)
        
        # Get the message handler
        assert self.mock_webview.onDidReceiveMessage.called
        message_handler = self.mock_webview.onDidReceiveMessage.call_args[0][0]
        
        # Test createProject message
        message_handler({
            'command': 'createProject',
            'prompt': 'Test prompt',
            'projectName': 'TestProject',
            'projectType': 'fullstack'
        })
        mock_create.assert_called_once()
        
        # Test checkSystemStatus message
        message_handler({'command': 'checkSystemStatus'})
        mock_status.assert_called_once()
        
        # Test refreshProjects message
        message_handler({'command': 'refreshProjects'})
        mock_refresh.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
