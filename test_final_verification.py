#!/usr/bin/env python3
"""
Final verification test for Aetherforge project completion
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, 'src')

async def test_project_generation():
    """Test the project generation functionality"""
    print("🔄 Testing Project Generation...")
    
    try:
        from project_generator_standalone import ProjectGenerator
        
        generator = ProjectGenerator()
        result = await generator.generate_project(
            prompt="Create a simple task management web application with user authentication",
            project_name="FinalTestApp",
            project_type="fullstack",
            project_path="./test_output"
        )
        
        print(f"✅ Project Generation Success: {result.get('success')}")
        print(f"📁 Files Created: {len(result.get('files_created', []))}")
        print(f"🔄 Phases Completed: {result.get('phases_completed')}")
        
        # Check if project directory exists
        project_path = Path("./test_output/FinalTestApp")
        if project_path.exists():
            print(f"✅ Project directory created: {project_path}")
            
            # Check key files
            key_files = ["README.md", "package.json", "src/App.tsx"]
            for file_name in key_files:
                file_path = project_path / file_name
                if file_path.exists():
                    print(f"✅ Key file exists: {file_name}")
                else:
                    print(f"❌ Missing key file: {file_name}")
        else:
            print(f"❌ Project directory not created: {project_path}")
            
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Project Generation Error: {e}")
        return False

def test_orchestrator_functionality():
    """Test orchestrator functionality"""
    print("🔄 Testing Orchestrator Functionality...")

    try:
        from orchestrator import app, create_project
        print("✅ Orchestrator app imported successfully")

        # Test that FastAPI app is properly configured
        if hasattr(app, 'routes') and len(app.routes) > 0:
            print(f"✅ Orchestrator has {len(app.routes)} routes configured")
        else:
            print("❌ Orchestrator routes not properly configured")
            return False

        # Test key functions exist
        if callable(create_project):
            print("✅ create_project function available")
        else:
            print("❌ create_project function not available")
            return False

        return True
    except Exception as e:
        print(f"❌ Orchestrator functionality error: {e}")
        return False

def test_component_imports():
    """Test that all components can be imported"""
    print("🔄 Testing Component Imports...")

    components = [
        ("orchestrator", "app"),
        ("pheromone_system", "get_pheromone_system"),
        ("workflow_engine", "get_workflow_engine"),
        ("agent_executors", "AgentExecutor"),
        ("config_manager", "ConfigurationManager"),
        ("component_adapters_real", "ArchonAdapter")
    ]

    success_count = 0
    for module_name, item_name in components:
        try:
            module = __import__(module_name)
            if hasattr(module, item_name):
                print(f"✅ {module_name}.{item_name} imported successfully")
                success_count += 1
            else:
                print(f"❌ {module_name} missing {item_name}")
        except Exception as e:
            print(f"❌ Failed to import {module_name}: {e}")

    print(f"📊 Component Import Success: {success_count}/{len(components)}")
    return success_count == len(components)

def test_file_structure():
    """Test that all required files exist"""
    print("🔄 Testing File Structure...")
    
    required_files = [
        "src/orchestrator.py",
        "src/pheromone_system.py", 
        "src/workflow_engine.py",
        "src/agent_executors.py",
        "src/project_generator_standalone.py",
        "src/config_manager.py",
        "src/component_adapters_real.py",
        "src/aetherforge.ts",
        "vscode-extension/package.json",
        "verify_aetherforge.py"
    ]
    
    success_count = 0
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} exists")
            success_count += 1
        else:
            print(f"❌ Missing file: {file_path}")
    
    print(f"📊 File Structure Success: {success_count}/{len(required_files)}")
    return success_count == len(required_files)

async def main():
    """Run all verification tests"""
    print("🔮 AETHERFORGE FINAL VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Component Imports", test_component_imports),
        ("Orchestrator Functionality", test_orchestrator_functionality),
        ("Project Generation", test_project_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
            
        results.append((test_name, result))
        print(f"Result: {'✅ PASSED' if result else '❌ FAILED'}")
    
    print("\n" + "=" * 50)
    print("📊 FINAL VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 AETHERFORGE PROJECT IS 100% COMPLETE! 🎉")
        return True
    else:
        print(f"\n⚠️  AETHERFORGE PROJECT IS {passed/total*100:.1f}% COMPLETE")
        print("Some components need attention.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
