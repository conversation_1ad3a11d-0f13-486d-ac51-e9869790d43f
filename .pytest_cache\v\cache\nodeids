["tests/test_archon_integration.py::TestAgentTypes::test_agent_lifecycle_stage_enum", "tests/test_archon_integration.py::TestAgentTypes::test_agent_type_enum", "tests/test_archon_integration.py::TestAgentTypes::test_optimization_strategy_enum", "tests/test_archon_integration.py::TestArchonConfig::test_agent_type_templates", "tests/test_archon_integration.py::TestArchonConfig::test_archon_config_initialization", "tests/test_archon_integration.py::TestArchonConfig::test_archon_mode_config", "tests/test_archon_integration.py::TestArchonConfig::test_lifecycle_stage_configs", "tests/test_archon_integration.py::TestArchonConfig::test_optimization_strategies", "tests/test_archon_integration.py::TestArchonIntegration::test_agent_evolution_plan_creation", "tests/test_archon_integration.py::TestArchonIntegration::test_agent_metrics_creation", "tests/test_archon_integration.py::TestArchonIntegration::test_agent_specification_creation", "tests/test_archon_integration.py::TestArchonIntegration::test_agent_team_composition_creation", "tests/test_archon_integration.py::TestArchonIntegration::test_archon_integration_initialization", "tests/test_archon_integration.py::TestArchonIntegration::test_assess_lifecycle_health", "tests/test_archon_integration.py::TestArchonIntegration::test_determine_lifecycle_stage", "tests/test_archon_integration.py::TestArchonIntegration::test_fallback_team_generation", "tests/test_archon_integration.py::TestArchonIntegration::test_parse_agent_metrics", "tests/test_archon_integration.py::TestArchonIntegration::test_parse_evolution_plan", "tests/test_archon_integration.py::TestArchonIntegration::test_parse_team_composition", "tests/test_archon_integration.py::TestArchonIntegrationExecutor::test_execute_agent_generation_data_structure", "tests/test_archon_integration.py::TestArchonIntegrationExecutor::test_executor_initialization", "tests/test_developer_agent.py::TestCodeGeneration::test_code_file_creation", "tests/test_developer_agent.py::TestCodeGeneration::test_test_suite_creation", "tests/test_developer_agent.py::TestDeveloperAgent::test_code_generation_context_creation", "tests/test_developer_agent.py::TestDeveloperAgent::test_developer_agent_initialization", "tests/test_developer_agent.py::TestDeveloperAgent::test_error_handling", "tests/test_developer_agent.py::TestDeveloperAgent::test_input_validation_missing_tech_stack", "tests/test_developer_agent.py::TestDeveloperAgent::test_input_validation_success", "tests/test_developer_agent.py::TestDeveloperAgent::test_project_structure_generation", "tests/test_developer_agent.py::TestDeveloperAgent::test_technology_stack_processing", "tests/test_developer_agent.py::TestQualityGates::test_code_quality_enum", "tests/test_developer_agent.py::TestQualityGates::test_project_type_enum", "tests/test_orchestrator.py::TestAgentExecution::test_developer_agent_execution", "tests/test_orchestrator.py::TestAgentExecution::test_qa_agent_execution", "tests/test_qa_agent.py::TestQAAgent::test_coverage_calculation", "tests/test_qa_agent.py::TestQAAgent::test_input_validation_missing_project", "tests/test_qa_agent.py::TestQAAgent::test_input_validation_success", "tests/test_qa_agent.py::TestQAAgent::test_jest_output_parsing", "tests/test_qa_agent.py::TestQAAgent::test_performance_keyword_detection", "tests/test_qa_agent.py::TestQAAgent::test_project_structure_analysis", "tests/test_qa_agent.py::TestQAAgent::test_qa_agent_initialization", "tests/test_qa_agent.py::TestQAAgent::test_qa_context_creation", "tests/test_qa_agent.py::TestQAAgent::test_quality_report_creation", "tests/test_qa_agent.py::TestQAAgent::test_security_keyword_detection", "tests/test_qa_agent.py::TestQAAgent::test_test_framework_detection", "tests/test_qa_agent.py::TestQAAgent::test_test_result_creation", "tests/test_qa_agent.py::TestQAAgent::test_test_suite_creation", "tests/test_qa_agent.py::TestQAAgentExecutor::test_executor_initialization", "tests/test_qa_agent.py::TestQAAgentExecutor::test_specification_data_conversion", "tests/test_qa_agent.py::TestQualityEnums::test_quality_level_enum", "tests/test_qa_agent.py::TestQualityEnums::test_test_status_enum", "tests/test_qa_agent.py::TestQualityEnums::test_test_type_enum", "tests/test_research_engine.py::TestResearchConfig::test_research_config_initialization", "tests/test_research_engine.py::TestResearchConfig::test_research_mode_config", "tests/test_research_engine.py::TestResearchConfig::test_source_priorities", "tests/test_research_engine.py::TestResearchConfig::test_technology_keywords", "tests/test_research_engine.py::TestResearchEngine::test_crawl_request_creation", "tests/test_research_engine.py::TestResearchEngine::test_deduplicate_results", "tests/test_research_engine.py::TestResearchEngine::test_expand_query", "tests/test_research_engine.py::TestResearchEngine::test_extract_domain", "tests/test_research_engine.py::TestResearchEngine::test_generate_cache_key", "tests/test_research_engine.py::TestResearchEngine::test_get_priority_value", "tests/test_research_engine.py::TestResearchEngine::test_query_pattern_initialization", "tests/test_research_engine.py::TestResearchEngine::test_rank_results", "tests/test_research_engine.py::TestResearchEngine::test_research_context_creation", "tests/test_research_engine.py::TestResearchEngine::test_research_engine_initialization", "tests/test_research_engine.py::TestResearchEngine::test_research_query_creation", "tests/test_research_engine.py::TestResearchEngine::test_research_result_creation", "tests/test_research_engine.py::TestResearchEngine::test_research_template_initialization", "tests/test_research_engine.py::TestResearchEngine::test_source_priority_initialization", "tests/test_research_engine.py::TestResearchEngineExecutor::test_execute_research_data_conversion", "tests/test_research_engine.py::TestResearchEngineExecutor::test_executor_initialization", "tests/test_research_engine.py::TestResearchTypes::test_research_priority_enum", "tests/test_research_engine.py::TestResearchTypes::test_research_type_enum"]