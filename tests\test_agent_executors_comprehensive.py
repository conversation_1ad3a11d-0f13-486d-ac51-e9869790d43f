"""
Comprehensive test suite for Agent Executors
Tests all BMAD agents and their execution patterns
"""

import pytest
import asyncio
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock

# Import agent executor components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent_executors import (
    AgentExecutor, create_agent_executor,
    execute_analyst_agent, execute_architect_agent,
    execute_developer_work, execute_qa_work
)


class TestAgentExecutor:
    """Test the base AgentExecutor class"""
    
    def test_executor_creation(self):
        """Test creating an agent executor"""
        executor = AgentExecutor(
            role="analyst",
            model="gpt-4",
            temperature=0.7,
            max_tokens=2000
        )
        
        assert executor.role == "analyst"
        assert executor.model == "gpt-4"
        assert executor.temperature == 0.7
        assert executor.max_tokens == 2000
        assert executor.execution_history == []
    
    def test_executor_configuration(self):
        """Test executor configuration management"""
        executor = AgentExecutor(role="analyst")
        
        # Test default configuration
        config = executor.get_configuration()
        assert config["role"] == "analyst"
        assert config["model"] == "gpt-4"
        assert "temperature" in config
        assert "max_tokens" in config
        
        # Test configuration update
        executor.update_configuration(temperature=0.5, max_tokens=1500)
        updated_config = executor.get_configuration()
        assert updated_config["temperature"] == 0.5
        assert updated_config["max_tokens"] == 1500
    
    @pytest.mark.asyncio
    async def test_executor_context_preparation(self):
        """Test preparing execution context"""
        executor = AgentExecutor(role="analyst")
        
        context = {
            "prompt": "Create a web application",
            "project_path": "/test/path",
            "project_type": "fullstack"
        }
        
        prepared_context = await executor.prepare_context(context)
        
        assert "prompt" in prepared_context
        assert "project_path" in prepared_context
        assert "project_type" in prepared_context
        assert "role" in prepared_context
        assert prepared_context["role"] == "analyst"
    
    @pytest.mark.asyncio
    async def test_executor_error_handling(self):
        """Test error handling in agent execution"""
        executor = AgentExecutor(role="analyst")
        
        # Mock OpenAI call to raise an exception
        with patch.object(executor, 'call_openai', side_effect=Exception("API Error")):
            context = {"prompt": "Test prompt", "project_path": "/test"}
            
            result = await executor.execute(context)
            
            assert result["success"] is False
            assert "error" in result
            assert "API Error" in result["error"]
    
    @pytest.mark.asyncio
    async def test_executor_retry_mechanism(self):
        """Test retry mechanism for failed executions"""
        executor = AgentExecutor(role="analyst", max_retries=3)
        
        # Mock OpenAI call to fail twice, then succeed
        call_count = 0
        def mock_openai_call(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary failure")
            return "Success response"
        
        with patch.object(executor, 'call_openai', side_effect=mock_openai_call):
            context = {"prompt": "Test prompt", "project_path": "/test"}
            
            result = await executor.execute(context)
            
            assert result["success"] is True
            assert call_count == 3  # Failed twice, succeeded on third try
    
    def test_execution_history_tracking(self):
        """Test tracking execution history"""
        executor = AgentExecutor(role="analyst")
        
        # Add execution records
        executor.add_execution_record({
            "context": {"prompt": "Test 1"},
            "result": {"success": True},
            "duration": 5.2
        })
        
        executor.add_execution_record({
            "context": {"prompt": "Test 2"},
            "result": {"success": False},
            "duration": 2.1
        })
        
        assert len(executor.execution_history) == 2
        
        # Test statistics
        stats = executor.get_execution_statistics()
        assert stats["total_executions"] == 2
        assert stats["success_rate"] == 0.5
        assert stats["average_duration"] == 3.65


class TestAnalystAgent:
    """Test the Requirements Analyst agent"""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create temporary project directory"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_analyst_execution(self, temp_project_dir):
        """Test analyst agent execution"""
        prompt = "Create a task management application with user authentication"
        
        # Mock OpenAI response
        mock_response = """
        # Requirements Analysis
        
        ## Functional Requirements
        1. User registration and authentication
        2. Task creation and management
        3. Task assignment and tracking
        
        ## Non-Functional Requirements
        1. Security: Secure authentication
        2. Performance: Fast response times
        3. Scalability: Support multiple users
        """
        
        with patch('src.agent_executors.call_openai_api', return_value=mock_response):
            result = execute_analyst_agent(prompt, temp_project_dir)
            
            assert result["success"] is True
            assert "outputs" in result
            assert len(result["outputs"]) > 0
            
            # Check if requirements file was created
            docs_dir = Path(temp_project_dir) / "docs"
            assert docs_dir.exists()
            assert (docs_dir / "requirements.md").exists()
            
            # Verify content
            requirements_content = (docs_dir / "requirements.md").read_text()
            assert "Functional Requirements" in requirements_content
            assert "Non-Functional Requirements" in requirements_content
    
    @pytest.mark.asyncio
    async def test_analyst_with_complex_requirements(self, temp_project_dir):
        """Test analyst with complex requirements"""
        prompt = """
        Create an e-commerce platform with:
        - Multi-vendor support
        - Real-time inventory management
        - Payment processing with multiple gateways
        - Advanced search and filtering
        - Mobile app support
        - Admin dashboard with analytics
        """
        
        mock_response = """
        # E-commerce Platform Requirements
        
        ## Core Features
        1. Multi-vendor marketplace
        2. Inventory management system
        3. Payment gateway integration
        4. Search and filtering system
        5. Mobile application
        6. Analytics dashboard
        
        ## Technical Requirements
        1. Microservices architecture
        2. Real-time data synchronization
        3. Scalable database design
        4. API-first approach
        """
        
        with patch('src.agent_executors.call_openai_api', return_value=mock_response):
            result = execute_analyst_agent(prompt, temp_project_dir)
            
            assert result["success"] is True
            
            # Verify multiple output files
            docs_dir = Path(temp_project_dir) / "docs"
            expected_files = ["requirements.md", "user_stories.md", "acceptance_criteria.md"]
            
            for file_name in expected_files:
                if (docs_dir / file_name).exists():
                    assert file_name in result["outputs"]
    
    def test_analyst_error_handling(self, temp_project_dir):
        """Test analyst error handling"""
        prompt = "Invalid prompt"
        
        # Mock API failure
        with patch('src.agent_executors.call_openai_api', side_effect=Exception("API Error")):
            result = execute_analyst_agent(prompt, temp_project_dir)
            
            assert result["success"] is False
            assert "error" in result


class TestArchitectAgent:
    """Test the System Architect agent"""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create temporary project directory"""
        temp_dir = tempfile.mkdtemp()
        # Create docs directory with requirements
        docs_dir = Path(temp_dir) / "docs"
        docs_dir.mkdir()
        (docs_dir / "requirements.md").write_text("# Requirements\nBasic requirements")
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_architect_execution(self, temp_project_dir):
        """Test architect agent execution"""
        prompt = "Design architecture for task management application"
        
        mock_response = """
        # System Architecture
        
        ## Technology Stack
        - Frontend: React with TypeScript
        - Backend: Node.js with Express
        - Database: PostgreSQL
        - Authentication: JWT
        
        ## Architecture Patterns
        - RESTful API design
        - MVC pattern
        - Database normalization
        
        ## System Components
        1. User Management Service
        2. Task Management Service
        3. Notification Service
        """
        
        with patch('src.agent_executors.call_openai_api', return_value=mock_response):
            result = execute_architect_agent(prompt, temp_project_dir)
            
            assert result["success"] is True
            assert "outputs" in result
            
            # Check if architecture files were created
            docs_dir = Path(temp_project_dir) / "docs"
            assert (docs_dir / "architecture.md").exists()
            
            # Verify content
            arch_content = (docs_dir / "architecture.md").read_text()
            assert "Technology Stack" in arch_content
            assert "System Components" in arch_content
    
    @pytest.mark.asyncio
    async def test_architect_with_microservices(self, temp_project_dir):
        """Test architect with microservices design"""
        prompt = "Design microservices architecture for e-commerce platform"
        
        mock_response = """
        # Microservices Architecture
        
        ## Services
        1. User Service
        2. Product Service
        3. Order Service
        4. Payment Service
        5. Inventory Service
        
        ## Communication
        - REST APIs for synchronous communication
        - Message queues for asynchronous processing
        - Event-driven architecture
        
        ## Data Management
        - Database per service
        - Event sourcing for audit trails
        """
        
        with patch('src.agent_executors.call_openai_api', return_value=mock_response):
            result = execute_architect_agent(prompt, temp_project_dir)
            
            assert result["success"] is True
            
            # Check for additional architecture files
            docs_dir = Path(temp_project_dir) / "docs"
            expected_files = ["architecture.md", "api_design.md", "database_schema.md"]
            
            for file_name in expected_files:
                if (docs_dir / file_name).exists():
                    assert file_name in result["outputs"]


class TestDeveloperAgent:
    """Test the Full Stack Developer agent"""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create temporary project directory with architecture"""
        temp_dir = tempfile.mkdtemp()
        docs_dir = Path(temp_dir) / "docs"
        docs_dir.mkdir()
        (docs_dir / "requirements.md").write_text("# Requirements\nTask management app")
        (docs_dir / "architecture.md").write_text("# Architecture\nReact + Node.js")
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_developer_execution(self, temp_project_dir):
        """Test developer agent execution"""
        context = {
            "prompt": "Implement task management application",
            "project_path": temp_project_dir,
            "project_id": "test_project",
            "phase": "core_development"
        }
        
        # Mock file generation
        mock_files = {
            "package.json": '{"name": "task-app", "version": "1.0.0"}',
            "server/index.js": "const express = require('express');",
            "client/src/App.jsx": "import React from 'react';"
        }
        
        with patch('src.file_generators.generate_project_files', return_value=mock_files):
            result = await execute_developer_work(context)
            
            assert result["success"] is True
            assert "outputs" in result
            assert len(result["outputs"]) > 0
            assert "files_created" in result
    
    @pytest.mark.asyncio
    async def test_developer_with_database(self, temp_project_dir):
        """Test developer with database implementation"""
        context = {
            "prompt": "Implement with PostgreSQL database",
            "project_path": temp_project_dir,
            "project_id": "test_project",
            "phase": "database_setup"
        }
        
        mock_files = {
            "database/schema.sql": "CREATE TABLE users (...);",
            "database/migrations/001_initial.sql": "-- Initial migration",
            "server/models/User.js": "class User { ... }"
        }
        
        with patch('src.file_generators.generate_project_files', return_value=mock_files):
            result = await execute_developer_work(context)
            
            assert result["success"] is True
            assert any("schema.sql" in output for output in result["outputs"])


class TestQAAgent:
    """Test the Quality Assurance agent"""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create temporary project directory with code"""
        temp_dir = tempfile.mkdtemp()
        
        # Create project structure
        src_dir = Path(temp_dir) / "src"
        src_dir.mkdir()
        (src_dir / "app.js").write_text("console.log('Hello World');")
        
        tests_dir = Path(temp_dir) / "tests"
        tests_dir.mkdir()
        
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_qa_execution(self, temp_project_dir):
        """Test QA agent execution"""
        context = {
            "prompt": "Test task management application",
            "project_path": temp_project_dir,
            "project_id": "test_project",
            "phase": "qa_validation",
            "quality_level": "standard"
        }
        
        # Mock QA analysis
        mock_qa_result = {
            "quality_score": 85,
            "test_coverage": 75,
            "code_quality": 90,
            "security_score": 80,
            "recommendations": [
                "Add more unit tests",
                "Implement input validation"
            ]
        }
        
        with patch('src.qa_agent.analyze_code_quality', return_value=mock_qa_result):
            result = await execute_qa_work(context)
            
            # QA might fall back to basic mode, so we check for either success or fallback
            if result["success"]:
                assert "quality_score" in result
                assert "outputs" in result
            else:
                # Basic fallback should still provide some results
                assert "error" in result or "fallback" in result


class TestAgentFactory:
    """Test agent creation and factory methods"""
    
    def test_create_agent_executor(self):
        """Test creating different types of agent executors"""
        roles = ["analyst", "architect", "developer", "qa"]
        
        for role in roles:
            executor = create_agent_executor(role)
            assert executor is not None
            assert executor.role == role
            assert isinstance(executor, AgentExecutor)
    
    def test_create_agent_with_custom_config(self):
        """Test creating agent with custom configuration"""
        config = {
            "model": "gpt-3.5-turbo",
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        executor = create_agent_executor("analyst", **config)
        
        assert executor.model == "gpt-3.5-turbo"
        assert executor.temperature == 0.3
        assert executor.max_tokens == 1000
    
    def test_invalid_agent_role(self):
        """Test handling invalid agent role"""
        with pytest.raises(ValueError):
            create_agent_executor("invalid_role")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
