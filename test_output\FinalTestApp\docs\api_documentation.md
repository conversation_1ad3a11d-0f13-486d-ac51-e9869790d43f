# API Documentation

## Overview
This document describes the API endpoints for the FinalTestApp application.

## Base URL
```
http://localhost:3001/api
```

## Authentication
All API endpoints require authentication via JWT tokens.

## Endpoints

### Health Check
```
GET /api/health
```
Returns the health status of the API.

### User Management
```
POST /api/auth/login
POST /api/auth/register
GET /api/auth/profile
PUT /api/auth/profile
```

### Data Operations
```
GET /api/data
POST /api/data
PUT /api/data/:id
DELETE /api/data/:id
```

## Error Handling
All endpoints return standardized error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

Generated: 2025-06-20T11:46:30.835161
