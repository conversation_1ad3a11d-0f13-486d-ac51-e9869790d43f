@tailwind base;
@tailwind components;
@tailwind utilities;

/* VS Code theme integration */
:root {
  --vscode-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --vscode-font-size: 13px;
  --vscode-font-weight: 400;
  --vscode-line-height: 1.4;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  font-weight: var(--vscode-font-weight);
  line-height: var(--vscode-line-height);
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background, #2d2d30);
}

::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background, #424242);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground, #4f4f4f);
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 1px solid var(--vscode-focusBorder, #007acc);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--vscode-editor-selectionBackground, #264f78);
  color: var(--vscode-editor-selectionForeground, #ffffff);
}

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation utilities */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Custom slider styling */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: var(--vscode-input-background, #3c3c3c);
  outline: none;
  border-radius: 5px;
  height: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--vscode-button-background, #0e639c);
  cursor: pointer;
  border: 2px solid var(--vscode-editor-background, #1e1e1e);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--vscode-button-background, #0e639c);
  cursor: pointer;
  border: 2px solid var(--vscode-editor-background, #1e1e1e);
}

/* Message bubble styles */
.message-bubble {
  max-width: 70%;
  word-wrap: break-word;
  border-radius: 12px;
  padding: 8px 12px;
  margin: 4px 0;
}

.message-bubble.user {
  background-color: var(--vscode-button-background, #0e639c);
  color: var(--vscode-button-foreground, #ffffff);
  margin-left: auto;
}

.message-bubble.agent {
  background-color: var(--vscode-input-background, #3c3c3c);
  color: var(--vscode-input-foreground, #cccccc);
  margin-right: auto;
}

.message-bubble.system {
  background-color: var(--vscode-badge-background, #4d4d4d);
  color: var(--vscode-badge-foreground, #ffffff);
  margin: 0 auto;
  text-align: center;
  font-style: italic;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-indicator.idle {
  background-color: var(--vscode-badge-background, #4d4d4d);
  color: var(--vscode-badge-foreground, #ffffff);
}

.status-indicator.working {
  background-color: #1f2937;
  color: #60a5fa;
}

.status-indicator.waiting {
  background-color: #451a03;
  color: #fbbf24;
}

.status-indicator.completed {
  background-color: #064e3b;
  color: #34d399;
}

.status-indicator.error {
  background-color: #7f1d1d;
  color: #f87171;
}

.status-indicator.blocked {
  background-color: #7c2d12;
  color: #fb923c;
}

/* Priority indicators */
.priority-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-indicator.low {
  background-color: #064e3b;
  color: #34d399;
}

.priority-indicator.medium {
  background-color: #451a03;
  color: #fbbf24;
}

.priority-indicator.high {
  background-color: #7c2d12;
  color: #fb923c;
}

.priority-indicator.critical {
  background-color: #7f1d1d;
  color: #f87171;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--vscode-input-background, #3c3c3c) 25%, 
    var(--vscode-list-hoverBackground, #2a2d2e) 50%, 
    var(--vscode-input-background, #3c3c3c) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--vscode-editorHoverWidget-background, #2d2d30);
  color: var(--vscode-editorHoverWidget-foreground, #cccccc);
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  border: 1px solid var(--vscode-editorHoverWidget-border, #454545);
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Code block styles */
.code-block {
  background-color: var(--vscode-textCodeBlock-background, #1e1e1e);
  border: 1px solid var(--vscode-panel-border, #2d2d30);
  border-radius: 4px;
  padding: 8px;
  font-family: var(--vscode-editor-font-family, 'Consolas', 'Courier New', monospace);
  font-size: var(--vscode-editor-font-size, 12px);
  overflow-x: auto;
  white-space: pre-wrap;
}

/* Reaction styles */
.reaction-button {
  background: none;
  border: none;
  padding: 2px 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.reaction-button:hover {
  background-color: var(--vscode-list-hoverBackground, #2a2d2e);
  transform: scale(1.1);
}

.reaction-button.active {
  background-color: var(--vscode-button-background, #0e639c);
}

/* Progress bar styles */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--vscode-progressBar-background, #0e70c0);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--vscode-button-background, #0e639c);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* Modal backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .tooltip .tooltip-text {
    width: 100px;
    margin-left: -50px;
  }
}
