"""
Comprehensive test suite for the Workflow Engine
Tests BMAD methodology implementation and workflow execution
"""

import pytest
import asyncio
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# Import workflow engine components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from workflow_engine import WorkflowEngine, WorkflowPhase, WorkflowStep, WorkflowContext


class TestWorkflowStep:
    """Test individual workflow steps"""
    
    def test_step_creation(self):
        """Test creating a workflow step"""
        step = WorkflowStep(
            name="test_step",
            description="Test step description",
            agent_role="analyst",
            inputs=["requirements"],
            outputs=["analysis.md"],
            dependencies=["previous_step"]
        )
        
        assert step.name == "test_step"
        assert step.description == "Test step description"
        assert step.agent_role == "analyst"
        assert step.inputs == ["requirements"]
        assert step.outputs == ["analysis.md"]
        assert step.dependencies == ["previous_step"]
        assert step.status == "pending"
    
    def test_step_validation(self):
        """Test step validation"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        # Valid step should pass validation
        assert step.validate() is True
        
        # Invalid step (empty name) should fail
        step.name = ""
        assert step.validate() is False
    
    def test_step_execution_tracking(self):
        """Test tracking step execution"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        # Initially pending
        assert step.status == "pending"
        assert step.start_time is None
        assert step.end_time is None
        
        # Start execution
        step.start_execution()
        assert step.status == "running"
        assert step.start_time is not None
        assert step.end_time is None
        
        # Complete execution
        step.complete_execution(success=True, outputs=["output.md"])
        assert step.status == "completed"
        assert step.end_time is not None
        assert step.outputs == ["output.md"]
        
        # Calculate duration
        duration = step.get_duration()
        assert duration is not None
        assert duration.total_seconds() >= 0
    
    def test_step_failure_handling(self):
        """Test handling step failures"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        step.start_execution()
        step.complete_execution(success=False, error="Test error")
        
        assert step.status == "failed"
        assert step.error == "Test error"
        assert step.end_time is not None


class TestWorkflowPhase:
    """Test workflow phases"""
    
    def test_phase_creation(self):
        """Test creating a workflow phase"""
        steps = [
            WorkflowStep("step1", "First step", "analyst"),
            WorkflowStep("step2", "Second step", "architect")
        ]
        
        phase = WorkflowPhase(
            name="analysis_phase",
            description="Analysis phase",
            steps=steps,
            parallel=False
        )
        
        assert phase.name == "analysis_phase"
        assert phase.description == "Analysis phase"
        assert len(phase.steps) == 2
        assert phase.parallel is False
        assert phase.status == "pending"
    
    def test_phase_dependency_resolution(self):
        """Test resolving step dependencies within a phase"""
        step1 = WorkflowStep("step1", "First step", "analyst")
        step2 = WorkflowStep("step2", "Second step", "architect", dependencies=["step1"])
        step3 = WorkflowStep("step3", "Third step", "developer", dependencies=["step1", "step2"])
        
        phase = WorkflowPhase(
            name="test_phase",
            description="Test phase",
            steps=[step1, step2, step3]
        )
        
        execution_order = phase.get_execution_order()
        
        # step1 should be first (no dependencies)
        assert execution_order[0] == step1
        # step2 should be second (depends on step1)
        assert execution_order[1] == step2
        # step3 should be last (depends on both step1 and step2)
        assert execution_order[2] == step3
    
    def test_phase_parallel_execution(self):
        """Test parallel execution capability"""
        step1 = WorkflowStep("step1", "First step", "analyst")
        step2 = WorkflowStep("step2", "Second step", "architect")
        step3 = WorkflowStep("step3", "Third step", "developer")
        
        phase = WorkflowPhase(
            name="parallel_phase",
            description="Parallel phase",
            steps=[step1, step2, step3],
            parallel=True
        )
        
        assert phase.can_run_parallel() is True
        
        # All steps without dependencies can run in parallel
        parallel_steps = phase.get_parallel_steps()
        assert len(parallel_steps) == 3
    
    def test_phase_progress_tracking(self):
        """Test tracking phase progress"""
        steps = [
            WorkflowStep("step1", "First step", "analyst"),
            WorkflowStep("step2", "Second step", "architect"),
            WorkflowStep("step3", "Third step", "developer")
        ]
        
        phase = WorkflowPhase(
            name="test_phase",
            description="Test phase",
            steps=steps
        )
        
        # Initially 0% complete
        assert phase.get_progress() == 0.0
        
        # Complete first step
        steps[0].status = "completed"
        assert phase.get_progress() == pytest.approx(33.33, rel=1e-2)
        
        # Complete second step
        steps[1].status = "completed"
        assert phase.get_progress() == pytest.approx(66.67, rel=1e-2)
        
        # Complete all steps
        steps[2].status = "completed"
        assert phase.get_progress() == 100.0
        
        # Phase should be completed
        assert phase.is_completed() is True


class TestWorkflowContext:
    """Test workflow execution context"""
    
    def test_context_creation(self):
        """Test creating workflow context"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path",
            project_type="fullstack",
            prompt="Create a web application"
        )
        
        assert context.project_id == "test_project"
        assert context.project_path == "/test/path"
        assert context.project_type == "fullstack"
        assert context.prompt == "Create a web application"
        assert context.variables == {}
        assert context.artifacts == {}
    
    def test_context_variables(self):
        """Test context variable management"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path"
        )
        
        # Set variables
        context.set_variable("database_type", "postgresql")
        context.set_variable("api_framework", "fastapi")
        
        assert context.get_variable("database_type") == "postgresql"
        assert context.get_variable("api_framework") == "fastapi"
        assert context.get_variable("nonexistent") is None
        assert context.get_variable("nonexistent", "default") == "default"
    
    def test_context_artifacts(self):
        """Test context artifact management"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path"
        )
        
        # Add artifacts
        context.add_artifact("requirements.md", "/path/to/requirements.md", "document")
        context.add_artifact("architecture.md", "/path/to/architecture.md", "document")
        
        assert len(context.artifacts) == 2
        assert "requirements.md" in context.artifacts
        assert context.artifacts["requirements.md"]["path"] == "/path/to/requirements.md"
        assert context.artifacts["requirements.md"]["type"] == "document"
    
    def test_context_serialization(self):
        """Test context serialization"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path",
            project_type="fullstack"
        )
        
        context.set_variable("test_var", "test_value")
        context.add_artifact("test.md", "/test.md", "document")
        
        # Serialize to dict
        data = context.to_dict()
        
        assert data["project_id"] == "test_project"
        assert data["project_path"] == "/test/path"
        assert data["project_type"] == "fullstack"
        assert data["variables"]["test_var"] == "test_value"
        assert "test.md" in data["artifacts"]
        
        # Create from dict
        new_context = WorkflowContext.from_dict(data)
        
        assert new_context.project_id == "test_project"
        assert new_context.get_variable("test_var") == "test_value"
        assert "test.md" in new_context.artifacts


class TestWorkflowEngine:
    """Test the main workflow engine"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    def test_engine_initialization(self):
        """Test workflow engine initialization"""
        engine = WorkflowEngine()
        
        assert engine.workflows == {}
        assert engine.active_executions == {}
        assert engine.execution_history == []
    
    def test_workflow_registration(self):
        """Test registering workflows"""
        engine = WorkflowEngine()
        
        # Create test workflow
        steps = [
            WorkflowStep("analysis", "Requirements analysis", "analyst"),
            WorkflowStep("design", "System design", "architect")
        ]
        phase = WorkflowPhase("development", "Development phase", steps)
        
        workflow_config = {
            "name": "test_workflow",
            "description": "Test workflow",
            "phases": [phase],
            "project_types": ["fullstack"]
        }
        
        engine.register_workflow("test_workflow", workflow_config)
        
        assert "test_workflow" in engine.workflows
        assert engine.workflows["test_workflow"]["name"] == "test_workflow"
    
    def test_workflow_selection(self):
        """Test selecting appropriate workflow for project type"""
        engine = WorkflowEngine()
        
        # Register workflows for different project types
        fullstack_workflow = {
            "name": "fullstack_workflow",
            "description": "Full stack workflow",
            "phases": [],
            "project_types": ["fullstack", "web"]
        }
        
        api_workflow = {
            "name": "api_workflow",
            "description": "API workflow",
            "phases": [],
            "project_types": ["api", "backend"]
        }
        
        engine.register_workflow("fullstack", fullstack_workflow)
        engine.register_workflow("api", api_workflow)
        
        # Test workflow selection
        selected = engine.select_workflow("fullstack")
        assert selected["name"] == "fullstack_workflow"
        
        selected = engine.select_workflow("api")
        assert selected["name"] == "api_workflow"
        
        # Test fallback to default
        selected = engine.select_workflow("unknown_type")
        assert selected is not None  # Should return a default workflow
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self, temp_dir):
        """Test executing a complete workflow"""
        engine = WorkflowEngine()
        
        # Create mock agent executor
        mock_executor = Mock()
        mock_executor.execute = AsyncMock(return_value={
            "success": True,
            "outputs": ["test_output.md"],
            "summary": "Step completed successfully"
        })
        
        # Create test workflow
        steps = [
            WorkflowStep("step1", "First step", "analyst", outputs=["analysis.md"]),
            WorkflowStep("step2", "Second step", "architect", outputs=["design.md"])
        ]
        phase = WorkflowPhase("test_phase", "Test phase", steps)
        
        workflow_config = {
            "name": "test_workflow",
            "description": "Test workflow",
            "phases": [phase],
            "project_types": ["test"]
        }
        
        engine.register_workflow("test", workflow_config)
        
        # Create execution context
        context = WorkflowContext(
            project_id="test_project",
            project_path=temp_dir,
            project_type="test",
            prompt="Test project"
        )
        
        # Mock agent creation
        with patch('src.agent_executors.create_agent_executor', return_value=mock_executor):
            execution_id = await engine.execute_workflow("test", context)
            
            assert execution_id is not None
            assert execution_id in engine.active_executions
            
            # Wait for execution to complete
            result = await engine.wait_for_completion(execution_id, timeout=10)
            
            assert result["success"] is True
            assert result["phases_completed"] == 1
            assert execution_id not in engine.active_executions
            assert execution_id in [e["execution_id"] for e in engine.execution_history]


class TestWorkflowTemplateManager:
    """Test workflow template management"""

    def test_template_manager_initialization(self):
        """Test template manager initialization"""
        from src.workflow_engine import WorkflowTemplateManager

        manager = WorkflowTemplateManager()

        # Should have built-in templates
        assert len(manager.templates) > 0
        assert 'greenfield-fullstack' in manager.templates
        assert 'greenfield-service' in manager.templates

        # Should have categories
        assert len(manager.template_categories) > 0
        assert 'greenfield' in manager.template_categories
        assert 'brownfield' in manager.template_categories

    def test_template_search(self):
        """Test template search functionality"""
        from src.workflow_engine import WorkflowTemplateManager

        manager = WorkflowTemplateManager()

        # Search for fullstack templates
        results = manager.search_templates("fullstack")
        assert len(results) > 0
        assert any("fullstack" in r['name'].lower() for r in results)

        # Search with project type filter
        results = manager.search_templates("web", project_type="fullstack")
        assert len(results) > 0

    def test_custom_template_management(self):
        """Test custom template creation and management"""
        from src.workflow_engine import WorkflowTemplateManager, WorkflowDefinition

        manager = WorkflowTemplateManager()

        # Create a custom template
        custom_template = WorkflowDefinition(
            id="custom-test",
            name="Custom Test Template",
            version="1.0.0",
            description="Test custom template"
        )

        # Add custom template
        manager.add_custom_template("custom-test", custom_template)
        assert "custom-test" in manager.custom_templates

        # Get custom template
        retrieved = manager.get_template("custom-test")
        assert retrieved is not None
        assert retrieved.name == "Custom Test Template"

        # Remove custom template
        success = manager.remove_custom_template("custom-test")
        assert success is True
        assert "custom-test" not in manager.custom_templates


class TestConditionalWorkflows:
    """Test conditional workflow execution"""

    @pytest.mark.asyncio
    async def test_simple_condition_evaluation(self):
        """Test simple condition evaluation"""
        from src.workflow_engine import WorkflowExecutionEngine, StepCondition, ConditionOperator, WorkflowExecution, WorkflowVariable

        engine = WorkflowExecutionEngine()

        # Create test execution with variables
        execution = WorkflowExecution(
            id="test_exec",
            workflow_id="test",
            status="running",
            started_at=1234567890,
            project_id="test_project",
            variables={
                "test_var": WorkflowVariable(name="test_var", value="test_value", type="string")
            }
        )

        # Test equality condition
        condition = StepCondition(
            variable="test_var",
            operator=ConditionOperator.EQUALS,
            value="test_value"
        )

        result = await engine._evaluate_condition(condition, execution)
        assert result is True

        # Test inequality condition
        condition.value = "different_value"
        result = await engine._evaluate_condition(condition, execution)
        assert result is False

    @pytest.mark.asyncio
    async def test_complex_condition_evaluation(self):
        """Test complex condition evaluation with logical operators"""
        from src.workflow_engine import WorkflowExecutionEngine, StepCondition, ConditionOperator, WorkflowExecution, WorkflowVariable

        engine = WorkflowExecutionEngine()

        # Create test execution with variables
        execution = WorkflowExecution(
            id="test_exec",
            workflow_id="test",
            status="running",
            started_at=1234567890,
            project_id="test_project",
            variables={
                "var1": WorkflowVariable(name="var1", value="value1", type="string"),
                "var2": WorkflowVariable(name="var2", value="value2", type="string")
            }
        )

        # Create AND condition
        condition1 = StepCondition(
            variable="var1",
            operator=ConditionOperator.EQUALS,
            value="value1"
        )

        condition2 = StepCondition(
            variable="var2",
            operator=ConditionOperator.EQUALS,
            value="value2"
        )

        and_condition = StepCondition(
            variable="",
            operator=ConditionOperator.AND,
            value=None,
            sub_conditions=[condition1, condition2]
        )

        result = await engine._evaluate_condition(and_condition, execution)
        assert result is True

        # Test OR condition with one false
        condition2.value = "wrong_value"
        or_condition = StepCondition(
            variable="",
            operator=ConditionOperator.OR,
            value=None,
            sub_conditions=[condition1, condition2]
        )

        result = await engine._evaluate_condition(or_condition, execution)
        assert result is True  # Should be true because condition1 is true

    @pytest.mark.asyncio
    async def test_condition_parsing(self):
        """Test condition parsing from various formats"""
        from src.workflow_engine import WorkflowYAMLParser

        parser = WorkflowYAMLParser()

        # Test simple string condition
        condition = parser._parse_condition("test_variable")
        assert condition is not None
        assert condition.variable == "test_variable"
        assert condition.operator == ConditionOperator.EXISTS

        # Test dictionary condition
        condition_data = {
            "variable": "test_var",
            "operator": "eq",
            "value": "test_value",
            "description": "Test condition"
        }

        condition = parser._parse_condition(condition_data)
        assert condition is not None
        assert condition.variable == "test_var"
        assert condition.operator == ConditionOperator.EQUALS
        assert condition.value == "test_value"
        assert condition.description == "Test condition"

        # Test complex condition
        complex_condition_data = {
            "operator": "and",
            "conditions": [
                {"variable": "var1", "operator": "eq", "value": "value1"},
                {"variable": "var2", "operator": "ne", "value": "value2"}
            ]
        }

        condition = parser._parse_condition(complex_condition_data)
        assert condition is not None
        assert condition.operator == ConditionOperator.AND
        assert len(condition.sub_conditions) == 2


class TestParallelExecution:
    """Test parallel workflow execution"""

    @pytest.mark.asyncio
    async def test_resource_manager(self):
        """Test resource management for parallel execution"""
        from src.workflow_engine import ResourceManager, WorkflowStep, StepType

        manager = ResourceManager()

        # Test initial state
        assert manager.cpu_usage == 0.0
        assert manager.memory_usage == 0.0

        # Create test step
        step = WorkflowStep(
            id="test_step",
            name="Test Step",
            type=StepType.TASK,
            description="Test step for resource management"
        )

        # Test resource allocation
        can_allocate = manager.can_allocate_resources(step)
        assert can_allocate is True

        manager.allocate_resources(step)
        assert manager.cpu_usage > 0
        assert manager.memory_usage > 0
        assert "test_step" in manager.allocated_resources

        # Test resource freeing
        manager.free_resources("test_step")
        assert manager.cpu_usage == 0.0
        assert manager.memory_usage == 0.0
        assert "test_step" not in manager.allocated_resources

    @pytest.mark.asyncio
    async def test_dependency_resolution(self):
        """Test dependency resolution in parallel execution"""
        from src.workflow_engine import WorkflowExecutionEngine, WorkflowDefinition, WorkflowStep, StepType

        engine = WorkflowExecutionEngine()

        # Create workflow with dependencies
        step1 = WorkflowStep(
            id="step1",
            name="Step 1",
            type=StepType.TASK,
            description="First step"
        )

        step2 = WorkflowStep(
            id="step2",
            name="Step 2",
            type=StepType.TASK,
            description="Second step",
            depends_on=["step1"]
        )

        step3 = WorkflowStep(
            id="step3",
            name="Step 3",
            type=StepType.TASK,
            description="Third step",
            depends_on=["step1", "step2"]
        )

        workflow = WorkflowDefinition(
            id="test_workflow",
            name="Test Workflow",
            version="1.0.0",
            steps={"step1": step1, "step2": step2, "step3": step3},
            step_order=["step1", "step2", "step3"]
        )

        # Test dependency depth calculation
        depth1 = engine._calculate_dependency_depth(step1, workflow)
        depth2 = engine._calculate_dependency_depth(step2, workflow)
        depth3 = engine._calculate_dependency_depth(step3, workflow)

        assert depth1 == 0  # No dependencies
        assert depth2 == 1  # Depends on step1
        assert depth3 == 2  # Depends on step1 and step2


class TestWorkflowMonitoring:
    """Test workflow monitoring and visualization"""

    def test_workflow_monitor_initialization(self):
        """Test workflow monitor initialization"""
        from src.workflow_engine import WorkflowMonitor

        monitor = WorkflowMonitor()

        assert monitor.monitoring_enabled is True
        assert monitor.update_interval == 1.0
        assert len(monitor.active_monitors) == 0
        assert len(monitor.subscribers) == 0

    def test_execution_monitor(self):
        """Test individual execution monitor"""
        from src.workflow_engine import ExecutionMonitor

        monitor = ExecutionMonitor(
            execution_id="test_exec",
            workflow_id="test_workflow",
            workflow_name="Test Workflow",
            total_steps=5
        )

        # Test initial state
        assert monitor.execution_id == "test_exec"
        assert monitor.total_steps == 5
        assert monitor.completed_steps == 0
        assert monitor.is_active is True

        # Test status retrieval
        status = monitor.get_current_status()
        assert status["execution_id"] == "test_exec"
        assert status["status"]["total_steps"] == 5
        assert status["status"]["progress_percentage"] == 0.0

    def test_workflow_diagram_generation(self):
        """Test Mermaid diagram generation"""
        from src.workflow_engine import WorkflowExecutionEngine, WorkflowDefinition, WorkflowStep, StepType

        engine = WorkflowExecutionEngine()

        # Create simple workflow
        step1 = WorkflowStep(
            id="step1",
            name="First Step",
            type=StepType.TASK,
            description="First step"
        )

        step2 = WorkflowStep(
            id="step2",
            name="Second Step",
            type=StepType.TASK,
            description="Second step",
            depends_on=["step1"]
        )

        workflow = WorkflowDefinition(
            id="test_workflow",
            name="Test Workflow",
            version="1.0.0",
            steps={"step1": step1, "step2": step2},
            step_order=["step1", "step2"]
        )

        # Generate diagram
        diagram = engine.generate_workflow_diagram(workflow)

        assert "graph TD" in diagram
        assert "step1" in diagram
        assert "step2" in diagram
        assert "step1 --> step2" in diagram
        assert "classDef" in diagram  # Should include styling


class TestWorkflowIntegration:
    """Integration tests for complete workflow features"""

    @pytest.mark.asyncio
    async def test_end_to_end_workflow_execution(self, temp_dir):
        """Test complete workflow execution with all features"""
        from src.workflow_engine import WorkflowExecutionEngine, WorkflowDefinition, WorkflowStep, StepType, StepCondition, ConditionOperator

        engine = WorkflowExecutionEngine()

        # Create complex workflow with conditions and parallel steps
        analysis_step = WorkflowStep(
            id="analysis",
            name="Analysis",
            type=StepType.TASK,
            description="Analyze requirements"
        )

        design_step = WorkflowStep(
            id="design",
            name="Design",
            type=StepType.TASK,
            description="Create design",
            depends_on=["analysis"],
            condition=StepCondition(
                variable="analysis_complete",
                operator=ConditionOperator.EQUALS,
                value=True
            )
        )

        implementation_step = WorkflowStep(
            id="implementation",
            name="Implementation",
            type=StepType.TASK,
            description="Implement solution",
            depends_on=["design"]
        )

        workflow = WorkflowDefinition(
            id="complex_workflow",
            name="Complex Test Workflow",
            version="1.0.0",
            steps={
                "analysis": analysis_step,
                "design": design_step,
                "implementation": implementation_step
            },
            step_order=["analysis", "design", "implementation"],
            parallel_execution=True,
            max_concurrent_steps=2
        )

        # Mock agent executor
        mock_executor = Mock()
        mock_executor.execute = AsyncMock(return_value={
            "success": True,
            "outputs": ["test_output.md"],
            "summary": "Step completed successfully"
        })

        # Test workflow execution
        with patch('src.agent_executors.create_agent_executor', return_value=mock_executor):
            execution = await engine.start_workflow(
                workflow_id="complex_workflow",
                project_id="test_project",
                agent_team={},
                pheromone_bus={},
                context={}
            )

            assert execution is not None
            assert execution.workflow_id == "complex_workflow"
            assert hasattr(execution, 'monitor_id')

            # Check monitoring data
            monitoring_data = engine.get_execution_monitoring_data(execution.id)
            assert monitoring_data is not None
            assert monitoring_data["execution_id"] == execution.id
