name: TaoForge CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: aetherforge
          POSTGRES_DB: aetherforge_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: Set up environment variables
      run: |
        echo "POSTGRES_PASSWORD=test_password" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql://aetherforge:test_password@localhost:5432/aetherforge_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379" >> $GITHUB_ENV
        echo "OPENAI_API_KEY=test_key" >> $GITHUB_ENV
        echo "AETHERFORGE_ENV=test" >> $GITHUB_ENV

    - name: Run unit tests
      run: |
        python -m pytest tests/ -v --cov=src --cov-report=xml --cov-report=html
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Run integration tests
      run: |
        python -m pytest tests/test_integration_comprehensive.py -v
        
    - name: Run production readiness tests
      run: |
        python -m pytest tests/test_production_integration.py -v --tb=short

  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.12
        
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install black flake8 mypy isort bandit safety
        
    - name: Run Black formatter check
      run: black --check --diff src/ tests/
      
    - name: Run isort import sorting check
      run: isort --check-only --diff src/ tests/
      
    - name: Run Flake8 linting
      run: flake8 src/ tests/ --max-line-length=88 --extend-ignore=E203,W503
      
    - name: Run MyPy type checking
      run: mypy src/ --ignore-missing-imports
      
    - name: Run Bandit security check
      run: bandit -r src/ -f json -o bandit-report.json
      
    - name: Run Safety dependency check
      run: safety check --json --output safety-report.json

  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, lint]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.orchestrator
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: [docker-build]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, lint, docker-build]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up environment variables
      run: |
        echo "AETHERFORGE_ENV=staging" >> $GITHUB_ENV
        echo "DATABASE_URL=${{ secrets.STAGING_DATABASE_URL }}" >> $GITHUB_ENV
        echo "REDIS_URL=${{ secrets.STAGING_REDIS_URL }}" >> $GITHUB_ENV
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> $GITHUB_ENV

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Create staging environment file
        cat > .env.staging << EOF
        AETHERFORGE_ENV=staging
        DATABASE_URL=${{ secrets.STAGING_DATABASE_URL }}
        REDIS_URL=${{ secrets.STAGING_REDIS_URL }}
        OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
        POSTGRES_PASSWORD=${{ secrets.STAGING_POSTGRES_PASSWORD }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        EOF

        # Deploy using Docker Compose
        docker-compose -f docker-compose.yml --env-file .env.staging up -d

        # Wait for services to be ready
        sleep 30

        # Run health checks
        ./scripts/health_check.sh || exit 1

    - name: Run staging tests
      run: |
        # Run smoke tests against staging
        python -m pytest tests/test_end_to_end.py -v --timeout=300

    - name: Notify deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "SUCCESS: Staging deployment successful"
        else
          echo "ERROR: Staging deployment failed"
          exit 1
        fi
        
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, lint, docker-build, security-scan]
    if: github.event_name == 'release'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up production environment
      run: |
        echo "AETHERFORGE_ENV=production" >> $GITHUB_ENV

        # Create production environment file
        cat > .env.production << EOF
        AETHERFORGE_ENV=production
        DATABASE_URL=${{ secrets.PRODUCTION_DATABASE_URL }}
        REDIS_URL=${{ secrets.PRODUCTION_REDIS_URL }}
        OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
        POSTGRES_PASSWORD=${{ secrets.PRODUCTION_POSTGRES_PASSWORD }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        DEPLOYMENT_WEBHOOK_URL=${{ secrets.DEPLOYMENT_WEBHOOK_URL }}
        ALERT_WEBHOOK_URL=${{ secrets.ALERT_WEBHOOK_URL }}
        EOF

    - name: Pre-deployment backup
      run: |
        echo "Creating pre-deployment backup..."
        # Create backup before deployment
        ./scripts/backup.sh production || echo "Backup failed, continuing..."

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."

        # Make deployment script executable
        chmod +x scripts/deploy_production.sh

        # Run production deployment
        ROLLBACK_ENABLED=true ./scripts/deploy_production.sh

    - name: Run post-deployment health checks
      run: |
        echo "Running comprehensive post-deployment health checks..."

        # Wait for services to stabilize
        sleep 60

        # Run health checks
        ./scripts/health_check.sh || {
          echo "Health checks failed, initiating rollback..."
          ROLLBACK_ENABLED=true ./scripts/deploy_production.sh rollback
          exit 1
        }

        # Run production integration tests
        python -m pytest tests/test_production_integration.py -v --timeout=300 || {
          echo "Production tests failed, initiating rollback..."
          ROLLBACK_ENABLED=true ./scripts/deploy_production.sh rollback
          exit 1
        }

    - name: Update deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "SUCCESS: Production deployment successful"
          # Send success notification
          curl -X POST "${{ secrets.DEPLOYMENT_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{"text":"SUCCESS: TaoForge production deployment successful! Version: ${{ github.ref_name }}"}' || true
        else
          echo "ERROR: Production deployment failed"
          # Send failure notification
          curl -X POST "${{ secrets.ALERT_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{"text":"ALERT: TaoForge production deployment FAILED! Version: ${{ github.ref_name }}"}' || true
          exit 1
        fi

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
