# TaoForge Environment Configuration
# Copy this file to .env and configure with your actual values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production, test)
AETHERFORGE_ENV=development

# Debug mode (true/false)
DEBUG=true

# Log level (debug, info, warning, error)
LOG_LEVEL=info

# =============================================================================
# AI SERVICES
# =============================================================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=aetherforge
POSTGRES_USER=aetherforge
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Database URL (constructed from above or override)
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Redis URL (constructed from above or override)
REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret for token signing (generate with: openssl rand -base64 64)
JWT_SECRET=your_jwt_secret_key_here

# Session secret for secure session management
SESSION_SECRET=your_session_secret_here

# Authentication settings
AUTH_TOKEN_EXPIRY=3600
AUTH_REFRESH_TOKEN_EXPIRY=86400
AUTH_MAX_LOGIN_ATTEMPTS=5
AUTH_LOCKOUT_DURATION=900

# CORS origins (comma-separated) for cross-origin security
CORS_ORIGINS=http://localhost:3000,http://localhost:8501

# Security headers and HTTPS enforcement
FORCE_HTTPS=false
SECURE_COOKIES=true
CSRF_PROTECTION=true

# API security settings
API_RATE_LIMIT=100
API_RATE_WINDOW=3600
API_KEY_REQUIRED=false

# =============================================================================
# APPLICATION PORTS
# =============================================================================

# Main orchestrator port
ORCHESTRATOR_PORT=8000

# Component service ports
ARCHON_PORT=8100
ARCHON_UI_PORT=8501
PHEROMIND_PORT=8502
BMAD_PORT=8503

# =============================================================================
# FILE SYSTEM CONFIGURATION
# =============================================================================

# Project storage directory
PROJECTS_DIR=./projects

# Logs directory
LOGS_DIR=./logs

# Backup directory
BACKUP_DIR=./backups

# Pheromone storage file
PHEROMONE_FILE=./pheromones.json

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Prometheus metrics
PROMETHEUS_PORT=9090
PROMETHEUS_ENABLED=true

# Grafana dashboard
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here

# Health check settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment webhook for notifications (Slack/Teams/Discord)
DEPLOYMENT_WEBHOOK_URL=your_deployment_webhook_url_here

# Alert webhook for critical notifications
ALERT_WEBHOOK_URL=your_alert_webhook_url_here

# Backup retention (days)
BACKUP_RETENTION_DAYS=30

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Maximum concurrent projects
MAX_CONCURRENT_PROJECTS=5

# Project timeout (minutes)
PROJECT_TIMEOUT_MINUTES=30

# Worker pool size
WORKER_POOL_SIZE=4

# Request timeout (seconds)
REQUEST_TIMEOUT=300

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Hot reload for development
HOT_RELOAD=true

# API documentation
API_DOCS_ENABLED=true

# Test database (for testing environment)
TEST_DATABASE_URL=postgresql://aetherforge:test_password@localhost:5432/aetherforge_test

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# GitHub integration (optional)
GITHUB_TOKEN=your_github_token_here
GITHUB_WEBHOOK_SECRET=your_github_webhook_secret_here

# Docker registry (for production deployments)
DOCKER_REGISTRY=ghcr.io
DOCKER_REGISTRY_USERNAME=your_username_here
DOCKER_REGISTRY_TOKEN=your_registry_token_here

# =============================================================================
# SSL/TLS CONFIGURATION (Production)
# =============================================================================

# SSL certificate paths (for production)
SSL_CERT_PATH=./nginx/ssl/cert.pem
SSL_KEY_PATH=./nginx/ssl/key.pem

# Force HTTPS redirect
FORCE_HTTPS=false

# =============================================================================
# RATE LIMITING
# =============================================================================

# API rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
FEATURE_ANALYTICS=true
FEATURE_TELEMETRY=false
FEATURE_EXPERIMENTAL=false

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all "your_*_here" values with actual configuration
# 2. Generate secure passwords for production environments
# 3. Keep this file secure and never commit actual secrets to version control
# 4. Use environment-specific .env files for different deployments
# 5. Validate configuration with: python scripts/validate_environment.py
