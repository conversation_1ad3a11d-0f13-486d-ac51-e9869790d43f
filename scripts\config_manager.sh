#!/bin/bash

# Aetherforge Configuration Management Script
# Manages environment-specific configurations and secrets

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"
SECRETS_DIR="$PROJECT_ROOT/secrets"
TEMPLATES_DIR="$PROJECT_ROOT/config/templates"

# Logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")
            echo -e "${RED}❌ [${timestamp}] ${message}${NC}" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ [${timestamp}] ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ [${timestamp}] ${message}${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ [${timestamp}] ${message}${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Show usage
show_usage() {
    cat << EOF
Aetherforge Configuration Manager

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    init [ENV]              Initialize configuration for environment
    validate [ENV]          Validate configuration for environment
    generate [ENV]          Generate configuration files from templates
    backup [ENV]            Backup configuration
    restore [ENV] [BACKUP]  Restore configuration from backup
    encrypt [FILE]          Encrypt sensitive configuration file
    decrypt [FILE]          Decrypt sensitive configuration file
    list                    List available configurations
    diff [ENV1] [ENV2]      Compare configurations between environments

Environments:
    development, staging, production

Examples:
    $0 init production
    $0 validate staging
    $0 generate development
    $0 backup production
    $0 encrypt secrets/production.env

EOF
}

# Initialize configuration for environment
init_config() {
    local env=${1:-development}
    
    log "INFO" "Initializing configuration for $env environment..."
    
    # Create directory structure
    mkdir -p "$CONFIG_DIR/$env"
    mkdir -p "$SECRETS_DIR/$env"
    mkdir -p "$CONFIG_DIR/templates"
    mkdir -p "$CONFIG_DIR/backups"
    
    # Create base configuration files
    create_base_config "$env"
    create_docker_config "$env"
    create_nginx_config "$env"
    create_monitoring_config "$env"
    create_secrets_template "$env"
    
    log "SUCCESS" "Configuration initialized for $env environment"
}

# Create base configuration
create_base_config() {
    local env=$1
    local config_file="$CONFIG_DIR/$env/aetherforge.yml"
    
    if [[ ! -f "$config_file" ]]; then
        cat > "$config_file" << EOF
# Aetherforge Configuration - $env Environment
environment: $env
version: "1.0.0"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  debug: $([ "$env" = "development" ] && echo "true" || echo "false")
  cors_origins: $([ "$env" = "production" ] && echo '["https://yourdomain.com"]' || echo '["*"]')

# Database Configuration
database:
  host: postgres
  port: 5432
  name: aetherforge
  user: aetherforge
  pool_size: $([ "$env" = "production" ] && echo "20" || echo "5")
  max_overflow: $([ "$env" = "production" ] && echo "30" || echo "10")

# Redis Configuration
redis:
  host: redis
  port: 6379
  db: 0
  max_connections: $([ "$env" = "production" ] && echo "100" || echo "20")

# Service URLs
services:
  archon_url: "http://archon:8100"
  mcp_url: "http://mcp-crawl4ai:8051"
  pheromind_url: "http://pheromind:8502"
  bmad_url: "http://bmad:8503"

# Performance Settings
performance:
  max_concurrent_projects: $([ "$env" = "production" ] && echo "20" || echo "5")
  project_timeout_minutes: $([ "$env" = "production" ] && echo "60" || echo "30")
  worker_processes: $([ "$env" = "production" ] && echo "4" || echo "2")

# Logging Configuration
logging:
  level: $([ "$env" = "development" ] && echo "debug" || echo "info")
  format: "json"
  file: "/var/log/aetherforge/app.log"
  max_size: "100MB"
  backup_count: 10

# Monitoring
monitoring:
  enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
  prometheus_port: 9090
  grafana_port: 3001
  metrics_interval: 30

# Security
security:
  jwt_secret_key: "CHANGE_ME_IN_PRODUCTION"
  session_timeout: 3600
  rate_limiting:
    enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
    requests_per_minute: 100
  
# Feature Flags
features:
  enable_mcp_research: true
  enable_pheromind_coordination: true
  enable_archon_generation: true
  enable_bmad_methodology: true
  enable_advanced_analytics: $([ "$env" = "production" ] && echo "true" || echo "false")
EOF
        log "SUCCESS" "Created base configuration: $config_file"
    fi
}

# Create Docker configuration
create_docker_config() {
    local env=$1
    local docker_env_file="$CONFIG_DIR/$env/docker.env"
    
    if [[ ! -f "$docker_env_file" ]]; then
        cat > "$docker_env_file" << EOF
# Docker Environment Configuration - $env
COMPOSE_PROJECT_NAME=aetherforge_$env
COMPOSE_FILE=docker-compose$([ "$env" != "development" ] && echo ".$env").yml

# Resource Limits
ORCHESTRATOR_MEMORY_LIMIT=$([ "$env" = "production" ] && echo "2g" || echo "1g")
ORCHESTRATOR_CPU_LIMIT=$([ "$env" = "production" ] && echo "2" || echo "1")

ARCHON_MEMORY_LIMIT=$([ "$env" = "production" ] && echo "1g" || echo "512m")
ARCHON_CPU_LIMIT=$([ "$env" = "production" ] && echo "1" || echo "0.5")

POSTGRES_MEMORY_LIMIT=$([ "$env" = "production" ] && echo "2g" || echo "512m")
POSTGRES_CPU_LIMIT=$([ "$env" = "production" ] && echo "2" || echo "1")

REDIS_MEMORY_LIMIT=$([ "$env" = "production" ] && echo "512m" || echo "256m")
REDIS_CPU_LIMIT=$([ "$env" = "production" ] && echo "1" || echo "0.5")

# Replica Configuration
ORCHESTRATOR_REPLICAS=$([ "$env" = "production" ] && echo "2" || echo "1")
ARCHON_REPLICAS=$([ "$env" = "production" ] && echo "2" || echo "1")

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=40s

# Restart Policy
RESTART_POLICY=unless-stopped

# Network Configuration
NETWORK_SUBNET=172.20.0.0/16
EOF
        log "SUCCESS" "Created Docker configuration: $docker_env_file"
    fi
}

# Create Nginx configuration
create_nginx_config() {
    local env=$1
    local nginx_dir="$CONFIG_DIR/$env/nginx"
    
    mkdir -p "$nginx_dir"
    
    if [[ ! -f "$nginx_dir/nginx.conf" ]]; then
        cat > "$nginx_dir/nginx.conf" << EOF
# Nginx Configuration - $env Environment

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    $([ "$env" = "production" ] && echo 'add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;')

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream servers
    upstream orchestrator {
        least_conn;
        server orchestrator:8000 max_fails=3 fail_timeout=30s;
        $([ "$env" = "production" ] && echo 'server orchestrator_2:8000 max_fails=3 fail_timeout=30s;')
    }

    upstream archon {
        least_conn;
        server archon:8100 max_fails=3 fail_timeout=30s;
        $([ "$env" = "production" ] && echo 'server archon_2:8100 max_fails=3 fail_timeout=30s;')
    }

    # Main server block
    server {
        listen 80;
        $([ "$env" = "production" ] && echo 'listen 443 ssl http2;')
        server_name $([ "$env" = "production" ] && echo 'yourdomain.com' || echo 'localhost');

        $([ "$env" = "production" ] && cat << 'SSLEOF'
        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
SSLEOF
)

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://orchestrator;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://orchestrator/health;
            access_log off;
        }

        # Archon UI
        location /archon/ {
            proxy_pass http://archon/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # Static files
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Default location
        location / {
            proxy_pass http://orchestrator;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }

    $([ "$env" = "production" ] && cat << 'REDIRECTEOF'
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }
REDIRECTEOF
)
}
EOF
        log "SUCCESS" "Created Nginx configuration: $nginx_dir/nginx.conf"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    local env=$1
    local monitoring_dir="$CONFIG_DIR/$env/monitoring"
    
    mkdir -p "$monitoring_dir"
    
    # Prometheus configuration
    if [[ ! -f "$monitoring_dir/prometheus.yml" ]]; then
        cat > "$monitoring_dir/prometheus.yml" << EOF
# Prometheus Configuration - $env Environment
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'aetherforge-orchestrator'
    static_configs:
      - targets: ['orchestrator:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'aetherforge-archon'
    static_configs:
      - targets: ['archon:8100']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
EOF
        log "SUCCESS" "Created Prometheus configuration: $monitoring_dir/prometheus.yml"
    fi
}

# Create secrets template
create_secrets_template() {
    local env=$1
    local secrets_file="$SECRETS_DIR/$env/secrets.env.template"
    
    if [[ ! -f "$secrets_file" ]]; then
        cat > "$secrets_file" << EOF
# Aetherforge Secrets - $env Environment
# Copy this file to secrets.env and fill in the actual values

# Database
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_USER=aetherforge
POSTGRES_DB=aetherforge

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
SESSION_SECRET=your_session_secret_here

# External Services
GITHUB_TOKEN=your_github_token_here
SLACK_WEBHOOK_URL=your_slack_webhook_url_here

# Monitoring
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here

# SSL (for production)
$([ "$env" = "production" ] && cat << 'SSLEOF'
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSLEOF
)
EOF
        log "SUCCESS" "Created secrets template: $secrets_file"
    fi
}

# Validate configuration
validate_config() {
    local env=${1:-development}
    
    log "INFO" "Validating configuration for $env environment..."
    
    local config_file="$CONFIG_DIR/$env/aetherforge.yml"
    local secrets_file="$SECRETS_DIR/$env/secrets.env"
    
    # Check if configuration files exist
    if [[ ! -f "$config_file" ]]; then
        error_exit "Configuration file not found: $config_file"
    fi
    
    if [[ ! -f "$secrets_file" ]]; then
        log "WARNING" "Secrets file not found: $secrets_file"
        log "INFO" "Use the template at $SECRETS_DIR/$env/secrets.env.template"
    fi
    
    # Validate YAML syntax
    if command -v python3 &> /dev/null; then
        python3 -c "import yaml; yaml.safe_load(open('$config_file'))" || error_exit "Invalid YAML syntax in $config_file"
    fi
    
    # Check required secrets
    if [[ -f "$secrets_file" ]]; then
        local required_secrets=("POSTGRES_PASSWORD" "OPENAI_API_KEY")
        for secret in "${required_secrets[@]}"; do
            if ! grep -q "^$secret=" "$secrets_file"; then
                log "WARNING" "Required secret $secret not found in $secrets_file"
            fi
        done
    fi
    
    log "SUCCESS" "Configuration validation completed for $env environment"
}

# Generate configuration files
generate_config() {
    local env=${1:-development}
    
    log "INFO" "Generating configuration files for $env environment..."
    
    # Generate Docker Compose override
    local compose_override="$PROJECT_ROOT/docker-compose.$env.override.yml"
    
    cat > "$compose_override" << EOF
# Generated Docker Compose override for $env environment
version: '3.8'

services:
  orchestrator:
    environment:
      - AETHERFORGE_ENV=$env
    env_file:
      - config/$env/docker.env
      - secrets/$env/secrets.env
    
  nginx:
    volumes:
      - ./config/$env/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      $([ "$env" = "production" ] && echo '- ./secrets/$env/ssl:/etc/nginx/ssl:ro')
EOF
    
    log "SUCCESS" "Generated Docker Compose override: $compose_override"
}

# Main function
main() {
    local command=${1:-}
    
    case $command in
        "init")
            init_config "${2:-development}"
            ;;
        "validate")
            validate_config "${2:-development}"
            ;;
        "generate")
            generate_config "${2:-development}"
            ;;
        "list")
            log "INFO" "Available configurations:"
            ls -la "$CONFIG_DIR" 2>/dev/null || log "WARNING" "No configurations found"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log "ERROR" "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
