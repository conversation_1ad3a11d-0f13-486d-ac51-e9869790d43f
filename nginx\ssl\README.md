# SSL/TLS Configuration for TaoForge

## Overview

This directory contains SSL/TLS certificates and configuration for secure HTTPS deployment of TaoForge.

## Production SSL Setup

### 1. Obtain SSL Certificates

For production deployment, you need valid SSL certificates. You can obtain them from:

- **Let's Encrypt** (Free, automated)
- **Commercial CA** (Paid, extended validation)
- **Self-signed** (Development/testing only)

### 2. Certificate Files

Place your SSL certificates in this directory:

```
nginx/ssl/
├── cert.pem          # SSL certificate
├── key.pem           # Private key
├── chain.pem         # Certificate chain (if applicable)
└── dhparam.pem       # Diffie-Hellman parameters
```

### 3. Let's Encrypt Setup

For Let's Encrypt certificates:

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 4. Self-Signed Certificates (Development)

For development/testing environments:

```bash
# Generate private key
openssl genrsa -out nginx/ssl/key.pem 2048

# Generate certificate
openssl req -new -x509 -key nginx/ssl/key.pem -out nginx/ssl/cert.pem -days 365 \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Generate DH parameters
openssl dhparam -out nginx/ssl/dhparam.pem 2048
```

## Security Configuration

### 1. Nginx SSL Configuration

The main nginx.conf includes SSL security headers:

- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security` (HSTS)

### 2. TLS Settings

Recommended TLS configuration:

```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

### 3. Security Best Practices

1. **Keep certificates updated**
2. **Use strong cipher suites**
3. **Enable HSTS**
4. **Disable weak protocols**
5. **Regular security audits**

## File Permissions

Ensure proper file permissions for security:

```bash
# Certificate files should be readable by nginx
chmod 644 nginx/ssl/cert.pem
chmod 644 nginx/ssl/chain.pem

# Private key should be secure
chmod 600 nginx/ssl/key.pem
chown root:root nginx/ssl/key.pem
```

## Testing SSL Configuration

Test your SSL configuration:

```bash
# Test SSL certificate
openssl x509 -in nginx/ssl/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect your-domain.com:443

# Online SSL test
# Visit: https://www.ssllabs.com/ssltest/
```

## Troubleshooting

### Common Issues

1. **Certificate not found**
   - Check file paths in nginx.conf
   - Verify file permissions

2. **Certificate expired**
   - Renew certificate
   - Restart nginx

3. **Mixed content warnings**
   - Ensure all resources use HTTPS
   - Update application URLs

### Log Files

Check nginx error logs:

```bash
tail -f /var/log/nginx/error.log
```

## Security Monitoring

Monitor SSL certificate expiration:

```bash
# Check certificate expiration
echo | openssl s_client -servername your-domain.com -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

## References

- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [OWASP TLS Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Transport_Layer_Protection_Cheat_Sheet.html)
