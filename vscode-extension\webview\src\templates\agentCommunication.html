<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline' vscode-resource:; script-src 'unsafe-inline' vscode-resource:; img-src vscode-resource: data:;">
    <title>Agent Communication</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            height: 100vh;
            overflow: hidden;
        }
        
        #root {
            height: 100vh;
            width: 100vw;
        }
        
        /* VS Code theme variables */
        :root {
            --vscode-button-background: #0e639c;
            --vscode-button-foreground: #ffffff;
            --vscode-button-hoverBackground: #1177bb;
            --vscode-input-background: #3c3c3c;
            --vscode-input-foreground: #cccccc;
            --vscode-input-border: #3c3c3c;
            --vscode-focusBorder: #007acc;
            --vscode-list-hoverBackground: #2a2d2e;
            --vscode-list-activeSelectionBackground: #094771;
            --vscode-list-inactiveSelectionBackground: #37373d;
            --vscode-badge-background: #4d4d4d;
            --vscode-badge-foreground: #ffffff;
            --vscode-progressBar-background: #0e70c0;
            --vscode-panel-background: #1e1e1e;
            --vscode-panel-border: #2d2d30;
            --vscode-sideBar-background: #252526;
            --vscode-sideBar-foreground: #cccccc;
            --vscode-sideBar-border: #2d2d30;
            --vscode-activityBar-background: #333333;
            --vscode-activityBar-foreground: #ffffff;
            --vscode-statusBar-background: #007acc;
            --vscode-statusBar-foreground: #ffffff;
            --vscode-titleBar-activeBackground: #3c3c3c;
            --vscode-titleBar-activeForeground: #cccccc;
            --vscode-menu-background: #3c3c3c;
            --vscode-menu-foreground: #cccccc;
            --vscode-dropdown-background: #3c3c3c;
            --vscode-dropdown-foreground: #cccccc;
            --vscode-dropdown-border: #3c3c3c;
            --vscode-checkbox-background: #3c3c3c;
            --vscode-checkbox-foreground: #f0f0f0;
            --vscode-checkbox-border: #3c3c3c;
        }
        
        /* Dark theme adjustments */
        .vscode-dark {
            --vscode-editor-background: #1e1e1e;
            --vscode-editor-foreground: #d4d4d4;
            --vscode-sideBar-background: #252526;
            --vscode-panel-background: #1e1e1e;
        }
        
        /* Light theme adjustments */
        .vscode-light {
            --vscode-editor-background: #ffffff;
            --vscode-editor-foreground: #333333;
            --vscode-sideBar-background: #f3f3f3;
            --vscode-panel-background: #ffffff;
            --vscode-input-background: #ffffff;
            --vscode-input-foreground: #333333;
            --vscode-button-background: #0078d4;
            --vscode-button-hoverBackground: #106ebe;
        }
        
        /* High contrast theme adjustments */
        .vscode-high-contrast {
            --vscode-editor-background: #000000;
            --vscode-editor-foreground: #ffffff;
            --vscode-sideBar-background: #000000;
            --vscode-panel-background: #000000;
            --vscode-input-background: #000000;
            --vscode-input-foreground: #ffffff;
            --vscode-button-background: #0078d4;
            --vscode-focusBorder: #f38518;
        }
        
        /* Loading animation */
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-input-border);
            border-top: 4px solid var(--vscode-progressBar-background);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .loading-subtext {
            font-size: 14px;
            opacity: 0.7;
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--vscode-scrollbarSlider-background);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }
        
        /* Custom slider styling for priority */
        .slider {
            -webkit-appearance: none;
            appearance: none;
            background: var(--vscode-input-background);
            outline: none;
            border-radius: 5px;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--vscode-button-background);
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--vscode-button-background);
            cursor: pointer;
            border: none;
        }
        
        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        /* Focus styles */
        button:focus,
        input:focus,
        textarea:focus,
        select:focus {
            outline: 1px solid var(--vscode-focusBorder);
            outline-offset: 2px;
        }
        
        /* Disabled state */
        button:disabled,
        input:disabled,
        textarea:disabled,
        select:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* Selection styles */
        ::selection {
            background-color: var(--vscode-editor-selectionBackground);
            color: var(--vscode-editor-selectionForeground);
        }
        
        /* Error state */
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border-color: var(--vscode-inputValidation-errorBorder);
        }
        
        /* Success state */
        .success {
            color: var(--vscode-terminal-ansiGreen);
        }
        
        /* Warning state */
        .warning {
            color: var(--vscode-terminal-ansiYellow);
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Agent Communication Panel</div>
            <div class="loading-subtext">Connecting to agents...</div>
        </div>
    </div>
</body>
</html>
