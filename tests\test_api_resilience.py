"""
Comprehensive tests for API resilience layer
"""

import pytest
import asyncio
import json
import tempfile
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

import sys
sys.path.append(str(Path(__file__).parent.parent / "src"))

from api_resilience import (
    APIResilienceLayer, QuotaManager, NotificationManager,
    RetryConfig, FallbackConfig, APICallResult, APIProvider
)
from api_manager import APIManager, APIError, QuotaExceededError, RateLimitError

class TestQuotaManager:
    """Test quota management functionality"""
    
    def test_quota_manager_initialization(self):
        """Test quota manager initializes correctly"""
        with tempfile.TemporaryDirectory() as temp_dir:
            quota_manager = QuotaManager(str(Path(temp_dir) / "quota.json"))
            assert quota_manager.usage_data == {}
    
    def test_record_usage(self):
        """Test recording API usage"""
        with tempfile.TemporaryDirectory() as temp_dir:
            quota_manager = QuotaManager(str(Path(temp_dir) / "quota.json"))
            
            quota_manager.record_usage(APIProvider.OPENAI, tokens=100, cost=0.01)
            
            stats = quota_manager.get_usage_stats(APIProvider.OPENAI)
            assert stats["requests"] == 1
            assert stats["tokens"] == 100
            assert stats["cost"] == 0.01
    
    def test_quota_warnings(self):
        """Test quota warning generation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            quota_manager = QuotaManager(str(Path(temp_dir) / "quota.json"))
            
            # Set up usage data with limits
            from api_resilience import QuotaUsage
            quota_manager.usage_data["openai"] = quota_manager.usage_data.get("openai",
                QuotaUsage(provider=APIProvider.OPENAI))
            quota_manager.usage_data["openai"].daily_limit = 100
            quota_manager.usage_data["openai"].requests_made = 85  # 85% of limit
            
            stats = quota_manager.get_usage_stats(APIProvider.OPENAI)
            assert len(stats["warnings"]) > 0
            assert "daily request limit" in stats["warnings"][0]

class TestNotificationManager:
    """Test notification management"""
    
    def test_add_notification(self):
        """Test adding notifications"""
        manager = NotificationManager()
        
        manager.add_notification("test", "Test message", "info", APIProvider.OPENAI)
        
        notifications = manager.get_recent_notifications(10)
        assert len(notifications) == 1
        assert notifications[0]["type"] == "test"
        assert notifications[0]["message"] == "Test message"
        assert notifications[0]["severity"] == "info"
        assert notifications[0]["provider"] == "openai"
    
    def test_notification_limit(self):
        """Test notification limit enforcement"""
        manager = NotificationManager()
        manager.max_notifications = 5
        
        # Add more notifications than the limit
        for i in range(10):
            manager.add_notification("test", f"Message {i}", "info")
        
        assert len(manager.notifications) == 5
        # Should keep the most recent ones
        assert manager.notifications[-1]["message"] == "Message 9"
    
    def test_get_notifications_by_type(self):
        """Test filtering notifications by type"""
        manager = NotificationManager()
        
        manager.add_notification("type1", "Message 1", "info")
        manager.add_notification("type2", "Message 2", "warning")
        manager.add_notification("type1", "Message 3", "error")
        
        type1_notifications = manager.get_notifications_by_type("type1")
        assert len(type1_notifications) == 2
        assert all(n["type"] == "type1" for n in type1_notifications)

class TestAPIResilienceLayer:
    """Test the main resilience layer"""
    
    @pytest.fixture
    def mock_api_manager(self):
        """Create a mock API manager"""
        manager = Mock(spec=APIManager)
        manager.providers = {APIProvider.OPENAI: Mock()}
        manager.providers[APIProvider.OPENAI].model = "gpt-4"
        manager.get_available_providers.return_value = [APIProvider.OPENAI]
        manager.get_provider_status.return_value = {
            "openai": {
                "configured": True,
                "model": "gpt-4",
                "rate_limit_remaining": 50,
                "can_make_request": True
            }
        }
        return manager
    
    @pytest.fixture
    def resilience_layer(self, mock_api_manager):
        """Create a resilience layer with mocked dependencies"""
        return APIResilienceLayer(api_manager=mock_api_manager)
    
    @pytest.mark.asyncio
    async def test_successful_api_call(self, resilience_layer, mock_api_manager):
        """Test successful API call without fallbacks"""
        mock_api_manager.generate_text = AsyncMock(return_value="Test response")
        
        messages = [{"role": "user", "content": "Test message"}]
        result = await resilience_layer.generate_text_resilient(messages)
        
        assert result.success
        assert result.response == "Test response"
        assert result.provider_used == APIProvider.OPENAI
        assert not result.fallback_used
        assert result.retries_attempted == 0
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, resilience_layer, mock_api_manager):
        """Test response caching"""
        mock_api_manager.generate_text = AsyncMock(return_value="Cached response")
        
        messages = [{"role": "user", "content": "Cache test"}]
        
        # First call should hit the API
        result1 = await resilience_layer.generate_text_resilient(messages)
        assert result1.success
        assert not result1.fallback_used
        
        # Second call should use cache
        result2 = await resilience_layer.generate_text_resilient(messages)
        assert result2.success
        assert result2.fallback_used  # Cache is considered a fallback
        assert result2.provider_used is None  # No provider used for cache
    
    @pytest.mark.asyncio
    async def test_retry_on_failure(self, resilience_layer, mock_api_manager):
        """Test retry logic on API failures"""
        # Mock API to fail twice then succeed
        mock_api_manager.generate_text = AsyncMock(
            side_effect=[
                Exception("First failure"),
                Exception("Second failure"), 
                "Success after retries"
            ]
        )
        
        messages = [{"role": "user", "content": "Retry test"}]
        result = await resilience_layer.generate_text_resilient(messages)
        
        assert result.success
        assert result.response == "Success after retries"
        assert result.retries_attempted > 0
    
    @pytest.mark.asyncio
    async def test_quota_exceeded_handling(self, resilience_layer, mock_api_manager):
        """Test handling of quota exceeded errors"""
        mock_api_manager.generate_text = AsyncMock(
            side_effect=QuotaExceededError(APIProvider.OPENAI)
        )
        
        messages = [{"role": "user", "content": "Quota test"}]
        
        # Should try degraded service when quota exceeded
        result = await resilience_layer.generate_text_resilient(messages)
        
        # Should fall back to degraded service
        assert result.success
        assert result.fallback_used
        assert "technical difficulties" in result.response.lower()
        
        # Should have notification about quota exceeded
        notifications = resilience_layer.get_user_notifications()
        quota_notifications = [n for n in notifications if n["type"] == "quota_exceeded"]
        assert len(quota_notifications) > 0
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, resilience_layer, mock_api_manager):
        """Test handling of rate limit errors"""
        # Configure to not retry on rate limits for faster testing
        resilience_layer.retry_config.retry_on_rate_limit = False
        
        mock_api_manager.generate_text = AsyncMock(
            side_effect=RateLimitError(APIProvider.OPENAI, 60)
        )
        
        messages = [{"role": "user", "content": "Rate limit test"}]
        result = await resilience_layer.generate_text_resilient(messages)
        
        # Should fall back to degraded service
        assert result.success
        assert result.fallback_used
        
        # Should have notification about rate limit
        notifications = resilience_layer.get_user_notifications()
        rate_notifications = [n for n in notifications if n["type"] == "rate_limit"]
        assert len(rate_notifications) > 0
    
    @pytest.mark.asyncio
    async def test_degraded_service_fallback(self, resilience_layer, mock_api_manager):
        """Test degraded service when all providers fail"""
        mock_api_manager.generate_text = AsyncMock(
            side_effect=Exception("All providers failed")
        )
        
        messages = [{"role": "user", "content": "Degraded service test"}]
        result = await resilience_layer.generate_text_resilient(messages)
        
        assert result.success
        assert result.fallback_used
        assert "technical difficulties" in result.response
        assert "Degraded service test" in result.response  # Should include user's request
        
        # Should have notification about degraded service
        notifications = resilience_layer.get_user_notifications()
        degraded_notifications = [n for n in notifications if n["type"] == "degraded_service"]
        assert len(degraded_notifications) > 0
    
    def test_get_resilience_stats(self, resilience_layer):
        """Test resilience statistics generation"""
        stats = resilience_layer.get_resilience_stats()
        
        assert "quota_usage" in stats
        assert "cache_stats" in stats
        assert "provider_status" in stats
        assert "fallback_config" in stats
        assert "recent_notifications" in stats
        assert "notification_summary" in stats
        
        # Check fallback config structure
        config = stats["fallback_config"]
        assert "provider_fallback" in config
        assert "model_fallback" in config
        assert "degraded_service" in config
        assert "cache_fallback" in config
    
    @pytest.mark.asyncio
    async def test_provider_testing(self, resilience_layer, mock_api_manager):
        """Test provider health testing"""
        mock_api_manager.generate_text = AsyncMock(return_value="Health check response")
        
        results = await resilience_layer.test_all_providers()
        
        assert "openai" in results
        assert results["openai"]["status"] == "healthy"
        assert "response_time" in results["openai"]
        assert "model" in results["openai"]

class TestRetryConfig:
    """Test retry configuration"""
    
    def test_default_retry_config(self):
        """Test default retry configuration"""
        config = RetryConfig()
        
        assert config.max_retries == 3
        assert config.base_delay == 1.0
        assert config.max_delay == 60.0
        assert config.exponential_base == 2.0
        assert config.jitter is True
        assert config.retry_on_quota is False
        assert config.retry_on_rate_limit is True
    
    def test_custom_retry_config(self):
        """Test custom retry configuration"""
        config = RetryConfig(
            max_retries=5,
            base_delay=2.0,
            retry_on_quota=True
        )
        
        assert config.max_retries == 5
        assert config.base_delay == 2.0
        assert config.retry_on_quota is True

class TestFallbackConfig:
    """Test fallback configuration"""
    
    def test_default_fallback_config(self):
        """Test default fallback configuration"""
        config = FallbackConfig()
        
        assert config.enable_provider_fallback is True
        assert config.enable_model_fallback is True
        assert config.enable_degraded_service is True
        assert config.enable_cache_fallback is True
        assert APIProvider.OPENAI in config.fallback_providers
        assert APIProvider.ANTHROPIC in config.fallback_providers
    
    def test_fallback_models(self):
        """Test fallback model configuration"""
        config = FallbackConfig()
        
        assert "gpt-4" in config.fallback_models[APIProvider.OPENAI]
        assert "gpt-3.5-turbo" in config.fallback_models[APIProvider.OPENAI]
        assert "claude-3-sonnet-20240229" in config.fallback_models[APIProvider.ANTHROPIC]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
