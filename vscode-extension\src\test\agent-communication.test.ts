import * as assert from 'assert';
import * as vscode from 'vscode';
import { AgentCommunicationPanel, Agent, AgentType, AgentStatus, Message, MessageType, FeedbackRequest } from '../agent-communication-panel';
import { WorkspaceManager } from '../workspace-manager';

suite('Agent Communication Tests', () => {
  let agentCommunicationPanel: AgentCommunicationPanel;
  let workspaceManager: WorkspaceManager;
  let testContext: vscode.ExtensionContext;

  suiteSetup(() => {
    // Create a mock extension context
    testContext = {
      subscriptions: [],
      workspaceState: {
        get: () => undefined,
        update: () => Promise.resolve()
      },
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        keys: () => []
      },
      extensionPath: __dirname,
      asAbsolutePath: (relativePath: string) => relativePath,
      storagePath: __dirname,
      globalStoragePath: __dirname,
      logPath: __dirname,
      extensionUri: vscode.Uri.file(__dirname),
      environmentVariableCollection: {} as any,
      extensionMode: vscode.ExtensionMode.Test,
      globalStorageUri: vscode.Uri.file(__dirname),
      logUri: vscode.Uri.file(__dirname),
      storageUri: vscode.Uri.file(__dirname),
      secrets: {} as any,
      extension: {} as any
    };

    workspaceManager = new WorkspaceManager(testContext);
    agentCommunicationPanel = new AgentCommunicationPanel(testContext, workspaceManager);
  });

  suiteTeardown(() => {
    agentCommunicationPanel.dispose();
    workspaceManager.dispose();
  });

  test('Initialize Default Agents', () => {
    const agents = agentCommunicationPanel.getAgents();
    
    assert.ok(agents.length >= 5, 'Should have at least 5 default agents');
    
    // Check for specific agent types
    const agentTypes = agents.map(agent => agent.type);
    assert.ok(agentTypes.includes(AgentType.ANALYST), 'Should have analyst agent');
    assert.ok(agentTypes.includes(AgentType.ARCHITECT), 'Should have architect agent');
    assert.ok(agentTypes.includes(AgentType.DEVELOPER), 'Should have developer agent');
    assert.ok(agentTypes.includes(AgentType.TESTER), 'Should have tester agent');
    assert.ok(agentTypes.includes(AgentType.REVIEWER), 'Should have reviewer agent');
  });

  test('Agent Properties', () => {
    const agents = agentCommunicationPanel.getAgents();
    const analystAgent = agents.find(agent => agent.type === AgentType.ANALYST);
    
    assert.ok(analystAgent, 'Analyst agent should exist');
    assert.strictEqual(analystAgent.name, 'Alex Analyst');
    assert.strictEqual(analystAgent.status, AgentStatus.IDLE);
    assert.ok(analystAgent.capabilities.length > 0, 'Agent should have capabilities');
    assert.ok(analystAgent.avatar, 'Agent should have avatar');
    assert.ok(analystAgent.description, 'Agent should have description');
    assert.strictEqual(analystAgent.progress, 0, 'Initial progress should be 0');
  });

  test('Update Agent Status', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    const originalStatus = testAgent.status;
    
    agentCommunicationPanel.updateAgentStatus(
      testAgent.id, 
      AgentStatus.WORKING, 
      'Test task', 
      50
    );
    
    const updatedAgent = agentCommunicationPanel.getAgent(testAgent.id);
    assert.ok(updatedAgent, 'Agent should exist after update');
    assert.strictEqual(updatedAgent.status, AgentStatus.WORKING);
    assert.strictEqual(updatedAgent.currentTask, 'Test task');
    assert.strictEqual(updatedAgent.progress, 50);
    assert.notStrictEqual(updatedAgent.lastActivity, testAgent.lastActivity);
    
    // Reset status
    agentCommunicationPanel.updateAgentStatus(testAgent.id, originalStatus);
  });

  test('Send Agent Message', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    const messageContent = 'Test message from agent';
    
    // This would normally trigger webview updates, but in test we just verify the method works
    assert.doesNotThrow(() => {
      agentCommunicationPanel.sendAgentMessage(
        testAgent.id,
        messageContent,
        MessageType.AGENT_MESSAGE
      );
    });
  });

  test('Request Feedback', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    const feedbackTitle = 'Test Feedback Request';
    const feedbackDescription = 'Please provide your input on this test scenario.';
    
    const feedbackId = agentCommunicationPanel.requestFeedback(
      testAgent.id,
      feedbackTitle,
      feedbackDescription,
      [
        { id: 'option1', label: 'Option 1', value: 'value1' },
        { id: 'option2', label: 'Option 2', value: 'value2' }
      ],
      'high'
    );
    
    assert.ok(feedbackId, 'Feedback request should return an ID');
    assert.ok(feedbackId.startsWith('msg_'), 'Feedback ID should have correct format');
    
    const updatedAgent = agentCommunicationPanel.getAgent(testAgent.id);
    assert.strictEqual(updatedAgent?.status, AgentStatus.WAITING_FOR_FEEDBACK);
  });

  test('Agent Capabilities by Type', () => {
    const agents = agentCommunicationPanel.getAgents();
    
    const analystAgent = agents.find(agent => agent.type === AgentType.ANALYST);
    assert.ok(analystAgent?.capabilities.includes('requirement_analysis'));
    
    const architectAgent = agents.find(agent => agent.type === AgentType.ARCHITECT);
    assert.ok(architectAgent?.capabilities.includes('system_design'));
    
    const developerAgent = agents.find(agent => agent.type === AgentType.DEVELOPER);
    assert.ok(developerAgent?.capabilities.includes('code_generation'));
    
    const testerAgent = agents.find(agent => agent.type === AgentType.TESTER);
    assert.ok(testerAgent?.capabilities.includes('test_creation'));
    
    const reviewerAgent = agents.find(agent => agent.type === AgentType.REVIEWER);
    assert.ok(reviewerAgent?.capabilities.includes('code_review'));
  });

  test('Agent Avatar and Description', () => {
    const agents = agentCommunicationPanel.getAgents();
    
    agents.forEach(agent => {
      assert.ok(agent.avatar, `Agent ${agent.name} should have an avatar`);
      assert.ok(agent.description, `Agent ${agent.name} should have a description`);
      assert.ok(agent.description.length > 10, `Agent ${agent.name} description should be meaningful`);
    });
  });

  test('Message ID Generation', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    
    // Generate multiple messages and check ID uniqueness
    const messageIds: string[] = [];
    
    for (let i = 0; i < 5; i++) {
      agentCommunicationPanel.sendAgentMessage(
        testAgent.id,
        `Test message ${i}`,
        MessageType.AGENT_MESSAGE
      );
      
      // In a real implementation, we would capture the message ID
      // For now, we just verify the method doesn't throw
    }
    
    // Verify IDs would be unique (this is a simplified test)
    assert.ok(true, 'Message generation should work without errors');
  });

  test('Agent Type Enum Values', () => {
    const expectedTypes = [
      'analyst',
      'architect', 
      'developer',
      'tester',
      'reviewer',
      'optimizer',
      'documenter'
    ];
    
    const actualTypes = Object.values(AgentType);
    
    expectedTypes.forEach(type => {
      assert.ok(actualTypes.includes(type as AgentType), `Should include ${type} agent type`);
    });
  });

  test('Agent Status Enum Values', () => {
    const expectedStatuses = [
      'idle',
      'working',
      'waiting_for_feedback',
      'blocked',
      'completed',
      'error'
    ];
    
    const actualStatuses = Object.values(AgentStatus);
    
    expectedStatuses.forEach(status => {
      assert.ok(actualStatuses.includes(status as AgentStatus), `Should include ${status} status`);
    });
  });

  test('Message Type Enum Values', () => {
    const expectedTypes = [
      'user_message',
      'agent_message',
      'system_message',
      'feedback_request',
      'task_update',
      'error_report',
      'suggestion',
      'question'
    ];
    
    const actualTypes = Object.values(MessageType);
    
    expectedTypes.forEach(type => {
      assert.ok(actualTypes.includes(type as MessageType), `Should include ${type} message type`);
    });
  });

  test('Feedback Priority Levels', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    
    const priorities: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical'];
    
    priorities.forEach(priority => {
      assert.doesNotThrow(() => {
        agentCommunicationPanel.requestFeedback(
          testAgent.id,
          `${priority} priority test`,
          'Test description',
          [],
          priority
        );
      }, `Should handle ${priority} priority feedback requests`);
    });
  });

  test('Agent Communication Panel Disposal', () => {
    // Create a new panel for disposal test
    const testPanel = new AgentCommunicationPanel(testContext, workspaceManager);
    
    assert.doesNotThrow(() => {
      testPanel.dispose();
    }, 'Panel disposal should not throw errors');
  });

  test('Get Agent by ID', () => {
    const agents = agentCommunicationPanel.getAgents();
    const firstAgent = agents[0];
    
    const retrievedAgent = agentCommunicationPanel.getAgent(firstAgent.id);
    assert.ok(retrievedAgent, 'Should retrieve agent by ID');
    assert.strictEqual(retrievedAgent.id, firstAgent.id);
    assert.strictEqual(retrievedAgent.name, firstAgent.name);
    
    const nonExistentAgent = agentCommunicationPanel.getAgent('non-existent-id');
    assert.strictEqual(nonExistentAgent, undefined, 'Should return undefined for non-existent agent');
  });

  test('Agent Last Activity Updates', () => {
    const agents = agentCommunicationPanel.getAgents();
    const testAgent = agents[0];
    const originalActivity = testAgent.lastActivity;
    
    // Wait a small amount to ensure timestamp difference
    setTimeout(() => {
      agentCommunicationPanel.sendAgentMessage(
        testAgent.id,
        'Activity update test',
        MessageType.AGENT_MESSAGE
      );
      
      const updatedAgent = agentCommunicationPanel.getAgent(testAgent.id);
      assert.ok(updatedAgent, 'Agent should exist');
      assert.ok(
        updatedAgent.lastActivity > originalActivity,
        'Last activity should be updated after sending message'
      );
    }, 10);
  });
});
