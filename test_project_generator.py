#!/usr/bin/env python3
"""
Test script for the Project Generator
Demonstrates end-to-end project generation capabilities
"""

import asyncio
import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.project_generator import (
    ProjectGenerationPipeline,
    generate_project
)
from src.project_types import (
    ProjectType,
    PackageFormat,
    OutputFormat,
    GenerationConfig
)

async def test_web_application_generation():
    """Test web application project generation"""
    print("🌐 Testing Web Application Generation")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir) / "test_web_app"
        
        config = GenerationConfig(
            project_type=ProjectType.WEB_APPLICATION,
            package_format=PackageFormat.DIRECTORY,
            output_format=OutputFormat.LOCAL_DIRECTORY,
            include_tests=True,
            include_docs=True,
            include_ci_cd=True,
            include_docker=True,
            include_deployment=False,
            code_quality_level="standard",
            security_level="standard"
        )
        
        try:
            result = await generate_project(
                prompt="Create a modern e-commerce web application with user authentication, product catalog, shopping cart, and payment processing",
                project_name="ECommerce Platform",
                project_type="web_application",
                project_path=str(project_path),
                workflow=None
            )
            
            print(f"✅ Generation Result:")
            print(f"   Success: {result['success']}")
            if result['success']:
                print(f"   Project Path: {result['project_path']}")
                print(f"   Phases Completed: {result['phases_completed']}/8")
                print(f"   Total Files: {result.get('total_files', 0)}")
                print(f"   Package Size: {result.get('package_size', 0)} bytes")
                
                # Verify key files exist
                key_files = [
                    "package.json",
                    "src/components/App.tsx",
                    "src/pages/Home.tsx",
                    "Dockerfile",
                    "docker-compose.yml",
                    ".github/workflows/ci.yml",
                    "README.md",
                    "LICENSE"
                ]
                
                print(f"\n📁 File Verification:")
                for file_path in key_files:
                    full_path = project_path / file_path
                    exists = full_path.exists()
                    print(f"   {'✅' if exists else '❌'} {file_path}")
                    
            else:
                print(f"   Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

async def test_api_service_generation():
    """Test API service project generation"""
    print("\n🔌 Testing API Service Generation")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir) / "test_api_service"
        
        config = GenerationConfig(
            project_type=ProjectType.API_SERVICE,
            package_format=PackageFormat.ZIP,
            include_tests=True,
            include_docs=True,
            include_ci_cd=True,
            include_docker=True,
            include_deployment=True
        )
        
        try:
            pipeline = ProjectGenerationPipeline(config)
            result = await pipeline.generate_project(
                prompt="Build a RESTful API service for user management with authentication, CRUD operations, and role-based access control",
                project_name="User Management API",
                project_type="api_service",
                project_path=str(project_path),
                generation_config=config
            )
            
            print(f"✅ Generation Result:")
            print(f"   Success: {result['success']}")
            if result['success']:
                print(f"   Project Path: {result['project_path']}")
                print(f"   Package Path: {result.get('package_path', 'N/A')}")
                print(f"   Phases Completed: {result['phases_completed']}/8")
                print(f"   Total Files: {result.get('total_files', 0)}")
                
                # Verify API-specific files
                api_files = [
                    "package.json",
                    "src/app.ts",
                    "src/server.ts",
                    "src/controllers/userController.ts",
                    "src/routes/userRoutes.ts",
                    "src/middleware/auth.ts",
                    "tsconfig.json",
                    ".eslintrc.json"
                ]
                
                print(f"\n📁 API File Verification:")
                for file_path in api_files:
                    full_path = project_path / file_path
                    exists = full_path.exists()
                    print(f"   {'✅' if exists else '❌'} {file_path}")
                    
            else:
                print(f"   Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")

async def test_data_platform_generation():
    """Test data platform project generation"""
    print("\n📊 Testing Data Platform Generation")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir) / "test_data_platform"
        
        config = GenerationConfig(
            project_type=ProjectType.DATA_PLATFORM,
            package_format=PackageFormat.TAR_GZ,
            include_tests=True,
            include_docs=True,
            include_ci_cd=True,
            include_docker=True,
            include_deployment=True
        )
        
        try:
            pipeline = ProjectGenerationPipeline(config)
            result = await pipeline.generate_project(
                prompt="Create a data analytics platform for processing large datasets, machine learning pipelines, and real-time analytics",
                project_name="Analytics Platform",
                project_type="data_platform",
                project_path=str(project_path),
                generation_config=config
            )
            
            print(f"✅ Generation Result:")
            print(f"   Success: {result['success']}")
            if result['success']:
                print(f"   Project Path: {result['project_path']}")
                print(f"   Package Path: {result.get('package_path', 'N/A')}")
                print(f"   Phases Completed: {result['phases_completed']}/8")
                print(f"   Total Files: {result.get('total_files', 0)}")
                
                # Verify data platform files
                data_files = [
                    "requirements.txt",
                    "pyproject.toml",
                    "src/pipelines/data_pipeline.py",
                    "src/processors/data_processor.py",
                    "src/models/data_model.py",
                    "Dockerfile",
                    "docker-compose.yml"
                ]
                
                print(f"\n📁 Data Platform File Verification:")
                for file_path in data_files:
                    full_path = project_path / file_path
                    exists = full_path.exists()
                    print(f"   {'✅' if exists else '❌'} {file_path}")
                    
            else:
                print(f"   Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")

async def test_packaging_formats():
    """Test different packaging formats"""
    print("\n📦 Testing Packaging Formats")
    print("=" * 60)
    
    formats = [
        (PackageFormat.ZIP, "ZIP Archive"),
        (PackageFormat.TAR_GZ, "TAR.GZ Archive"),
        (PackageFormat.DIRECTORY, "Directory")
    ]
    
    for package_format, format_name in formats:
        print(f"\n🔧 Testing {format_name}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / f"test_packaging_{package_format.value}"
            
            config = GenerationConfig(
                project_type=ProjectType.WEB_APPLICATION,
                package_format=package_format,
                include_tests=False,
                include_docs=False,
                include_ci_cd=False,
                include_docker=False
            )
            
            try:
                pipeline = ProjectGenerationPipeline(config)
                result = await pipeline.generate_project(
                    prompt="Simple web application for packaging test",
                    project_name="Packaging Test",
                    project_type="web_application",
                    project_path=str(project_path),
                    generation_config=config
                )
                
                if result['success']:
                    package_path = result.get('package_path')
                    if package_path and package_path != str(project_path):
                        package_exists = Path(package_path).exists()
                        print(f"   ✅ {format_name}: {package_path} ({'exists' if package_exists else 'missing'})")
                    else:
                        print(f"   ✅ {format_name}: Directory format (no packaging)")
                else:
                    print(f"   ❌ {format_name}: Failed - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ {format_name}: Exception - {e}")

async def test_configuration_options():
    """Test different configuration options"""
    print("\n⚙️ Testing Configuration Options")
    print("=" * 60)
    
    configs = [
        ("Minimal", GenerationConfig(
            project_type=ProjectType.WEB_APPLICATION,
            include_tests=False,
            include_docs=False,
            include_ci_cd=False,
            include_docker=False,
            include_deployment=False
        )),
        ("Standard", GenerationConfig(
            project_type=ProjectType.WEB_APPLICATION,
            include_tests=True,
            include_docs=True,
            include_ci_cd=True,
            include_docker=False,
            include_deployment=False
        )),
        ("Enterprise", GenerationConfig(
            project_type=ProjectType.WEB_APPLICATION,
            include_tests=True,
            include_docs=True,
            include_ci_cd=True,
            include_docker=True,
            include_deployment=True,
            code_quality_level="enterprise",
            security_level="enterprise"
        ))
    ]
    
    for config_name, config in configs:
        print(f"\n🔧 Testing {config_name} Configuration")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / f"test_config_{config_name.lower()}"
            
            try:
                pipeline = ProjectGenerationPipeline(config)
                result = await pipeline.generate_project(
                    prompt=f"Test application with {config_name.lower()} configuration",
                    project_name=f"Config Test {config_name}",
                    project_type="web_application",
                    project_path=str(project_path),
                    generation_config=config
                )
                
                if result['success']:
                    print(f"   ✅ {config_name}: {result.get('total_files', 0)} files generated")
                    
                    # Check for configuration-specific files
                    if config.include_docker:
                        docker_exists = (project_path / "Dockerfile").exists()
                        print(f"      Docker: {'✅' if docker_exists else '❌'}")
                    
                    if config.include_ci_cd:
                        ci_exists = (project_path / ".github" / "workflows" / "ci.yml").exists()
                        print(f"      CI/CD: {'✅' if ci_exists else '❌'}")
                        
                else:
                    print(f"   ❌ {config_name}: Failed - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ {config_name}: Exception - {e}")

def print_usage():
    """Print usage information"""
    print("""
🚀 Project Generator Test Suite

Usage:
    python test_project_generator.py [command]

Commands:
    test        Run all tests (default)
    web         Test web application generation
    api         Test API service generation
    data        Test data platform generation
    packaging   Test packaging formats
    config      Test configuration options
    help        Show this help message

Examples:
    python test_project_generator.py test
    python test_project_generator.py web
    python test_project_generator.py packaging

The Project Generator will:
1. 🏗️  Create comprehensive project structures
2. 📝 Generate all necessary files (source, config, docs)
3. 🐳 Add Docker and CI/CD configurations
4. 📦 Package projects in various formats
5. 🔧 Support multiple project types and configurations
6. ✅ Provide end-to-end project generation pipeline

Project types supported:
- Web Applications (React, TypeScript, Vite)
- API Services (Express.js, TypeScript, REST)
- Data Platforms (Python, Pandas, ML pipelines)
- Mobile Applications (React Native)
- Desktop Applications
- Microservices
- Libraries and CLI tools
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "web":
        await test_web_application_generation()
    elif command == "api":
        await test_api_service_generation()
    elif command == "data":
        await test_data_platform_generation()
    elif command == "packaging":
        await test_packaging_formats()
    elif command == "config":
        await test_configuration_options()
    elif command == "test":
        await test_web_application_generation()
        await test_api_service_generation()
        await test_data_platform_generation()
        await test_packaging_formats()
        await test_configuration_options()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
