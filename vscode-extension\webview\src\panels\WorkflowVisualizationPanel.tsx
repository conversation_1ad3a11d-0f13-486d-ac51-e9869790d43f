import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Square, 
  RefreshCw, 
  Eye, 
  Settings, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  Activity,
  Zap,
  GitBranch,
  Users,
  Filter,
  Search,
  Download,
  Maximize2
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

import { useProgressMonitoringStore } from '@/store';
import { Workflow, WorkflowStep, WorkflowExecution } from '@/types';
import { vscode, useVSCodeMessage, WebviewErrorBoundary } from '@/utils/vscode';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import Input, { Select } from '@/components/common/Input';
import Progress, { CircularProgress } from '@/components/common/Progress';

interface WorkflowVisualizationPanelProps {
  workflowId?: string;
  executionId?: string;
}

const WorkflowVisualizationPanel: React.FC<WorkflowVisualizationPanelProps> = ({
  workflowId,
  executionId
}) => {
  const {
    workflows,
    selectedProject,
    pheromones,
    isLoading,
    error,
    realTimeEnabled,
    loadWorkflows,
    loadPheromones,
    toggleRealTime
  } = useProgressMonitoringStore();

  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState<'diagram' | 'timeline' | 'details'>('diagram');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const diagramRef = useRef<HTMLDivElement>(null);

  // Load initial data
  useEffect(() => {
    loadWorkflows();
    loadExecutions();
  }, [loadWorkflows]);

  // Handle real-time updates
  useVSCodeMessage('workflowUpdate', (data) => {
    if (realTimeEnabled) {
      updateWorkflowExecution(data);
    }
  });

  useVSCodeMessage('pheromoneUpdate', (data) => {
    if (realTimeEnabled && data.type === 'workflow_step') {
      updateStepProgress(data);
    }
  });

  const loadExecutions = async () => {
    try {
      const result = await vscode.sendRequest('getWorkflowExecutions', {
        workflowId: selectedWorkflow?.id,
        projectId: selectedProject?.id
      });
      setExecutions(result);
    } catch (error) {
      toast.error('Failed to load workflow executions');
    }
  };

  const updateWorkflowExecution = (data: any) => {
    setExecutions(prev => 
      prev.map(exec => 
        exec.id === data.executionId 
          ? { ...exec, ...data.updates }
          : exec
      )
    );
  };

  const updateStepProgress = (pheromone: any) => {
    if (selectedExecution && pheromone.executionId === selectedExecution.id) {
      setSelectedExecution(prev => prev ? {
        ...prev,
        steps: prev.steps.map(step =>
          step.id === pheromone.stepId
            ? { ...step, ...pheromone.data }
            : step
        )
      } : null);
    }
  };

  const handleExecuteWorkflow = async (workflow: Workflow) => {
    try {
      const result = await vscode.sendRequest('executeWorkflow', {
        workflowId: workflow.id,
        projectId: selectedProject?.id
      });
      toast.success('Workflow execution started');
      loadExecutions();
    } catch (error) {
      toast.error('Failed to start workflow execution');
    }
  };

  const handlePauseExecution = async (executionId: string) => {
    try {
      await vscode.sendRequest('pauseWorkflowExecution', { executionId });
      toast.success('Workflow execution paused');
    } catch (error) {
      toast.error('Failed to pause workflow execution');
    }
  };

  const handleResumeExecution = async (executionId: string) => {
    try {
      await vscode.sendRequest('resumeWorkflowExecution', { executionId });
      toast.success('Workflow execution resumed');
    } catch (error) {
      toast.error('Failed to resume workflow execution');
    }
  };

  const handleStopExecution = async (executionId: string) => {
    try {
      await vscode.sendRequest('stopWorkflowExecution', { executionId });
      toast.success('Workflow execution stopped');
    } catch (error) {
      toast.error('Failed to stop workflow execution');
    }
  };

  const filteredExecutions = executions.filter(execution => {
    const matchesSearch = execution.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         execution.workflow.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || execution.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (error) {
    return (
      <div className="p-6">
        <Card variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Error</h3>
            <p className="mt-1">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => window.location.reload()}
              icon={<RefreshCw className="w-4 h-4" />}
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <WebviewErrorBoundary>
      <div className={`min-h-screen bg-gray-50 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
        <Toaster position="top-right" />
        
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <GitBranch className="w-6 h-6 mr-2 text-blue-600" />
                Workflow Visualization
              </h1>
              <p className="text-gray-600 mt-1">
                Monitor and control workflow executions in real-time
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant={realTimeEnabled ? "primary" : "outline"}
                onClick={toggleRealTime}
                icon={<Activity className="w-4 h-4" />}
                size="sm"
              >
                Real-time {realTimeEnabled ? 'On' : 'Off'}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setIsFullscreen(!isFullscreen)}
                icon={<Maximize2 className="w-4 h-4" />}
                size="sm"
              >
                {isFullscreen ? 'Exit' : 'Fullscreen'}
              </Button>
              
              <Button
                variant="outline"
                onClick={loadExecutions}
                loading={isLoading}
                icon={<RefreshCw className="w-4 h-4" />}
                size="sm"
              >
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            {/* Search and Filters */}
            <div className="p-4 border-b border-gray-200">
              <div className="space-y-3">
                <Input
                  placeholder="Search workflows..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={<Search className="w-4 h-4" />}
                />
                
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'running', label: 'Running' },
                    { value: 'completed', label: 'Completed' },
                    { value: 'failed', label: 'Failed' },
                    { value: 'paused', label: 'Paused' }
                  ]}
                />
              </div>
            </div>

            {/* Workflow Executions List */}
            <div className="flex-1 overflow-auto">
              <div className="p-4 space-y-3">
                {filteredExecutions.map((execution) => (
                  <WorkflowExecutionCard
                    key={execution.id}
                    execution={execution}
                    isSelected={selectedExecution?.id === execution.id}
                    onSelect={() => setSelectedExecution(execution)}
                    onPause={() => handlePauseExecution(execution.id)}
                    onResume={() => handleResumeExecution(execution.id)}
                    onStop={() => handleStopExecution(execution.id)}
                  />
                ))}
                
                {filteredExecutions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <GitBranch className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>No workflow executions found</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Visualization Area */}
          <div className="flex-1 flex flex-col">
            {selectedExecution ? (
              <>
                {/* View Mode Tabs */}
                <div className="bg-white border-b border-gray-200 px-6 py-3">
                  <div className="flex space-x-1">
                    {[
                      { id: 'diagram', label: 'Diagram', icon: GitBranch },
                      { id: 'timeline', label: 'Timeline', icon: Clock },
                      { id: 'details', label: 'Details', icon: Eye }
                    ].map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setViewMode(tab.id as any)}
                          className={`
                            flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                            ${viewMode === tab.id
                              ? 'bg-blue-100 text-blue-700'
                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                            }
                          `}
                        >
                          <Icon className="w-4 h-4 mr-2" />
                          {tab.label}
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Visualization Content */}
                <div className="flex-1 p-6" ref={diagramRef}>
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={viewMode}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      {viewMode === 'diagram' && (
                        <WorkflowDiagram execution={selectedExecution} />
                      )}
                      {viewMode === 'timeline' && (
                        <WorkflowTimeline execution={selectedExecution} />
                      )}
                      {viewMode === 'details' && (
                        <WorkflowDetails execution={selectedExecution} />
                      )}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <GitBranch className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Select a Workflow Execution
                  </h3>
                  <p className="text-gray-500">
                    Choose a workflow execution from the sidebar to view its visualization
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </WebviewErrorBoundary>
  );
};

// Workflow Execution Card Component
const WorkflowExecutionCard: React.FC<{
  execution: WorkflowExecution;
  isSelected: boolean;
  onSelect: () => void;
  onPause: () => void;
  onResume: () => void;
  onStop: () => void;
}> = ({ execution, isSelected, onSelect, onPause, onResume, onStop }) => {
  const getStatusIcon = () => {
    switch (execution.status) {
      case 'running':
        return <Play className="w-4 h-4 text-green-500" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (execution.status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card
      className={`cursor-pointer transition-all ${
        isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 truncate">
              {execution.name}
            </h4>
            <p className="text-sm text-gray-500 truncate">
              {execution.workflow.name}
            </p>
          </div>
          <div className="flex items-center space-x-1">
            {getStatusIcon()}
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
              {execution.status}
            </span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Progress</span>
            <span className="font-medium">{Math.round(execution.progress * 100)}%</span>
          </div>
          <Progress value={execution.progress} className="h-2" />
        </div>

        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Started {new Date(execution.startTime).toLocaleTimeString()}</span>
          <div className="flex space-x-1">
            {execution.status === 'running' && (
              <>
                <button
                  onClick={(e) => { e.stopPropagation(); onPause(); }}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Pause"
                >
                  <Pause className="w-3 h-3" />
                </button>
                <button
                  onClick={(e) => { e.stopPropagation(); onStop(); }}
                  className="p-1 hover:bg-gray-200 rounded"
                  title="Stop"
                >
                  <Square className="w-3 h-3" />
                </button>
              </>
            )}
            {execution.status === 'paused' && (
              <button
                onClick={(e) => { e.stopPropagation(); onResume(); }}
                className="p-1 hover:bg-gray-200 rounded"
                title="Resume"
              >
                <Play className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Workflow Diagram Component
const WorkflowDiagram: React.FC<{ execution: WorkflowExecution }> = ({ execution }) => {
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);

  const getStepStatus = (step: WorkflowStep) => {
    if (step.status === 'completed') return 'completed';
    if (step.status === 'running') return 'running';
    if (step.status === 'failed') return 'failed';
    if (step.status === 'pending') return 'pending';
    return 'waiting';
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 border-green-500 text-white';
      case 'running':
        return 'bg-blue-500 border-blue-500 text-white animate-pulse';
      case 'failed':
        return 'bg-red-500 border-red-500 text-white';
      case 'pending':
        return 'bg-yellow-500 border-yellow-500 text-white';
      default:
        return 'bg-gray-200 border-gray-300 text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Workflow Diagram: {execution.workflow.name}
        </h3>
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span>Running</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>Pending</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Failed</span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6 min-h-96">
        <div className="flex flex-wrap gap-4 justify-center">
          {execution.steps.map((step, index) => {
            const status = getStepStatus(step);
            const isSelected = selectedStep?.id === step.id;

            return (
              <div key={step.id} className="flex flex-col items-center">
                <motion.div
                  className={`
                    relative w-24 h-24 rounded-lg border-2 flex items-center justify-center cursor-pointer
                    transition-all duration-200 ${getStepColor(status)}
                    ${isSelected ? 'ring-4 ring-blue-300 scale-110' : 'hover:scale-105'}
                  `}
                  onClick={() => setSelectedStep(step)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="text-center">
                    <div className="text-xs font-medium mb-1">
                      Step {index + 1}
                    </div>
                    {status === 'running' && (
                      <div className="absolute -top-1 -right-1">
                        <Zap className="w-4 h-4 text-yellow-400" />
                      </div>
                    )}
                    {step.progress > 0 && (
                      <div className="text-xs">
                        {Math.round(step.progress * 100)}%
                      </div>
                    )}
                  </div>
                </motion.div>

                <div className="mt-2 text-xs text-center max-w-24">
                  <div className="font-medium text-gray-900 truncate">
                    {step.name}
                  </div>
                  <div className="text-gray-500 truncate">
                    {step.agent}
                  </div>
                </div>

                {index < execution.steps.length - 1 && (
                  <div className="flex items-center mt-4 mb-4">
                    <div className="w-8 h-0.5 bg-gray-300"></div>
                    <div className="w-2 h-2 bg-gray-300 rounded-full mx-1"></div>
                    <div className="w-8 h-0.5 bg-gray-300"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {selectedStep && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 bg-gray-50 rounded-lg"
          >
            <h4 className="font-semibold text-gray-900 mb-2">
              {selectedStep.name}
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Agent:</span>
                <span className="ml-2 font-medium">{selectedStep.agent}</span>
              </div>
              <div>
                <span className="text-gray-500">Status:</span>
                <span className="ml-2 font-medium capitalize">{selectedStep.status}</span>
              </div>
              <div>
                <span className="text-gray-500">Progress:</span>
                <span className="ml-2 font-medium">{Math.round(selectedStep.progress * 100)}%</span>
              </div>
              <div>
                <span className="text-gray-500">Duration:</span>
                <span className="ml-2 font-medium">
                  {selectedStep.duration ? `${selectedStep.duration}s` : 'N/A'}
                </span>
              </div>
            </div>
            {selectedStep.description && (
              <div className="mt-3">
                <span className="text-gray-500">Description:</span>
                <p className="mt-1 text-gray-700">{selectedStep.description}</p>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};

// Workflow Timeline Component
const WorkflowTimeline: React.FC<{ execution: WorkflowExecution }> = ({ execution }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">
        Execution Timeline: {execution.workflow.name}
      </h3>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="space-y-4">
          {execution.steps.map((step, index) => (
            <div key={step.id} className="flex items-start space-x-4">
              <div className="flex flex-col items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium
                  ${step.status === 'completed' ? 'bg-green-500' :
                    step.status === 'running' ? 'bg-blue-500' :
                    step.status === 'failed' ? 'bg-red-500' :
                    step.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-400'}
                `}>
                  {index + 1}
                </div>
                {index < execution.steps.length - 1 && (
                  <div className="w-0.5 h-16 bg-gray-200 mt-2"></div>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{step.name}</h4>
                  <span className="text-sm text-gray-500">
                    {step.startTime && new Date(step.startTime).toLocaleTimeString()}
                  </span>
                </div>

                <p className="text-sm text-gray-600 mt-1">
                  Agent: {step.agent}
                </p>

                {step.description && (
                  <p className="text-sm text-gray-500 mt-1">
                    {step.description}
                  </p>
                )}

                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">Progress:</span>
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${step.progress * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-xs font-medium">
                      {Math.round(step.progress * 100)}%
                    </span>
                  </div>

                  {step.duration && (
                    <span className="text-xs text-gray-500">
                      Duration: {step.duration}s
                    </span>
                  )}
                </div>

                {step.output && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-md">
                    <div className="text-xs text-gray-500 mb-1">Output:</div>
                    <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                      {step.output}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Workflow Details Component
const WorkflowDetails: React.FC<{ execution: WorkflowExecution }> = ({ execution }) => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'steps', label: 'Steps' },
    { id: 'agents', label: 'Agents' },
    { id: 'logs', label: 'Logs' }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">
        Execution Details: {execution.workflow.name}
      </h3>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card title="Execution Status">
                <div className="flex items-center space-x-3">
                  <div className={`
                    w-3 h-3 rounded-full
                    ${execution.status === 'completed' ? 'bg-green-500' :
                      execution.status === 'running' ? 'bg-blue-500' :
                      execution.status === 'failed' ? 'bg-red-500' :
                      execution.status === 'paused' ? 'bg-yellow-500' : 'bg-gray-400'}
                  `}></div>
                  <span className="font-medium capitalize">{execution.status}</span>
                </div>
              </Card>

              <Card title="Progress">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall</span>
                    <span className="font-medium">{Math.round(execution.progress * 100)}%</span>
                  </div>
                  <Progress value={execution.progress} />
                </div>
              </Card>

              <Card title="Duration">
                <div className="text-lg font-semibold">
                  {execution.duration ? `${execution.duration}s` : 'In Progress'}
                </div>
              </Card>
            </div>

            <Card title="Execution Information">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Execution ID:</span>
                  <div className="font-mono text-xs mt-1">{execution.id}</div>
                </div>
                <div>
                  <span className="text-gray-500">Workflow:</span>
                  <div className="font-medium mt-1">{execution.workflow.name}</div>
                </div>
                <div>
                  <span className="text-gray-500">Started:</span>
                  <div className="mt-1">{new Date(execution.startTime).toLocaleString()}</div>
                </div>
                <div>
                  <span className="text-gray-500">Project:</span>
                  <div className="font-medium mt-1">{execution.projectId || 'N/A'}</div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'steps' && (
          <div className="space-y-4">
            {execution.steps.map((step, index) => (
              <Card key={step.id} title={`Step ${index + 1}: ${step.name}`}>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Agent:</span>
                      <div className="font-medium mt-1">{step.agent}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Status:</span>
                      <div className="font-medium mt-1 capitalize">{step.status}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Progress:</span>
                      <div className="font-medium mt-1">{Math.round(step.progress * 100)}%</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Duration:</span>
                      <div className="font-medium mt-1">
                        {step.duration ? `${step.duration}s` : 'N/A'}
                      </div>
                    </div>
                  </div>

                  {step.description && (
                    <div>
                      <span className="text-gray-500 text-sm">Description:</span>
                      <p className="mt-1 text-gray-700">{step.description}</p>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'agents' && (
          <div className="space-y-4">
            {Array.from(new Set(execution.steps.map(step => step.agent))).map((agent) => {
              const agentSteps = execution.steps.filter(step => step.agent === agent);
              const completedSteps = agentSteps.filter(step => step.status === 'completed').length;

              return (
                <Card key={agent} title={agent}>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {completedSteps} of {agentSteps.length} steps completed
                      </span>
                      <span className="text-sm font-medium">
                        {Math.round((completedSteps / agentSteps.length) * 100)}%
                      </span>
                    </div>
                    <Progress value={completedSteps / agentSteps.length} />

                    <div className="space-y-2">
                      {agentSteps.map((step, index) => (
                        <div key={step.id} className="flex items-center justify-between text-sm">
                          <span>{step.name}</span>
                          <span className={`
                            px-2 py-1 text-xs rounded-full
                            ${step.status === 'completed' ? 'bg-green-100 text-green-800' :
                              step.status === 'running' ? 'bg-blue-100 text-blue-800' :
                              step.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'}
                          `}>
                            {step.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}

        {activeTab === 'logs' && (
          <div className="space-y-4">
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-auto">
              {execution.logs ? execution.logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-500">[{new Date(log.timestamp).toLocaleTimeString()}]</span>
                  <span className="ml-2">{log.message}</span>
                </div>
              )) : (
                <div className="text-gray-500">No logs available</div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowVisualizationPanel;
