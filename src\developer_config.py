"""
Configuration and integration utilities for the Developer Agent
"""

import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

from developer_agent import ProjectStructure, CodeFile, CodeGenerationContext, ProjectType, CodeQuality
from architect_agent import SystemArchitecture

@dataclass
class DeveloperConfig:
    """Configuration for the Developer Agent"""
    
    # MCP-RAG Configuration
    mcp_url: str = "http://localhost:8051"
    mcp_timeout: int = 60
    mcp_enabled: bool = True
    
    # OpenAI Configuration
    openai_api_key: str = None
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 4000
    
    # Code Generation Configuration
    default_quality_level: str = "production"
    enable_typescript: bool = True
    enable_testing: bool = True
    enable_documentation: bool = True
    
    # File Generation Configuration
    generate_docker: bool = True
    generate_ci_cd: bool = True
    generate_security_config: bool = True
    
    # Quality Gates
    min_test_coverage: float = 80.0
    enable_linting: bool = True
    enable_security_scan: bool = True
    
    def __post_init__(self):
        if self.openai_api_key is None:
            self.openai_api_key = os.getenv("OPENAI_API_KEY")

class DeveloperIntegration:
    """Integration utilities for the Developer Agent with Aetherforge components"""
    
    @staticmethod
    def create_orchestrator_context(project_structure: ProjectStructure, project_id: str, 
                                  project_path: str) -> Dict[str, Any]:
        """Create context for orchestrator integration"""
        return {
            "project_id": project_id,
            "project_path": project_path,
            "agent_role": "developer",
            "phase": "development",
            "project_type": project_structure.type.value,
            "source_files_count": len(project_structure.source_files),
            "test_files_count": len(project_structure.test_files),
            "config_files_count": len(project_structure.config_files),
            "documentation_files_count": len(project_structure.documentation_files),
            "timestamp": "2025-01-20T00:00:00Z"
        }
    
    @staticmethod
    def format_for_qa_agent(project_structure: ProjectStructure) -> Dict[str, Any]:
        """Format project structure for QA agent consumption"""
        return {
            "project_name": project_structure.name,
            "project_type": project_structure.type.value,
            "source_files": [
                {
                    "path": f.path,
                    "language": f.language,
                    "file_type": f.file_type,
                    "tests_required": f.tests_required,
                    "dependencies": f.dependencies
                }
                for f in project_structure.source_files
            ],
            "test_files": [
                {
                    "path": f.path,
                    "language": f.language,
                    "file_type": f.file_type
                }
                for f in project_structure.test_files
            ],
            "dependencies": project_structure.dependencies,
            "scripts": project_structure.scripts,
            "environment_variables": project_structure.environment_variables
        }
    
    @staticmethod
    def create_deployment_context(project_structure: ProjectStructure, 
                                architecture: SystemArchitecture) -> Dict[str, Any]:
        """Create deployment context from project structure and architecture"""
        return {
            "project_name": project_structure.name,
            "project_type": project_structure.type.value,
            "architecture_pattern": architecture.architecture_pattern.value,
            "scalability_tier": architecture.scalability_tier.value,
            "deployment_architecture": architecture.deployment_architecture,
            "infrastructure_requirements": {
                "docker": any("docker" in f.path.lower() for f in project_structure.config_files),
                "database": any("database" in str(architecture.data_architecture).lower() for _ in [1]),
                "caching": any("redis" in str(architecture.data_architecture).lower() for _ in [1]),
                "monitoring": any("prometheus" in str(architecture.deployment_architecture).lower() for _ in [1])
            },
            "security_requirements": architecture.security_architecture,
            "performance_requirements": architecture.quality_attributes.get("performance", {}),
            "environment_variables": project_structure.environment_variables
        }

class CodeGenerationTemplates:
    """Templates for code generation"""
    
    @staticmethod
    def get_react_component_template(is_typescript: bool = True) -> str:
        """Get React component template"""
        if is_typescript:
            return """import React from 'react';
import { {interface_imports} } from '../types';
import styles from './{component_name}.module.css';

interface {component_name}Props {{
  {props_interface}
}}

export const {component_name}: React.FC<{component_name}Props> = ({{
  {props_destructuring}
}}) => {{
  {component_logic}

  return (
    <div className={{styles.{component_name_lower}}}>
      {component_jsx}
    </div>
  );
}};

export default {component_name};"""
        else:
            return """import React from 'react';
import styles from './{component_name}.module.css';

export const {component_name} = ({{
  {props_destructuring}
}}) => {{
  {component_logic}

  return (
    <div className={{styles.{component_name_lower}}}>
      {component_jsx}
    </div>
  );
}};

export default {component_name};"""
    
    @staticmethod
    def get_express_controller_template(is_typescript: bool = True) -> str:
        """Get Express controller template"""
        if is_typescript:
            return """import {{ Request, Response, NextFunction }} from 'express';
import {{ {service_name}Service }} from '../services/{service_name}Service';
import {{ {validation_imports} }} from '../validation';
import {{ ApiError, catchAsync, sendResponse }} from '../utils';
import {{ {type_imports} }} from '../types';

export class {controller_name}Controller {{
  private {service_name_lower}Service: {service_name}Service;

  constructor() {{
    this.{service_name_lower}Service = new {service_name}Service();
  }}

  {controller_methods}
}}"""
        else:
            return """const {{ {service_name}Service }} = require('../services/{service_name}Service');
const {{ {validation_imports} }} = require('../validation');
const {{ ApiError, catchAsync, sendResponse }} = require('../utils');

class {controller_name}Controller {{
  constructor() {{
    this.{service_name_lower}Service = new {service_name}Service();
  }}

  {controller_methods}
}}

module.exports = {{ {controller_name}Controller }};"""
    
    @staticmethod
    def get_jest_test_template(test_type: str = "unit") -> str:
        """Get Jest test template"""
        if test_type == "unit":
            return """import {{ {imports} }} from '{import_path}';
{mock_imports}

{mock_setup}

describe('{test_suite_name}', () => {{
  {setup_teardown}
  
  describe('{feature_name}', () => {{
    {test_cases}
  }});
  
  describe('error handling', () => {{
    {error_test_cases}
  }});
}});"""
        elif test_type == "integration":
            return """import request from 'supertest';
import {{ app }} from '{app_path}';
import {{ {test_imports} }} from '{test_import_path}';

{test_setup}

describe('{endpoint_name} Integration Tests', () => {{
  {setup_teardown}
  
  describe('successful operations', () => {{
    {success_test_cases}
  }});
  
  describe('error scenarios', () => {{
    {error_test_cases}
  }});
  
  describe('authentication and authorization', () => {{
    {auth_test_cases}
  }});
}});"""
        else:
            return """// E2E test template
import {{ test, expect }} from '@playwright/test';

test.describe('{feature_name}', () => {{
  {e2e_test_cases}
}});"""

class ProjectTemplates:
    """Project-level templates and configurations"""
    
    @staticmethod
    def get_package_json_template(project_name: str, project_type: ProjectType) -> Dict[str, Any]:
        """Get package.json template"""
        base_template = {
            "name": project_name.lower().replace(" ", "-"),
            "version": "1.0.0",
            "description": f"Generated by Aetherforge - {project_name}",
            "keywords": ["aetherforge", "generated"],
            "author": "Aetherforge Developer Agent",
            "license": "MIT",
            "engines": {
                "node": ">=18.0.0",
                "npm": ">=8.0.0"
            }
        }
        
        if project_type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
            base_template.update({
                "main": "src/index.js",
                "scripts": {
                    "start": "react-scripts start",
                    "build": "react-scripts build",
                    "test": "react-scripts test",
                    "eject": "react-scripts eject",
                    "lint": "eslint src/**/*.{js,jsx,ts,tsx}",
                    "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix"
                }
            })
        
        if project_type in [ProjectType.BACKEND, ProjectType.API, ProjectType.MICROSERVICE]:
            base_template.update({
                "main": "dist/server.js",
                "scripts": {
                    "start": "node dist/server.js",
                    "dev": "nodemon src/server.ts",
                    "build": "tsc",
                    "test": "jest",
                    "test:watch": "jest --watch",
                    "test:coverage": "jest --coverage",
                    "lint": "eslint src/**/*.{js,ts}",
                    "lint:fix": "eslint src/**/*.{js,ts} --fix"
                }
            })
        
        return base_template
    
    @staticmethod
    def get_tsconfig_template(project_type: ProjectType) -> Dict[str, Any]:
        """Get TypeScript configuration template"""
        base_config = {
            "compilerOptions": {
                "target": "ES2020",
                "lib": ["ES2020"],
                "module": "commonjs",
                "moduleResolution": "node",
                "esModuleInterop": True,
                "allowSyntheticDefaultImports": True,
                "strict": True,
                "skipLibCheck": True,
                "forceConsistentCasingInFileNames": True,
                "resolveJsonModule": True,
                "declaration": True,
                "outDir": "./dist",
                "rootDir": "./src"
            },
            "include": ["src/**/*"],
            "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
        }
        
        if project_type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
            base_config["compilerOptions"].update({
                "target": "ES5",
                "lib": ["DOM", "DOM.Iterable", "ES6"],
                "allowJs": True,
                "module": "esnext",
                "moduleResolution": "bundler",
                "jsx": "react-jsx",
                "isolatedModules": True,
                "noEmit": True
            })
            base_config["include"] = ["src"]
        
        return base_config
    
    @staticmethod
    def get_jest_config_template(project_type: ProjectType) -> Dict[str, Any]:
        """Get Jest configuration template"""
        base_config = {
            "preset": "ts-jest",
            "testEnvironment": "node",
            "roots": ["<rootDir>/src", "<rootDir>/tests"],
            "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"],
            "collectCoverageFrom": [
                "src/**/*.ts",
                "!src/**/*.d.ts",
                "!src/types/**/*"
            ],
            "coverageDirectory": "coverage",
            "coverageReporters": ["text", "lcov", "html"],
            "coverageThreshold": {
                "global": {
                    "branches": 80,
                    "functions": 80,
                    "lines": 80,
                    "statements": 80
                }
            }
        }
        
        if project_type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
            base_config.update({
                "testEnvironment": "jsdom",
                "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"],
                "moduleNameMapping": {
                    "\\.(css|less|scss|sass)$": "identity-obj-proxy"
                }
            })
        
        return base_config
