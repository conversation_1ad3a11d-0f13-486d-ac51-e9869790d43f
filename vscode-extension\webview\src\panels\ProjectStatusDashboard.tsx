import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  RefreshCw,
  Eye,
  Play,
  Pause,
  Square,
  FolderOpen,
  Download,
  Settings,
  Filter,
  Search,
  Calendar,
  Users,
  FileText,
  Code,
  Database,
  Cloud
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

import { useProgressMonitoringStore } from '@/store';
import { Project, ProjectPhase, ProjectMetrics } from '@/types';
import { vscode, useVSCodeMessage, WebviewErrorBoundary } from '@/utils/vscode';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import Input, { Select } from '@/components/common/Input';
import Progress, { CircularProgress } from '@/components/common/Progress';

const ProjectStatusDashboard: React.FC = () => {
  const {
    projects,
    selectedProject,
    isLoading,
    error,
    realTimeEnabled,
    loadProjects,
    selectProject,
    updateProject,
    pauseProject,
    resumeProject,
    cancelProject,
    toggleRealTime
  } = useProgressMonitoringStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('created');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedMetrics, setSelectedMetrics] = useState<ProjectMetrics | null>(null);

  // Load initial data
  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  // Handle real-time updates
  useVSCodeMessage('projectUpdate', (data) => {
    if (realTimeEnabled) {
      updateProject(data.projectId, data.updates);
    }
  });

  useVSCodeMessage('projectMetrics', (data) => {
    setSelectedMetrics(data);
  });

  const handleProjectAction = async (action: string, projectId: string) => {
    try {
      switch (action) {
        case 'pause':
          await pauseProject(projectId);
          toast.success('Project paused');
          break;
        case 'resume':
          await resumeProject(projectId);
          toast.success('Project resumed');
          break;
        case 'cancel':
          await cancelProject(projectId);
          toast.success('Project cancelled');
          break;
        case 'open':
          await vscode.sendRequest('openProject', { projectId });
          break;
        case 'details':
          const metrics = await vscode.sendRequest('getProjectMetrics', { projectId });
          setSelectedMetrics(metrics);
          break;
      }
    } catch (error) {
      toast.error(`Failed to ${action} project`);
    }
  };

  const filteredProjects = projects
    .filter(project => {
      const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           project.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = filterStatus === 'all' || project.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'progress':
          return b.progress.overall - a.progress.overall;
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

  if (error) {
    return (
      <div className="p-6">
        <Card variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Error</h3>
            <p className="mt-1">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => window.location.reload()}
              icon={<RefreshCw className="w-4 h-4" />}
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <WebviewErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <Toaster position="top-right" />
        
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <BarChart3 className="w-6 h-6 mr-2 text-blue-600" />
                Project Status Dashboard
              </h1>
              <p className="text-gray-600 mt-1">
                Monitor project progress and manage active developments
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant={realTimeEnabled ? "primary" : "outline"}
                onClick={toggleRealTime}
                icon={<TrendingUp className="w-4 h-4" />}
                size="sm"
              >
                Real-time {realTimeEnabled ? 'On' : 'Off'}
              </Button>
              
              <Button
                variant="outline"
                onClick={loadProjects}
                loading={isLoading}
                icon={<RefreshCw className="w-4 h-4" />}
                size="sm"
              >
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search className="w-4 h-4" />}
                className="w-64"
              />
              
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                options={[
                  { value: 'all', label: 'All Status' },
                  { value: 'active', label: 'Active' },
                  { value: 'completed', label: 'Completed' },
                  { value: 'paused', label: 'Paused' },
                  { value: 'failed', label: 'Failed' }
                ]}
                className="w-40"
              />
              
              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                options={[
                  { value: 'created', label: 'Created Date' },
                  { value: 'name', label: 'Name' },
                  { value: 'progress', label: 'Progress' }
                ]}
                className="w-40"
              />
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                </div>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <div className="w-4 h-4 flex flex-col space-y-1">
                  <div className="h-0.5 bg-current rounded"></div>
                  <div className="h-0.5 bg-current rounded"></div>
                  <div className="h-0.5 bg-current rounded"></div>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <SummaryCard
              title="Total Projects"
              value={projects.length}
              icon={<FolderOpen className="w-6 h-6" />}
              color="blue"
            />
            <SummaryCard
              title="Active Projects"
              value={projects.filter(p => p.status === 'active').length}
              icon={<Play className="w-6 h-6" />}
              color="green"
            />
            <SummaryCard
              title="Completed"
              value={projects.filter(p => p.status === 'completed').length}
              icon={<CheckCircle className="w-6 h-6" />}
              color="emerald"
            />
            <SummaryCard
              title="Failed"
              value={projects.filter(p => p.status === 'failed').length}
              icon={<XCircle className="w-6 h-6" />}
              color="red"
            />
          </div>

          {/* Projects Grid/List */}
          <div className={`
            ${viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
              : 'space-y-4'
            }
          `}>
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                viewMode={viewMode}
                onAction={(action) => handleProjectAction(action, project.id)}
              />
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <FolderOpen className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No projects found
              </h3>
              <p className="text-gray-500">
                {searchQuery || filterStatus !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Create your first project to get started'
                }
              </p>
            </div>
          )}
        </div>

        {/* Project Metrics Modal */}
        <AnimatePresence>
          {selectedMetrics && (
            <ProjectMetricsModal
              metrics={selectedMetrics}
              onClose={() => setSelectedMetrics(null)}
            />
          )}
        </AnimatePresence>
      </div>
    </WebviewErrorBoundary>
  );
};

// Summary Card Component
const SummaryCard: React.FC<{
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}> = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-100',
    green: 'bg-green-500 text-green-100',
    emerald: 'bg-emerald-500 text-emerald-100',
    red: 'bg-red-500 text-red-100',
    yellow: 'bg-yellow-500 text-yellow-100'
  };

  return (
    <Card className="p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color] || colorClasses.blue}`}>
          {icon}
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
      </div>
    </Card>
  );
};

// Project Card Component
const ProjectCard: React.FC<{
  project: Project;
  viewMode: 'grid' | 'list';
  onAction: (action: string) => void;
}> = ({ project, viewMode, onAction }) => {
  const getStatusIcon = () => {
    switch (project.status) {
      case 'active':
        return <Play className="w-4 h-4 text-green-500" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (project.status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = () => {
    switch (project.type) {
      case 'frontend':
        return <Code className="w-4 h-4" />;
      case 'backend':
        return <Database className="w-4 h-4" />;
      case 'fullstack':
        return <Cloud className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  if (viewMode === 'list') {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              {getTypeIcon()}
              <div>
                <h3 className="font-semibold text-gray-900 truncate">{project.name}</h3>
                <p className="text-sm text-gray-500 truncate">{project.description}</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
                {project.status}
              </span>
            </div>

            <div className="w-32">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-500">Progress</span>
                <span className="font-medium">{Math.round(project.progress.overall * 100)}%</span>
              </div>
              <Progress value={project.progress.overall} className="h-2" />
            </div>

            <div className="text-sm text-gray-500">
              {new Date(project.createdAt).toLocaleDateString()}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAction('details')}
              icon={<Eye className="w-4 h-4" />}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAction('open')}
              icon={<FolderOpen className="w-4 h-4" />}
            />
            {project.status === 'active' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction('pause')}
                icon={<Pause className="w-4 h-4" />}
              />
            )}
            {project.status === 'paused' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction('resume')}
                icon={<Play className="w-4 h-4" />}
              />
            )}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow">
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getTypeIcon()}
            <div>
              <h3 className="font-semibold text-gray-900">{project.name}</h3>
              <p className="text-sm text-gray-500 capitalize">{project.type}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
              {project.status}
            </span>
          </div>
        </div>

        <p className="text-sm text-gray-600 line-clamp-2">
          {project.description}
        </p>

        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-gray-500">Overall Progress</span>
              <span className="font-medium">{Math.round(project.progress.overall * 100)}%</span>
            </div>
            <Progress value={project.progress.overall} />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Created:</span>
              <div className="font-medium">{new Date(project.createdAt).toLocaleDateString()}</div>
            </div>
            <div>
              <span className="text-gray-500">Files:</span>
              <div className="font-medium">{project.metrics?.filesGenerated || 0}</div>
            </div>
          </div>

          {project.progress.phases && (
            <div className="space-y-2">
              <span className="text-sm text-gray-500">Phases:</span>
              <div className="grid grid-cols-2 gap-2">
                {project.progress.phases.slice(0, 4).map((phase, index) => (
                  <div key={index} className="flex items-center space-x-2 text-xs">
                    <div className={`w-2 h-2 rounded-full ${
                      phase.status === 'completed' ? 'bg-green-500' :
                      phase.status === 'active' ? 'bg-blue-500' :
                      phase.status === 'failed' ? 'bg-red-500' : 'bg-gray-300'
                    }`}></div>
                    <span className="truncate">{phase.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between pt-3 border-t border-gray-200">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onAction('details')}
              icon={<Eye className="w-4 h-4" />}
            >
              Details
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onAction('open')}
              icon={<FolderOpen className="w-4 h-4" />}
            >
              Open
            </Button>
          </div>

          <div className="flex space-x-1">
            {project.status === 'active' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction('pause')}
                icon={<Pause className="w-4 h-4" />}
              />
            )}
            {project.status === 'paused' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction('resume')}
                icon={<Play className="w-4 h-4" />}
              />
            )}
            {(project.status === 'active' || project.status === 'paused') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction('cancel')}
                icon={<Square className="w-4 h-4" />}
              />
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Project Metrics Modal Component
const ProjectMetricsModal: React.FC<{
  metrics: ProjectMetrics;
  onClose: () => void;
}> = ({ metrics, onClose }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-lg max-w-4xl max-h-[80vh] overflow-auto w-full"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Project Metrics</h2>
            <Button variant="ghost" onClick={onClose}>×</Button>
          </div>

          <div className="space-y-6">
            {/* Overview Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card title="Files Generated">
                <div className="text-2xl font-bold text-blue-600">
                  {metrics.filesGenerated}
                </div>
              </Card>
              <Card title="Lines of Code">
                <div className="text-2xl font-bold text-green-600">
                  {metrics.linesOfCode?.toLocaleString()}
                </div>
              </Card>
              <Card title="Test Coverage">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round((metrics.testCoverage || 0) * 100)}%
                </div>
              </Card>
              <Card title="Build Time">
                <div className="text-2xl font-bold text-orange-600">
                  {metrics.buildTime}s
                </div>
              </Card>
            </div>

            {/* Progress by Phase */}
            {metrics.phaseProgress && (
              <Card title="Phase Progress">
                <div className="space-y-4">
                  {metrics.phaseProgress.map((phase, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">{phase.name}</span>
                        <span className="text-sm text-gray-500">
                          {Math.round(phase.progress * 100)}%
                        </span>
                      </div>
                      <Progress value={phase.progress} />
                      <div className="text-sm text-gray-600">
                        {phase.description}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Agent Activity */}
            {metrics.agentActivity && (
              <Card title="Agent Activity">
                <div className="space-y-3">
                  {metrics.agentActivity.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          agent.status === 'active' ? 'bg-green-500' :
                          agent.status === 'idle' ? 'bg-yellow-500' : 'bg-gray-400'
                        }`}></div>
                        <div>
                          <div className="font-medium text-gray-900">{agent.name}</div>
                          <div className="text-sm text-gray-500">{agent.currentTask || 'Idle'}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{agent.tasksCompleted} tasks</div>
                        <div className="text-xs text-gray-500">
                          {agent.efficiency ? `${Math.round(agent.efficiency * 100)}% efficiency` : ''}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Quality Metrics */}
            {metrics.qualityMetrics && (
              <Card title="Quality Metrics">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <CircularProgress
                      value={metrics.qualityMetrics.codeQuality || 0}
                      size={80}
                      showLabel
                      label="Code Quality"
                    />
                  </div>
                  <div className="text-center">
                    <CircularProgress
                      value={metrics.qualityMetrics.security || 0}
                      size={80}
                      showLabel
                      label="Security"
                    />
                  </div>
                  <div className="text-center">
                    <CircularProgress
                      value={metrics.qualityMetrics.performance || 0}
                      size={80}
                      showLabel
                      label="Performance"
                    />
                  </div>
                </div>
              </Card>
            )}

            {/* Recent Activity */}
            {metrics.recentActivity && (
              <Card title="Recent Activity">
                <div className="space-y-3 max-h-64 overflow-auto">
                  {metrics.recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === 'success' ? 'bg-green-500' :
                        activity.type === 'error' ? 'bg-red-500' :
                        activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`}></div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900">
                          {activity.message}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(activity.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Error Log */}
            {metrics.errors && metrics.errors.length > 0 && (
              <Card title="Recent Errors">
                <div className="space-y-2 max-h-48 overflow-auto">
                  {metrics.errors.map((error, index) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start space-x-2">
                        <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-red-800">
                            {error.message}
                          </div>
                          <div className="text-xs text-red-600 mt-1">
                            {error.agent} • {new Date(error.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>

          <div className="flex justify-end mt-6 pt-6 border-t border-gray-200">
            <Button onClick={onClose}>Close</Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ProjectStatusDashboard;
