"""
Comprehensive tests for the enhanced API manager.
Tests multi-provider support, fallback mechanisms, retry logic, and rate limiting.
"""

import pytest
import asyncio
import os
import tempfile
import json
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

# Import the API manager components
import sys
sys.path.insert(0, 'src')

from api_manager import (
    APIManager, APIProvider, APIConfig, RateLimiter,
    SecureKeyStorage, APIKeyValidator,
    get_api_manager, generate_text
)

class TestSecureKeyStorage:
    """Test secure key storage functionality"""

    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = Path(self.temp_dir) / "test_keys.enc"
        self.storage = SecureKeyStorage(str(self.storage_path))

    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_store_and_load_key(self):
        """Test storing and loading API keys"""
        test_key = "sk-test123456789"
        provider = "openai"

        # Store key
        self.storage.store_key(provider, test_key)

        # Load key
        loaded_key = self.storage.load_key(provider)
        assert loaded_key == test_key

    def test_load_nonexistent_key(self):
        """Test loading non-existent key"""
        result = self.storage.load_key("nonexistent")
        assert result is None

    def test_delete_key(self):
        """Test deleting API keys"""
        test_key = "sk-test123456789"
        provider = "anthropic"

        # Store key
        self.storage.store_key(provider, test_key)
        assert self.storage.load_key(provider) == test_key

        # Delete key
        success = self.storage.delete_key(provider)
        assert success is True
        assert self.storage.load_key(provider) is None

    def test_list_providers(self):
        """Test listing providers with stored keys"""
        keys = {
            "openai": "sk-test1",
            "anthropic": "sk-ant-test2"
        }

        for provider, key in keys.items():
            self.storage.store_key(provider, key)

        providers = self.storage.list_providers()
        assert set(providers) == set(keys.keys())

class TestAPIKeyValidator:
    """Test API key validation functionality"""

    @pytest.mark.asyncio
    async def test_validate_openai_key_success(self):
        """Test successful OpenAI key validation"""
        with patch('api_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client

            # Mock successful response
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Hello"
            mock_client.chat.completions.create.return_value = mock_response

            result = await APIKeyValidator.validate_openai_key("sk-test123")

            assert result["valid"] is True
            assert result["provider"] == "openai"
            assert "model_access" in result

    @pytest.mark.asyncio
    async def test_validate_anthropic_key_success(self):
        """Test successful Anthropic key validation"""
        with patch('api_manager.AsyncAnthropic') as mock_anthropic:
            mock_client = AsyncMock()
            mock_anthropic.return_value = mock_client

            # Mock successful response
            mock_response = Mock()
            mock_response.content = [Mock()]
            mock_response.content[0].text = "Hello"
            mock_client.messages.create.return_value = mock_response

            result = await APIKeyValidator.validate_anthropic_key("sk-ant-test123")

            assert result["valid"] is True
            assert result["provider"] == "anthropic"

class TestRateLimiter:
    """Test the rate limiter functionality"""
    
    def test_rate_limiter_initialization(self):
        """Test rate limiter initialization"""
        limiter = RateLimiter(max_requests=10, time_window=60)
        assert limiter.max_requests == 10
        assert limiter.time_window == 60
        assert len(limiter.requests) == 0
    
    def test_can_make_request_when_under_limit(self):
        """Test that requests are allowed when under the limit"""
        limiter = RateLimiter(max_requests=5, time_window=60)
        
        # Should allow requests when under limit
        for i in range(5):
            assert limiter.can_make_request() == True
            limiter.record_request()
        
        # Should deny when at limit
        assert limiter.can_make_request() == False
    
    def test_time_window_cleanup(self):
        """Test that old requests are cleaned up"""
        limiter = RateLimiter(max_requests=2, time_window=1)
        
        # Make requests
        limiter.record_request()
        limiter.record_request()
        assert limiter.can_make_request() == False
        
        # Wait for time window to pass
        import time
        time.sleep(1.1)
        
        # Should be able to make requests again
        assert limiter.can_make_request() == True

class TestAPIConfig:
    """Test API configuration"""
    
    def test_api_config_creation(self):
        """Test creating API configuration"""
        config = APIConfig(
            provider=APIProvider.OPENAI,
            api_key="test-key",
            model="gpt-4",
            max_tokens=2000
        )
        
        assert config.provider == APIProvider.OPENAI
        assert config.api_key == "test-key"
        assert config.model == "gpt-4"
        assert config.max_tokens == 2000

class TestAPIManager:
    """Test the main API manager functionality"""
    
    @pytest.fixture
    def temp_config_file(self):
        """Create a temporary config file for testing"""
        config_data = {
            "openai": {
                "api_key": "test-openai-key",
                "model": "gpt-4",
                "max_tokens": 2000,
                "rate_limit_rpm": 60
            },
            "anthropic": {
                "api_key": "test-anthropic-key", 
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 2000,
                "rate_limit_rpm": 60
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_file = f.name
        
        yield temp_file
        
        # Cleanup
        os.unlink(temp_file)
    
    @patch.dict(os.environ, {
        'OPENAI_API_KEY': 'test-openai-key',
        'ANTHROPIC_API_KEY': 'test-anthropic-key'
    })
    def test_api_manager_initialization(self):
        """Test API manager initialization with environment variables"""
        manager = APIManager()
        
        assert APIProvider.OPENAI in manager.providers
        assert APIProvider.ANTHROPIC in manager.providers
        assert len(manager.rate_limiters) >= 2
    
    def test_api_manager_with_config_file(self, temp_config_file):
        """Test API manager initialization with config file"""
        with patch.dict(os.environ, {}, clear=True):
            manager = APIManager(config_file=temp_config_file)
            
            # Should load from config file even without env vars
            assert len(manager.providers) >= 2
    
    @patch.dict(os.environ, {}, clear=True)
    def test_api_manager_no_external_providers(self):
        """Test API manager with no external API providers configured"""
        manager = APIManager()

        # Should still have local providers configured by default
        # but no external providers like OpenAI or Anthropic
        external_providers = [p for p in manager.providers.keys()
                            if p not in [APIProvider.LOCAL, APIProvider.OLLAMA]]
        assert len(external_providers) == 0
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self):
        """Test successful text generation"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager = APIManager()
            
            # Mock the OpenAI client
            mock_client = AsyncMock()
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Generated text"
            mock_client.chat.completions.create.return_value = mock_response
            
            manager.clients[APIProvider.OPENAI] = mock_client
            
            messages = [{"role": "user", "content": "Test prompt"}]
            result = await manager.generate_text(messages)
            
            assert result == "Generated text"
            mock_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_text_fallback(self):
        """Test fallback to secondary provider when primary fails"""
        with patch.dict(os.environ, {
            'OPENAI_API_KEY': 'test-openai-key',
            'ANTHROPIC_API_KEY': 'test-anthropic-key'
        }):
            manager = APIManager()
            
            # Mock OpenAI client to fail
            mock_openai_client = AsyncMock()
            mock_openai_client.chat.completions.create.side_effect = Exception("Quota exceeded")
            manager.clients[APIProvider.OPENAI] = mock_openai_client
            
            # Mock Anthropic client to succeed
            mock_anthropic_client = AsyncMock()
            mock_response = Mock()
            mock_response.content = [Mock()]
            mock_response.content[0].text = "Fallback generated text"
            mock_anthropic_client.messages.create.return_value = mock_response
            manager.clients[APIProvider.ANTHROPIC] = mock_anthropic_client
            
            messages = [{"role": "user", "content": "Test prompt"}]
            result = await manager.generate_text(messages)
            
            assert result == "Fallback generated text"
            # OpenAI should have been tried first
            mock_openai_client.chat.completions.create.assert_called_once()
            # Anthropic should have been used as fallback
            mock_anthropic_client.messages.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_text_all_providers_fail(self):
        """Test when all providers fail"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager = APIManager()
            
            # Mock client to always fail
            mock_client = AsyncMock()
            mock_client.chat.completions.create.side_effect = Exception("API Error")
            manager.clients[APIProvider.OPENAI] = mock_client

            messages = [{"role": "user", "content": "Test prompt"}]

            with pytest.raises(Exception):
                await manager.generate_text(messages)
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager = APIManager()
            
            # Set very low rate limit for testing
            manager.rate_limiters[APIProvider.OPENAI] = RateLimiter(max_requests=1, time_window=60)
            
            # Mock client
            mock_client = AsyncMock()
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Generated text"
            mock_client.chat.completions.create.return_value = mock_response
            manager.clients[APIProvider.OPENAI] = mock_client
            
            messages = [{"role": "user", "content": "Test prompt"}]
            
            # First request should succeed
            result1 = await manager.generate_text(messages)
            assert result1 == "Generated text"
            
            # Second request should be rate limited (but will wait)
            # We'll mock the wait to avoid actual delay in tests
            with patch.object(manager, '_wait_for_rate_limit', new_callable=AsyncMock):
                result2 = await manager.generate_text(messages)
                assert result2 == "Generated text"
    
    def test_get_provider_status(self):
        """Test getting provider status"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager = APIManager()
            
            status = manager.get_provider_status()
            
            assert 'openai' in status
            assert status['openai']['configured'] == True
            assert 'model' in status['openai']
            assert 'rate_limit_remaining' in status['openai']
    
    @pytest.mark.asyncio
    async def test_manager_cleanup(self):
        """Test proper cleanup of API manager"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager = APIManager()
            
            # Mock client with close method
            mock_client = AsyncMock()
            mock_client.close = AsyncMock()
            manager.clients[APIProvider.OPENAI] = mock_client
            
            await manager.close()
            
            mock_client.close.assert_called_once()

class TestGlobalAPIManager:
    """Test global API manager functions"""
    
    @pytest.mark.asyncio
    async def test_global_generate_text(self):
        """Test global generate_text function"""
        with patch('api_manager.get_api_manager') as mock_get_manager:
            mock_manager = AsyncMock()
            mock_manager.generate_text.return_value = "Global generated text"
            mock_get_manager.return_value = mock_manager
            
            messages = [{"role": "user", "content": "Test prompt"}]
            result = await generate_text(messages)
            
            assert result == "Global generated text"
            mock_manager.generate_text.assert_called_once_with(messages)
    
    def test_get_api_manager_singleton(self):
        """Test that get_api_manager returns singleton instance"""
        # Clear any existing instance
        import api_manager
        api_manager._api_manager = None
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            manager1 = get_api_manager()
            manager2 = get_api_manager()
            
            assert manager1 is manager2

class TestEnhancedAPIManager:
    """Test enhanced API manager functionality"""

    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = Path(self.temp_dir) / "test_keys.enc"

        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'OPENAI_API_KEY': 'test-openai-key',
            'ANTHROPIC_API_KEY': 'test-anthropic-key'
        })
        self.env_patcher.start()

        self.api_manager = APIManager(storage_path=str(self.storage_path))

    def teardown_method(self):
        """Cleanup test environment"""
        self.env_patcher.stop()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_get_api_key_from_env(self):
        """Test getting API key from environment variables"""
        key = self.api_manager.get_api_key(APIProvider.OPENAI)
        assert key == "test-openai-key"

    @pytest.mark.asyncio
    async def test_set_api_key_success(self):
        """Test setting API key successfully"""
        with patch.object(self.api_manager.validator, 'validate_key') as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "provider": "openai",
                "message": "Valid key"
            }

            result = await self.api_manager.set_api_key(
                APIProvider.OPENAI,
                "sk-new-test-key",
                validate=True
            )

            assert result["success"] is True
            assert "successfully" in result["message"]

    def test_list_configured_providers(self):
        """Test listing configured providers"""
        providers = self.api_manager.list_configured_providers()

        # Should include all providers
        provider_names = [p["provider"] for p in providers]
        expected_providers = [provider.value for provider in APIProvider]

        for expected in expected_providers:
            assert expected in provider_names

        # Check that OpenAI shows as having a key (from env)
        openai_provider = next(p for p in providers if p["provider"] == "openai")
        assert openai_provider["has_key"] is True

if __name__ == "__main__":
    pytest.main([__file__])
