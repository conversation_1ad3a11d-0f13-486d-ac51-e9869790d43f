# Blog Platform API Environment Configuration

# Application
NODE_ENV=development
PORT=3000
APP_NAME=Blog Platform API

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/blog_platform_api

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# External APIs
OPENAI_API_KEY=your-openai-api-key

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Monitoring
SENTRY_DSN=your-sentry-dsn

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
