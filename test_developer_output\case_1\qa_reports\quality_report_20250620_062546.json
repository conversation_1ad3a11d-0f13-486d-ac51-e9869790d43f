{"project_name": "Unknown Project", "overall_coverage": 0.0, "quality_score": 11.166666666666666, "requirements_compliance": 0.0, "security_score": 0.13333333333333333, "performance_score": 0.611111111111111, "test_suites": [{"name": "Unit Tests", "test_type": "unit", "total_tests": 1, "passed_tests": 0, "failed_tests": 0, "coverage_percentage": 0.0}, {"name": "Integration Tests", "test_type": "integration", "total_tests": 1, "passed_tests": 0, "failed_tests": 0, "coverage_percentage": 0.0}, {"name": "API Tests", "test_type": "api", "total_tests": 1, "passed_tests": 0, "failed_tests": 0, "coverage_percentage": 0.0}], "recommendations": ["Increase test coverage from 0.0% to at least 80.0%", "Add JWT-based authentication", "Implement input validation for all endpoints", "Implement caching strategy for improved performance", "Implement lazy loading for better initial load times"], "timestamp": "2025-06-20T06:25:46.587562"}