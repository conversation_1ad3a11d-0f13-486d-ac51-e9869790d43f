"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const framer_motion_1 = require("framer-motion");
const lucide_react_1 = require("lucide-react");
const recharts_1 = require("recharts");
const react_hot_toast_1 = require("react-hot-toast");
const date_fns_1 = require("date-fns");
const store_1 = require("@/store");
const vscode_1 = require("@/utils/vscode");
const Button_1 = require("@/components/common/Button");
const Card_1 = require("@/components/common/Card");
const Input_1 = require("@/components/common/Input");
const Progress_1 = require("@/components/common/Progress");
const ProgressMonitoringPanel = () => {
    const { projects, selectedProject, workflows, pheromones, systemStatus, isLoading, error, realTimeEnabled, loadProjects, selectProject, loadWorkflows, loadPheromones, loadSystemStatus, updateProject, updateWorkflow, addPheromone, toggleRealTime, pauseProject, resumeProject, cancelProject } = (0, store_1.useProgressMonitoringStore)();
    const [activeTab, setActiveTab] = (0, react_1.useState)('overview');
    const [searchQuery, setSearchQuery] = (0, react_1.useState)('');
    const [statusFilter, setStatusFilter] = (0, react_1.useState)('all');
    const [timeRange, setTimeRange] = (0, react_1.useState)('24h');
    // Load initial data
    (0, react_1.useEffect)(() => {
        loadProjects();
        loadWorkflows();
        loadPheromones();
        loadSystemStatus();
    }, [loadProjects, loadWorkflows, loadPheromones, loadSystemStatus]);
    // Handle real-time updates
    (0, vscode_1.useVSCodeMessage)('projectUpdate', (data) => {
        updateProject(data.projectId, data);
    });
    (0, vscode_1.useVSCodeMessage)('workflowUpdate', (data) => {
        updateWorkflow(data.workflowId, data);
    });
    (0, vscode_1.useVSCodeMessage)('pheromoneUpdate', (data) => {
        if (realTimeEnabled) {
            addPheromone(data);
        }
    });
    const filteredProjects = projects.filter(project => {
        const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            project.description.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'text-green-600 bg-green-100';
            case 'in_progress': return 'text-blue-600 bg-blue-100';
            case 'failed': return 'text-red-600 bg-red-100';
            case 'paused': return 'text-yellow-600 bg-yellow-100';
            case 'cancelled': return 'text-gray-600 bg-gray-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };
    const tabs = [
        { id: 'overview', label: 'Overview', icon: lucide_react_1.BarChart3 },
        { id: 'projects', label: 'Projects', icon: lucide_react_1.Target },
        { id: 'workflows', label: 'Workflows', icon: lucide_react_1.Activity },
        { id: 'pheromones', label: 'Pheromone Trail', icon: lucide_react_1.Zap },
        { id: 'system', label: 'System Status', icon: lucide_react_1.Settings }
    ];
    if (error) {
        return (<div className="p-6">
        <Card_1.default variant="outlined" className="border-red-200 bg-red-50">
          <div className="text-red-800">
            <h3 className="font-semibold">Error</h3>
            <p className="mt-1">{error}</p>
            <Button_1.default variant="outline" size="sm" className="mt-3" onClick={() => window.location.reload()} icon={<lucide_react_1.RefreshCw className="w-4 h-4"/>}>
              Retry
            </Button_1.default>
          </div>
        </Card_1.default>
      </div>);
    }
    return (<vscode_1.WebviewErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <react_hot_toast_1.Toaster position="top-right"/>
        
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <lucide_react_1.Activity className="w-6 h-6 mr-2 text-blue-600"/>
                Progress Monitoring
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time monitoring of projects, workflows, and system status
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Real-time updates</span>
                <button onClick={toggleRealTime} className={`
                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${realTimeEnabled ? 'bg-blue-600' : 'bg-gray-200'}
                  `}>
                  <span className={`
                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${realTimeEnabled ? 'translate-x-6' : 'translate-x-1'}
                    `}/>
                </button>
              </div>
              
              <Button_1.default variant="outline" onClick={() => {
            loadProjects();
            loadWorkflows();
            loadSystemStatus();
        }} icon={<lucide_react_1.RefreshCw className="w-4 h-4"/>}>
                Refresh
              </Button_1.default>
            </div>
          </div>
        </div>

        <div className="flex">
          {/* Sidebar Navigation */}
          <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
            <nav className="p-4 space-y-2">
              {tabs.map((tab) => {
            const Icon = tab.icon;
            return (<button key={tab.id} onClick={() => setActiveTab(tab.id)} className={`
                      w-full flex items-center px-3 py-2 text-left rounded-md transition-colors
                      ${activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-100'}
                    `}>
                    <Icon className="w-4 h-4 mr-3"/>
                    {tab.label}
                  </button>);
        })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            <framer_motion_1.AnimatePresence mode="wait">
              <framer_motion_1.motion.div key={activeTab} initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.2 }}>
                {activeTab === 'overview' && (<OverviewTab projects={projects} workflows={workflows} systemStatus={systemStatus} isLoading={isLoading}/>)}
                
                {activeTab === 'projects' && (<ProjectsTab projects={filteredProjects} selectedProject={selectedProject} searchQuery={searchQuery} setSearchQuery={setSearchQuery} statusFilter={statusFilter} setStatusFilter={setStatusFilter} onSelectProject={selectProject} onPauseProject={pauseProject} onResumeProject={resumeProject} onCancelProject={cancelProject} getStatusColor={getStatusColor} isLoading={isLoading}/>)}
                
                {activeTab === 'workflows' && (<WorkflowsTab workflows={workflows} isLoading={isLoading}/>)}
                
                {activeTab === 'pheromones' && (<PheromonesTab pheromones={pheromones} timeRange={timeRange} setTimeRange={setTimeRange} isLoading={isLoading}/>)}
                
                {activeTab === 'system' && (<SystemStatusTab systemStatus={systemStatus} isLoading={isLoading}/>)}
              </framer_motion_1.motion.div>
            </framer_motion_1.AnimatePresence>
          </div>
        </div>
      </div>
    </vscode_1.WebviewErrorBoundary>);
};
// Overview Tab Component
const OverviewTab = ({ projects, workflows, systemStatus, isLoading }) => {
    const stats = {
        totalProjects: projects.length,
        activeProjects: projects.filter(p => p.status === 'in_progress').length,
        completedProjects: projects.filter(p => p.status === 'completed').length,
        failedProjects: projects.filter(p => p.status === 'failed').length,
        activeWorkflows: workflows.filter(w => w.status === 'running').length,
        totalTasks: projects.reduce((sum, p) => sum + p.tasks.length, 0),
        completedTasks: projects.reduce((sum, p) => sum + p.tasks.filter(t => t.status === 'completed').length, 0)
    };
    const chartData = projects.slice(0, 10).map(project => ({
        name: project.name.substring(0, 15) + (project.name.length > 15 ? '...' : ''),
        progress: project.progress.overall,
        tasks: project.tasks.length,
        completed: project.tasks.filter(t => t.status === 'completed').length
    }));
    const statusData = [
        { name: 'Completed', value: stats.completedProjects, color: '#10B981' },
        { name: 'In Progress', value: stats.activeProjects, color: '#3B82F6' },
        { name: 'Failed', value: stats.failedProjects, color: '#EF4444' },
        { name: 'Other', value: stats.totalProjects - stats.completedProjects - stats.activeProjects - stats.failedProjects, color: '#6B7280' }
    ];
    if (isLoading) {
        return (<div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>);
    }
    return (<div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card_1.default className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <lucide_react_1.Target className="w-6 h-6 text-blue-600"/>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProjects}</p>
            </div>
          </div>
        </Card_1.default>

        <Card_1.default className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <lucide_react_1.CheckCircle className="w-6 h-6 text-green-600"/>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedProjects}</p>
            </div>
          </div>
        </Card_1.default>

        <Card_1.default className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <lucide_react_1.Activity className="w-6 h-6 text-yellow-600"/>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeProjects}</p>
            </div>
          </div>
        </Card_1.default>

        <Card_1.default className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <lucide_react_1.Users className="w-6 h-6 text-purple-600"/>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedTasks}/{stats.totalTasks}</p>
            </div>
          </div>
        </Card_1.default>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card_1.default title="Project Progress" className="p-6">
          <recharts_1.ResponsiveContainer width="100%" height={300}>
            <recharts_1.BarChart data={chartData}>
              <recharts_1.CartesianGrid strokeDasharray="3 3"/>
              <recharts_1.XAxis dataKey="name"/>
              <recharts_1.YAxis />
              <recharts_1.Tooltip />
              <recharts_1.Bar dataKey="progress" fill="#3B82F6"/>
            </recharts_1.BarChart>
          </recharts_1.ResponsiveContainer>
        </Card_1.default>

        <Card_1.default title="Project Status Distribution" className="p-6">
          <recharts_1.ResponsiveContainer width="100%" height={300}>
            <recharts_1.PieChart>
              <recharts_1.Pie data={statusData} cx="50%" cy="50%" outerRadius={80} dataKey="value" label={({ name, value }) => `${name}: ${value}`}>
                {statusData.map((entry, index) => (<recharts_1.Cell key={`cell-${index}`} fill={entry.color}/>))}
              </recharts_1.Pie>
              <recharts_1.Tooltip />
            </recharts_1.PieChart>
          </recharts_1.ResponsiveContainer>
        </Card_1.default>
      </div>

      {/* Recent Activity */}
      <Card_1.default title="Recent Activity" className="p-6">
        <div className="space-y-4">
          {projects.slice(0, 5).map((project) => (<div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status).replace('text-', 'bg-').replace(' bg-', ' ')}`}/>
                <div>
                  <p className="font-medium text-gray-900">{project.name}</p>
                  <p className="text-sm text-gray-500">{project.status}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{Math.round(project.progress.overall)}%</p>
                <p className="text-xs text-gray-500">
                  {(0, date_fns_1.formatDistanceToNow)(new Date(project.updatedAt), { addSuffix: true })}
                </p>
              </div>
            </div>))}
        </div>
      </Card_1.default>
    </div>);
};
function getStatusColor(status) {
    switch (status) {
        case 'completed': return 'text-green-600 bg-green-100';
        case 'in_progress': return 'text-blue-600 bg-blue-100';
        case 'failed': return 'text-red-600 bg-red-100';
        case 'paused': return 'text-yellow-600 bg-yellow-100';
        case 'cancelled': return 'text-gray-600 bg-gray-100';
        default: return 'text-gray-600 bg-gray-100';
    }
}
// Projects Tab Component
const ProjectsTab = ({ projects, selectedProject, searchQuery, setSearchQuery, statusFilter, setStatusFilter, onSelectProject, onPauseProject, onResumeProject, onCancelProject, getStatusColor, isLoading }) => (<div className="space-y-6">
    {/* Filters */}
    <Card_1.default className="p-4">
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input_1.default placeholder="Search projects..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} icon={<lucide_react_1.Search className="w-4 h-4"/>} fullWidth/>
        </div>

        <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md">
          <option value="all">All Status</option>
          <option value="in_progress">In Progress</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
          <option value="paused">Paused</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>
    </Card_1.default>

    {/* Projects Grid */}
    {isLoading ? (<div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>) : projects.length === 0 ? (<Card_1.default className="p-8 text-center">
        <lucide_react_1.Target className="w-12 h-12 text-gray-400 mx-auto mb-4"/>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
        <p className="text-gray-500">Try adjusting your search or filter criteria</p>
      </Card_1.default>) : (<div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {projects.map((project) => (<ProjectCard key={project.id} project={project} isSelected={selectedProject?.id === project.id} onSelect={() => onSelectProject(project)} onPause={() => onPauseProject(project.id)} onResume={() => onResumeProject(project.id)} onCancel={() => onCancelProject(project.id)} getStatusColor={getStatusColor}/>))}
      </div>)}

    {/* Selected Project Details */}
    {selectedProject && (<Card_1.default title={`${selectedProject.name} - Details`} className="p-6">
        <ProjectDetails project={selectedProject}/>
      </Card_1.default>)}
  </div>);
// Project Card Component
const ProjectCard = ({ project, isSelected, onSelect, onPause, onResume, onCancel, getStatusColor }) => (<framer_motion_1.motion.div whileHover={{ y: -2 }} className={`
      border rounded-lg p-4 cursor-pointer transition-all
      ${isSelected ? 'border-blue-300 bg-blue-50' : 'border-gray-200 bg-white hover:border-gray-300'}
    `} onClick={onSelect}>
    <div className="flex items-start justify-between mb-3">
      <div className="flex-1">
        <h3 className="font-semibold text-gray-900 truncate">{project.name}</h3>
        <p className="text-sm text-gray-500 mt-1 line-clamp-2">{project.description}</p>
      </div>

      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
        {project.status.replace('_', ' ')}
      </span>
    </div>

    <div className="space-y-3">
      <Progress_1.default value={project.progress.overall} showLabel label="Overall Progress" animated/>

      <div className="flex justify-between text-sm text-gray-600">
        <span>Tasks: {project.tasks.filter(t => t.status === 'completed').length}/{project.tasks.length}</span>
        <span>Agents: {project.agents.length}</span>
      </div>

      <div className="flex justify-between items-center">
        <span className="text-xs text-gray-500">
          Updated {(0, date_fns_1.formatDistanceToNow)(new Date(project.updatedAt), { addSuffix: true })}
        </span>

        <div className="flex space-x-1" onClick={(e) => e.stopPropagation()}>
          {project.status === 'in_progress' && (<Button_1.default variant="ghost" size="sm" onClick={onPause} icon={<lucide_react_1.Pause className="w-3 h-3"/>}/>)}
          {project.status === 'paused' && (<Button_1.default variant="ghost" size="sm" onClick={onResume} icon={<lucide_react_1.Play className="w-3 h-3"/>}/>)}
          {['in_progress', 'paused'].includes(project.status) && (<Button_1.default variant="ghost" size="sm" onClick={onCancel} icon={<lucide_react_1.Square className="w-3 h-3"/>}/>)}
        </div>
      </div>
    </div>
  </framer_motion_1.motion.div>);
// Project Details Component
const ProjectDetails = ({ project }) => (<div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="space-y-4">
        <h4 className="font-semibold text-gray-900">Progress Overview</h4>
        <Progress_1.CircularProgress value={project.progress.overall} size={120} showLabel label="Overall"/>
      </div>

      <div className="space-y-4">
        <h4 className="font-semibold text-gray-900">Phase Progress</h4>
        <Progress_1.StepProgress steps={project.progress.phases.map(phase => ({
        id: phase.name,
        title: phase.name,
        status: phase.status
    }))} currentStep={project.progress.phases.findIndex(p => p.status === 'active')} orientation="vertical"/>
      </div>

      <div className="space-y-4">
        <h4 className="font-semibold text-gray-900">Metrics</h4>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Code Quality</span>
            <span className="text-sm font-medium">{Math.round(project.metrics.codeQuality * 100)}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Test Coverage</span>
            <span className="text-sm font-medium">{Math.round(project.metrics.testCoverage * 100)}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Performance</span>
            <span className="text-sm font-medium">{Math.round(project.metrics.performance * 100)}%</span>
          </div>
        </div>
      </div>
    </div>

    <div>
      <h4 className="font-semibold text-gray-900 mb-3">Recent Timeline</h4>
      <div className="space-y-3">
        {project.timeline.slice(0, 5).map((event) => (<div key={event.id} className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"/>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">{event.title}</p>
              <p className="text-xs text-gray-500">{event.description}</p>
              <p className="text-xs text-gray-400 mt-1">
                {(0, date_fns_1.formatDistanceToNow)(new Date(event.timestamp), { addSuffix: true })}
              </p>
            </div>
          </div>))}
      </div>
    </div>
  </div>);
// Workflows Tab Component
const WorkflowsTab = ({ workflows, isLoading }) => (<div className="space-y-6">
    {isLoading ? (<div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>) : workflows.length === 0 ? (<Card_1.default className="p-8 text-center">
        <lucide_react_1.Activity className="w-12 h-12 text-gray-400 mx-auto mb-4"/>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
        <p className="text-gray-500">Workflows will appear here when projects are running</p>
      </Card_1.default>) : (<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {workflows.map((workflow) => (<WorkflowCard key={workflow.id} workflow={workflow}/>))}
      </div>)}
  </div>);
// Workflow Card Component
const WorkflowCard = ({ workflow }) => (<Card_1.default className="p-6">
    <div className="flex items-start justify-between mb-4">
      <div>
        <h3 className="font-semibold text-gray-900">{workflow.name}</h3>
        <p className="text-sm text-gray-500 mt-1">{workflow.description}</p>
      </div>

      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(workflow.status)}`}>
        {workflow.status}
      </span>
    </div>

    <div className="space-y-4">
      <Progress_1.default value={workflow.progress} showLabel label="Workflow Progress" animated/>

      <div className="space-y-2">
        {workflow.steps.map((step, index) => (<div key={step.id} className="flex items-center space-x-3">
            <div className={`
              w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium
              ${step.status === 'completed' ? 'bg-green-100 text-green-800' :
            step.status === 'running' ? 'bg-blue-100 text-blue-800' :
                step.status === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-600'}
            `}>
              {step.status === 'completed' ? '✓' : index + 1}
            </div>

            <div className="flex-1">
              <span className="text-sm font-medium text-gray-900">{step.name}</span>
              {step.status === 'running' && (<div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                  <div className="bg-blue-500 h-1 rounded-full transition-all duration-300" style={{ width: `${step.progress}%` }}/>
                </div>)}
            </div>
          </div>))}
      </div>

      <div className="flex justify-between text-sm text-gray-600">
        <span>Steps: {workflow.steps.filter(s => s.status === 'completed').length}/{workflow.steps.length}</span>
        <span>
          {workflow.startedAt && (0, date_fns_1.formatDistanceToNow)(new Date(workflow.startedAt), { addSuffix: true })}
        </span>
      </div>
    </div>
  </Card_1.default>);
// Pheromones Tab Component
const PheromonesTab = ({ pheromones, timeRange, setTimeRange, isLoading }) => {
    const filteredPheromones = pheromones.slice(0, 100); // Show last 100 pheromones
    const pheromonesByType = pheromones.reduce((acc, pheromone) => {
        acc[pheromone.type] = (acc[pheromone.type] || 0) + 1;
        return acc;
    }, {});
    const chartData = Object.entries(pheromonesByType).map(([type, count]) => ({
        type,
        count
    }));
    return (<div className="space-y-6">
      {/* Controls */}
      <Card_1.default className="p-4">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Pheromone Activity</h3>
          <select value={timeRange} onChange={(e) => setTimeRange(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md">
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </Card_1.default>

      {/* Pheromone Type Distribution */}
      <Card_1.default title="Pheromone Types" className="p-6">
        <recharts_1.ResponsiveContainer width="100%" height={300}>
          <recharts_1.BarChart data={chartData}>
            <recharts_1.CartesianGrid strokeDasharray="3 3"/>
            <recharts_1.XAxis dataKey="type"/>
            <recharts_1.YAxis />
            <recharts_1.Tooltip />
            <recharts_1.Bar dataKey="count" fill="#8B5CF6"/>
          </recharts_1.BarChart>
        </recharts_1.ResponsiveContainer>
      </Card_1.default>

      {/* Recent Pheromones */}
      <Card_1.default title="Recent Pheromone Trail" className="p-6">
        {isLoading ? (<div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>) : filteredPheromones.length === 0 ? (<div className="text-center py-8 text-gray-500">
            No pheromone activity found
          </div>) : (<div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredPheromones.map((pheromone) => (<div key={pheromone.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className={`
                  w-3 h-3 rounded-full mt-1
                  ${pheromone.strength > 0.8 ? 'bg-red-500' :
                    pheromone.strength > 0.5 ? 'bg-yellow-500' :
                        'bg-green-500'}
                `}/>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{pheromone.type}</span>
                    <span className="text-xs text-gray-500">
                      {(0, date_fns_1.formatDistanceToNow)(new Date(pheromone.timestamp), { addSuffix: true })}
                    </span>
                  </div>

                  <p className="text-sm text-gray-600 mt-1">
                    From: {pheromone.source} {pheromone.target && `→ ${pheromone.target}`}
                  </p>

                  {pheromone.data && (<p className="text-xs text-gray-500 mt-1 truncate">
                      {JSON.stringify(pheromone.data).substring(0, 100)}...
                    </p>)}
                </div>

                <div className="text-right">
                  <div className="text-xs text-gray-500">Strength</div>
                  <div className="text-sm font-medium">{Math.round(pheromone.strength * 100)}%</div>
                </div>
              </div>))}
          </div>)}
      </Card_1.default>
    </div>);
};
// System Status Tab Component
const SystemStatusTab = ({ systemStatus, isLoading }) => {
    if (isLoading) {
        return (<div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>);
    }
    if (!systemStatus) {
        return (<Card_1.default className="p-8 text-center">
        <lucide_react_1.Settings className="w-12 h-12 text-gray-400 mx-auto mb-4"/>
        <h3 className="text-lg font-medium text-gray-900 mb-2">System status unavailable</h3>
        <p className="text-gray-500">Unable to fetch system status information</p>
      </Card_1.default>);
    }
    const services = [
        { name: 'Orchestrator', status: systemStatus.orchestrator, icon: lucide_react_1.Settings },
        { name: 'Agents', status: systemStatus.agents, icon: lucide_react_1.Users },
        { name: 'Database', status: systemStatus.database, icon: lucide_react_1.Activity },
        { name: 'Message Queue', status: systemStatus.messageQueue, icon: lucide_react_1.Zap },
        { name: 'Storage', status: systemStatus.storage, icon: lucide_react_1.Target },
        { name: 'Monitoring', status: systemStatus.monitoring, icon: lucide_react_1.TrendingUp }
    ];
    const getServiceStatusColor = (status) => {
        switch (status) {
            case 'healthy': return 'text-green-600 bg-green-100';
            case 'degraded': return 'text-yellow-600 bg-yellow-100';
            case 'unhealthy': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };
    return (<div className="space-y-6">
      {/* Service Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => {
            const Icon = service.icon;
            const status = service.status || { status: 'unknown', uptime: 0, responseTime: 0, errorRate: 0 };
            return (<Card_1.default key={service.name} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="p-2 bg-gray-100 rounded-lg mr-3">
                    <Icon className="w-5 h-5 text-gray-600"/>
                  </div>
                  <h3 className="font-semibold text-gray-900">{service.name}</h3>
                </div>

                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getServiceStatusColor(status.status)}`}>
                  {status.status || 'unknown'}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Uptime</span>
                  <span className="font-medium">{Math.round(status.uptime || 0)}%</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Response Time</span>
                  <span className="font-medium">{status.responseTime || 0}ms</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Error Rate</span>
                  <span className="font-medium">{Math.round((status.errorRate || 0) * 100)}%</span>
                </div>

                {status.version && (<div className="flex justify-between text-sm">
                    <span className="text-gray-600">Version</span>
                    <span className="font-medium">{status.version}</span>
                  </div>)}

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`h-2 rounded-full transition-all duration-300 ${status.status === 'healthy' ? 'bg-green-500' :
                    status.status === 'degraded' ? 'bg-yellow-500' :
                        'bg-red-500'}`} style={{ width: `${status.uptime || 0}%` }}/>
                </div>
              </div>
            </Card_1.default>);
        })}
      </div>

      {/* System Metrics */}
      <Card_1.default title="System Performance" className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Response Times</h4>
            <recharts_1.ResponsiveContainer width="100%" height={200}>
              <recharts_1.LineChart data={services.map(s => ({
            name: s.name,
            responseTime: s.status?.responseTime || 0
        }))}>
                <recharts_1.CartesianGrid strokeDasharray="3 3"/>
                <recharts_1.XAxis dataKey="name"/>
                <recharts_1.YAxis />
                <recharts_1.Tooltip />
                <recharts_1.Line type="monotone" dataKey="responseTime" stroke="#3B82F6" strokeWidth={2}/>
              </recharts_1.LineChart>
            </recharts_1.ResponsiveContainer>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Error Rates</h4>
            <recharts_1.ResponsiveContainer width="100%" height={200}>
              <recharts_1.AreaChart data={services.map(s => ({
            name: s.name,
            errorRate: (s.status?.errorRate || 0) * 100
        }))}>
                <recharts_1.CartesianGrid strokeDasharray="3 3"/>
                <recharts_1.XAxis dataKey="name"/>
                <recharts_1.YAxis />
                <recharts_1.Tooltip />
                <recharts_1.Area type="monotone" dataKey="errorRate" stroke="#EF4444" fill="#FEE2E2"/>
              </recharts_1.AreaChart>
            </recharts_1.ResponsiveContainer>
          </div>
        </div>
      </Card_1.default>

      {/* System Health Summary */}
      <Card_1.default title="Health Summary" className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {services.filter(s => s.status?.status === 'healthy').length}
            </div>
            <div className="text-sm text-gray-600">Healthy Services</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {services.filter(s => s.status?.status === 'degraded').length}
            </div>
            <div className="text-sm text-gray-600">Degraded Services</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {services.filter(s => s.status?.status === 'unhealthy').length}
            </div>
            <div className="text-sm text-gray-600">Unhealthy Services</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(services.reduce((sum, s) => sum + (s.status?.uptime || 0), 0) / services.length)}%
            </div>
            <div className="text-sm text-gray-600">Average Uptime</div>
          </div>
        </div>
      </Card_1.default>
    </div>);
};
exports.default = ProgressMonitoringPanel;
//# sourceMappingURL=ProgressMonitoringPanel.js.map