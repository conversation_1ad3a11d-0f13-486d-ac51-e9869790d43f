<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Aetherforge - Agent Interaction</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            overflow: hidden;
        }
        
        #root {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        /* Loading spinner */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-progressBar-background);
            border-top: 4px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 16px;
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
        }
        
        /* Connection status indicator */
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .connection-status.connected {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        
        .connection-status.connecting {
            background-color: var(--vscode-testing-iconQueued);
            color: white;
        }
        
        .connection-status.disconnected {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        
        /* Chat-specific styles */
        .chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }
        
        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin: 0 8px;
        }
        
        .message.user .message-avatar {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }
        
        .message.agent .message-avatar {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }
        
        .message.agent .message-content {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
        }
        
        .message-time {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }
        
        .chat-input {
            padding: 16px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-panel-background);
        }
        
        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }
        
        .message-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 20px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: var(--vscode-focusBorder);
        }
        
        .send-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* Typing indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            font-size: 14px;
        }
        
        .typing-dots {
            display: inline-flex;
            margin-left: 8px;
        }
        
        .typing-dots span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: var(--vscode-descriptionForeground);
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="spinner"></div>
            <div class="loading-text">Connecting to Agents...</div>
        </div>
    </div>
    
    <div class="connection-status disconnected" id="connectionStatus">
        Disconnected
    </div>
    
    <script>
        // Initialize VS Code API
        const vscode = acquireVsCodeApi();
        
        // Connection status management
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }
        
        // Handle theme changes
        function updateTheme() {
            const body = document.body;
            const theme = body.getAttribute('data-vscode-theme-kind');
            
            body.className = '';
            if (theme === 'vscode-light') {
                body.classList.add('vscode-light');
            } else if (theme === 'vscode-dark') {
                body.classList.add('vscode-dark');
            } else if (theme === 'vscode-high-contrast') {
                body.classList.add('vscode-high-contrast');
            }
        }
        
        // Apply initial theme
        updateTheme();
        
        // Listen for theme changes
        new MutationObserver(updateTheme).observe(document.body, {
            attributes: true,
            attributeFilter: ['data-vscode-theme-kind']
        });
        
        // Message handling
        window.addEventListener('message', (event) => {
            const message = event.data;
            
            switch (message.command) {
                case 'connectionStatus':
                    updateConnectionStatus(message.status);
                    break;
                case 'agentMessage':
                    // Handle incoming agent messages
                    break;
                case 'typingIndicator':
                    // Show/hide typing indicator
                    break;
            }
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Agent Interaction Panel Error:', event.error);
            
            vscode.postMessage({
                command: 'error',
                data: {
                    message: event.error?.message || 'Agent interaction panel error',
                    stack: event.error?.stack
                }
            });
        });
        
        // Notify extension that panel is ready
        vscode.postMessage({
            command: 'panelReady',
            data: { panel: 'agentInteraction' }
        });
    </script>
</body>
</html>
