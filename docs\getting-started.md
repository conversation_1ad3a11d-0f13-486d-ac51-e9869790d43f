# Getting Started with <PERSON>F<PERSON><PERSON>

Welcome to TaoForge! This guide will help you get up and running with the autonomous AI software creation system in just a few minutes.

## 🎯 What You'll Learn

By the end of this guide, you'll:
- Have TaoForge installed and configured
- Understand the basic concepts and workflow
- Create your first AI-generated project
- Know where to go for more advanced features

## 📋 Prerequisites

Before you begin, ensure you have:

- **Python 3.9+** installed on your system
- **Node.js 16+** (for web projects)
- **Git** for version control
- **OpenAI API Key** (for AI functionality)
- **VS Code** (recommended for the best experience)

## 🚀 Quick Installation

### Option 1: Using pip (Recommended)

```bash
# Install TaoForge
pip install taoforge

# Verify installation
taoforge --version
```

### Option 2: From Source

```bash
# Clone the repository
git clone https://github.com/taoforge/taoforge.git
cd taoforge

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Option 3: Using Docker

```bash
# Pull the official image
docker pull taoforge/taoforge:latest

# Run TaoForge
docker run -p 8000:8000 -e OPENAI_API_KEY=your_key_here taoforge/taoforge:latest
```

## ⚙️ Initial Configuration

### 1. Set Up Your API Key

Create a `.env` file in your working directory:

```bash
# .env
OPENAI_API_KEY=your_openai_api_key_here
TAOFORGE_LOG_LEVEL=info
TAOFORGE_PROJECTS_DIR=./projects
```

### 2. Initialize TaoForge

```bash
# Initialize configuration
taoforge init

# This creates:
# - ~/.taoforge/config.json
# - ~/.taoforge/workflows/
# - ~/.taoforge/templates/
```

### 3. Verify Setup

```bash
# Check system status
taoforge status

# Expected output:
# ✅ TaoForge v1.0.0
# ✅ OpenAI API: Connected
# ✅ Orchestrator: Running
# ✅ Pheromone System: Active
# ✅ VS Code Extension: Available
```

## 🎨 Install VS Code Extension (Optional but Recommended)

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "TaoForge"
4. Click "Install"
5. Reload VS Code

Or install from command line:
```bash
code --install-extension taoforge.taoforge-vscode
```

## 🏗️ Your First Project

Let's create a simple task management application to see TaoForge in action.

### Method 1: Using the CLI

```bash
# Create a new project
taoforge create "A simple task management app with user authentication"

# Follow the interactive prompts:
# Project name: TaskMaster
# Project type: fullstack
# Framework preference: React + Node.js
# Database: PostgreSQL
```

### Method 2: Using VS Code Extension

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "TaoForge: Create Project"
4. Enter your project description: "A simple task management app with user authentication"
5. Follow the prompts in the sidebar

### Method 3: Using the Web Interface

1. Start the TaoForge server:
   ```bash
   taoforge serve
   ```
2. Open http://localhost:8000 in your browser
3. Click "Create New Project"
4. Fill in the project details
5. Click "Generate Project"

## 📊 Understanding the Process

TaoForge follows the BMAD methodology:

### 1. **Business Analysis** (Analyst Agent)
- Analyzes your requirements
- Creates user stories and acceptance criteria
- Defines functional and non-functional requirements

### 2. **Model Design** (Architect Agent)
- Designs system architecture
- Selects appropriate technologies
- Creates API specifications and database schemas

### 3. **Architecture Implementation** (Developer Agent)
- Generates complete source code
- Creates configuration files
- Sets up project structure

### 4. **Development Validation** (QA Agent)
- Creates test suites
- Validates code quality
- Ensures requirements are met

## 🔍 Monitoring Progress

### Real-time Progress Tracking

```bash
# Watch project creation in real-time
taoforge watch <project-id>

# View pheromone trail (agent communication)
taoforge pheromones <project-id>

# Check agent status
taoforge agents status
```

### VS Code Integration

The VS Code extension provides:
- **Real-time progress panel** showing agent activities
- **Pheromone trail visualization** for debugging
- **Agent chat interface** for direct communication
- **Project structure preview** as it's being created

## 📁 Project Structure

After creation, your project will have this structure:

```
TaskMaster/
├── .aetherforge.json          # Project metadata
├── README.md                  # Generated documentation
├── package.json               # Dependencies and scripts
├── .env.example              # Environment variables template
├── .gitignore                # Git ignore rules
├── docs/                     # Generated documentation
│   ├── requirements.md       # Business requirements
│   ├── architecture.md       # System design
│   ├── api-docs.md          # API documentation
│   └── deployment.md        # Deployment guide
├── client/                   # Frontend application
│   ├── src/
│   ├── public/
│   └── package.json
├── server/                   # Backend application
│   ├── src/
│   ├── tests/
│   └── package.json
├── database/                 # Database schemas
│   ├── migrations/
│   └── seeds/
└── tests/                    # Integration tests
    ├── unit/
    ├── integration/
    └── e2e/
```

## 🚀 Running Your Project

### Development Mode

```bash
cd TaskMaster

# Install dependencies
npm run install:all

# Start development servers
npm run dev

# This starts:
# - Frontend: http://localhost:3000
# - Backend: http://localhost:3001
# - Database: PostgreSQL on port 5432
```

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up --build

# Access your application at http://localhost:3000
```

## 🎯 Next Steps

Congratulations! You've successfully created your first TaoForge project. Here's what to explore next:

### 📚 Learn More
- **[Developer Guide](user-guides/developers-guide.md)** - Advanced features and customization
- **[API Documentation](api/README.md)** - Integrate TaoForge into your workflow
- **[Project Types](examples/project-types/README.md)** - Explore different application types

### 🛠️ Customize Your Workflow
- **[Configuration Guide](configuration/README.md)** - Customize agents and workflows
- **[Templates](guides/templates.md)** - Create reusable project templates
- **[Integrations](examples/integrations/README.md)** - Connect with existing tools

### 🎨 Advanced Features
- **[Team Collaboration](user-guides/team-collaboration.md)** - Work with your team
- **[Custom Agents](technical/custom-agents.md)** - Create specialized agents
- **[Workflow Customization](technical/workflow-engine.md)** - Modify the BMAD process

## 🆘 Need Help?

### Common Issues

**Project creation fails:**
```bash
# Check system status
taoforge status

# View logs
taoforge logs --tail 50

# Reset configuration
taoforge reset --config
```

**VS Code extension not working:**
1. Ensure TaoForge server is running: `taoforge serve`
2. Check extension settings: `Ctrl+,` → Search "TaoForge"
3. Reload VS Code: `Ctrl+Shift+P` → "Developer: Reload Window"

**API key issues:**
```bash
# Test API connection
taoforge test-api

# Update API key
taoforge config set OPENAI_API_KEY your_new_key
```

### Getting Support

- **[FAQ](support/faq.md)** - Quick answers to common questions
- **[Troubleshooting Guide](support/troubleshooting.md)** - Detailed problem-solving
- **[Community Forum](support/community.md)** - Connect with other users
- **[GitHub Issues](https://github.com/taoforge/taoforge/issues)** - Report bugs

## 🎉 You're Ready!

You now have TaoForge set up and have created your first AI-generated project. The system is designed to learn from your preferences and improve over time.

**Pro Tip:** The more specific and detailed your project descriptions, the better TaoForge can understand and implement your vision.

Ready to dive deeper? Check out our [Developer Guide](user-guides/developers-guide.md) for advanced features and customization options.
