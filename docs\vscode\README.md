# TaoForge VS Code Extension

The TaoForge VS Code extension provides seamless integration with the autonomous AI development system, allowing you to create, monitor, and manage AI-generated projects directly from your favorite editor.

## 🚀 Quick Start

### Installation

1. **From VS Code Marketplace:**
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "TaoForge"
   - Click "Install"

2. **From Command Line:**
   ```bash
   code --install-extension taoforge.taoforge-vscode
   ```

3. **Manual Installation:**
   - Download the `.vsix` file from releases
   - Run: `code --install-extension taoforge-vscode.vsix`

### Initial Setup

1. **Configure API Connection:**
   - Open VS Code Settings (Ctrl+,)
   - Search for "TaoForge"
   - Set your API endpoint and authentication

2. **Verify Connection:**
   - Press Ctrl+Shift+P
   - Run "TaoForge: Check Connection"
   - Ensure all systems are green ✅

## 🎯 Core Features

### 1. Project Creation

Create new projects with guided prompts:

- **Command:** `TaoForge: Create Project`
- **Shortcut:** `Ctrl+Shift+T` (Windows/Linux), `Cmd+Shift+T` (Mac)
- **Description:** Launch the project creation wizard

### 2. Quick Create

Rapid project creation with templates:

- **Command:** `TaoForge: Quick Create`
- **Shortcut:** `Ctrl+Alt+Q`
- **Description:** Create projects from predefined templates

### 3. Agent Monitoring

Real-time agent activity monitoring:

- **Command:** `TaoForge: Show Agent Panel`
- **Shortcut:** `Ctrl+Alt+A`
- **Description:** Open the agent monitoring dashboard

### 4. Pheromone Visualization

Visualize agent communication patterns:

- **Command:** `TaoForge: Show Pheromone Trail`
- **Shortcut:** `Ctrl+Alt+P`
- **Description:** Display pheromone trail visualization

## 📋 All Commands

### Project Management

| Command | Shortcut | Description |
|---------|----------|-------------|
| `TaoForge: Create Project` | `Ctrl+Shift+T` | Create a new project with guided wizard |
| `TaoForge: Quick Create` | `Ctrl+Alt+Q` | Create project from template |
| `TaoForge: Open Project` | `Ctrl+Alt+O` | Open existing TaoForge project |
| `TaoForge: Clone Project` | - | Clone project from repository |
| `TaoForge: Import Project` | - | Import existing project into TaoForge |

### Monitoring & Debugging

| Command | Shortcut | Description |
|---------|----------|-------------|
| `TaoForge: Show Agent Panel` | `Ctrl+Alt+A` | Open agent monitoring dashboard |
| `TaoForge: Show Pheromone Trail` | `Ctrl+Alt+P` | Visualize agent communication |
| `TaoForge: Show Project Status` | `Ctrl+Alt+S` | Display current project status |
| `TaoForge: Show Logs` | `Ctrl+Alt+L` | View system and project logs |
| `TaoForge: Show Metrics` | - | Display performance metrics |

### Development Tools

| Command | Shortcut | Description |
|---------|----------|-------------|
| `TaoForge: Enhance Code` | `Ctrl+Alt+E` | AI-enhance selected code |
| `TaoForge: Generate Tests` | `Ctrl+Alt+T` | Generate tests for current file |
| `TaoForge: Add Feature` | `Ctrl+Alt+F` | Add new feature to project |
| `TaoForge: Refactor Code` | `Ctrl+Alt+R` | AI-powered code refactoring |
| `TaoForge: Generate Documentation` | `Ctrl+Alt+D` | Generate code documentation |

### System Management

| Command | Shortcut | Description |
|---------|----------|-------------|
| `TaoForge: Check Connection` | - | Verify API connection |
| `TaoForge: Restart Orchestrator` | - | Restart the orchestrator service |
| `TaoForge: Update Configuration` | - | Update extension configuration |
| `TaoForge: Show System Status` | - | Display system health status |
| `TaoForge: Clear Cache` | - | Clear extension cache |

## 🎨 User Interface

### 1. Activity Bar

The TaoForge icon in the Activity Bar provides quick access to:

- **Project Explorer** - Browse TaoForge projects
- **Agent Monitor** - Real-time agent status
- **Pheromone Viewer** - Communication visualization
- **System Status** - Health and performance metrics

### 2. Side Panel

#### Project Explorer
```
📁 TaoForge Projects
├── 🚀 Active Projects
│   ├── 📊 TaskMaster (85% complete)
│   └── 🛒 EcommerceHub (in progress)
├── ✅ Completed Projects
│   ├── 📝 BlogApp
│   └── 🔐 AuthService
└── 📋 Templates
    ├── React App
    ├── Express API
    └── Full Stack
```

#### Agent Monitor
```
🤖 Agent Status
├── 📋 Analyst
│   ├── Status: Active
│   ├── Task: Requirements analysis
│   └── Progress: 75%
├── 🏗️ Architect
│   ├── Status: Waiting
│   └── Queue: 1 task
├── 💻 Developer
│   ├── Status: Active
│   ├── Task: Component implementation
│   └── Progress: 60%
└── 🧪 QA
    ├── Status: Idle
    └── Next: Test generation
```

### 3. Status Bar

The status bar shows:
- **Connection Status:** 🟢 Connected / 🔴 Disconnected
- **Active Projects:** Number of projects in progress
- **System Load:** Current system utilization

### 4. Webview Panels

#### Project Creation Wizard
Interactive form with:
- Project description input
- Type selection (fullstack, api, mobile, etc.)
- Technology preferences
- Feature selection
- Advanced configuration options

#### Agent Dashboard
Real-time monitoring with:
- Agent activity timeline
- Progress indicators
- Task queues
- Performance metrics
- Error notifications

#### Pheromone Visualization
Interactive visualization showing:
- Agent communication patterns
- Signal strength over time
- Project coordination flow
- Bottleneck identification

## ⚙️ Configuration

### Extension Settings

Access via File → Preferences → Settings → Extensions → TaoForge

#### Connection Settings

```json
{
  "taoforge.orchestratorUrl": "http://localhost:8000",
  "taoforge.apiKey": "your-api-key-here",
  "taoforge.timeout": 30000,
  "taoforge.retryAttempts": 3
}
```

#### UI Settings

```json
{
  "taoforge.autoOpenAgentPanel": true,
  "taoforge.showProgressNotifications": true,
  "taoforge.enablePheromoneVisualization": true,
  "taoforge.refreshInterval": 5000
}
```

#### Development Settings

```json
{
  "taoforge.autoSave": true,
  "taoforge.autoFormat": true,
  "taoforge.enableCodeLens": true,
  "taoforge.showInlineHints": true
}
```

#### Notification Settings

```json
{
  "taoforge.notifications.projectComplete": true,
  "taoforge.notifications.agentErrors": true,
  "taoforge.notifications.systemAlerts": true,
  "taoforge.notifications.sound": false
}
```

### Workspace Settings

Project-specific settings in `.vscode/settings.json`:

```json
{
  "taoforge.project.id": "proj_abc123",
  "taoforge.project.type": "fullstack",
  "taoforge.project.workflow": "enterprise",
  "taoforge.monitoring.enabled": true,
  "taoforge.autoEnhance.enabled": false
}
```

## 🔧 Advanced Features

### 1. Code Enhancement

Select code and use `Ctrl+Alt+E` to enhance it:

```javascript
// Before enhancement
function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price;
  }
  return total;
}

// After AI enhancement
/**
 * Calculates the total price of items with error handling and validation
 * @param {Array<{price: number}>} items - Array of items with price property
 * @returns {number} Total price of all items
 * @throws {Error} If items is not an array or contains invalid price values
 */
function calculateTotal(items) {
  if (!Array.isArray(items)) {
    throw new Error('Items must be an array');
  }
  
  return items.reduce((total, item) => {
    if (typeof item.price !== 'number' || item.price < 0) {
      throw new Error(`Invalid price: ${item.price}`);
    }
    return total + item.price;
  }, 0);
}
```

### 2. Test Generation

Generate comprehensive tests for your code:

```javascript
// Original function
function validateEmail(email) {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

// Generated tests
describe('validateEmail', () => {
  test('should return true for valid email addresses', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  test('should return false for invalid email addresses', () => {
    expect(validateEmail('invalid-email')).toBe(false);
    expect(validateEmail('@domain.com')).toBe(false);
    expect(validateEmail('user@')).toBe(false);
  });

  test('should handle edge cases', () => {
    expect(validateEmail('')).toBe(false);
    expect(validateEmail(null)).toBe(false);
    expect(validateEmail(undefined)).toBe(false);
  });
});
```

### 3. Feature Addition

Add new features to existing projects:

1. Select the area where you want to add a feature
2. Run `TaoForge: Add Feature`
3. Describe the feature in natural language
4. TaoForge generates and integrates the code

### 4. Code Lens Integration

Inline actions appear above functions and classes:

```javascript
// 🔧 Enhance | 🧪 Generate Tests | 📖 Generate Docs
function processPayment(amount, paymentMethod) {
  // Implementation
}
```

## 🔍 Troubleshooting

### Common Issues

#### Connection Problems

**Issue:** "Cannot connect to TaoForge orchestrator"

**Solutions:**
1. Check if the orchestrator is running: `taoforge status`
2. Verify the URL in settings: `taoforge.orchestratorUrl`
3. Check firewall and network settings
4. Restart VS Code and try again

#### Extension Not Loading

**Issue:** Extension appears inactive or commands not available

**Solutions:**
1. Reload VS Code: `Ctrl+Shift+P` → "Developer: Reload Window"
2. Check extension is enabled: Extensions → TaoForge → Enable
3. Update to latest version
4. Check VS Code version compatibility

#### Performance Issues

**Issue:** VS Code becomes slow with TaoForge extension

**Solutions:**
1. Increase refresh interval: `taoforge.refreshInterval`
2. Disable auto-refresh: `taoforge.autoRefresh: false`
3. Reduce monitoring scope
4. Clear extension cache: `TaoForge: Clear Cache`

### Debug Mode

Enable debug mode for detailed logging:

1. Open VS Code settings
2. Set `taoforge.debug: true`
3. Open Output panel (View → Output)
4. Select "TaoForge" from dropdown
5. Monitor debug logs

### Log Files

Extension logs are stored in:
- **Windows:** `%APPDATA%\Code\logs\taoforge\`
- **macOS:** `~/Library/Application Support/Code/logs/taoforge/`
- **Linux:** `~/.config/Code/logs/taoforge/`

## 📚 Additional Resources

### Tutorials
- **[Getting Started with VS Code Extension](../tutorials/vscode-extension-tutorial.md)**
- **[Advanced Workflow Integration](../tutorials/vscode-advanced.md)**
- **[Custom Configuration Guide](../tutorials/vscode-configuration.md)**

### Reference
- **[Command Reference](commands.md)** - Complete command documentation
- **[Configuration Reference](configuration.md)** - All configuration options
- **[API Integration](api-integration.md)** - Extending the extension

### Support
- **[FAQ](../support/faq.md#vs-code-extension)** - Common questions
- **[GitHub Issues](https://github.com/taoforge/vscode-extension/issues)** - Report bugs
- **[Community Forum](../support/community.md)** - Get help from users

## 🎉 Tips and Tricks

### Productivity Tips

1. **Use Keyboard Shortcuts:** Learn the main shortcuts for faster workflow
2. **Customize Workspace:** Set up project-specific configurations
3. **Monitor in Background:** Keep agent panel open while coding
4. **Use Templates:** Create custom templates for common project types
5. **Batch Operations:** Process multiple files with bulk commands

### Best Practices

1. **Regular Monitoring:** Check agent status periodically
2. **Clear Descriptions:** Provide detailed feature descriptions
3. **Incremental Development:** Add features gradually
4. **Code Review:** Always review AI-generated code
5. **Backup Projects:** Keep backups of important projects

The TaoForge VS Code extension transforms your development experience by bringing autonomous AI development directly into your editor. Start creating amazing projects with just a few clicks!
