/**
 * VS Code UI Guidelines Compliance Checker
 * Ensures components follow VS Code design system standards
 */

export interface UIGuidelineRule {
  id: string;
  name: string;
  description: string;
  category: 'color' | 'typography' | 'spacing' | 'icons' | 'layout' | 'interaction';
  severity: 'error' | 'warning' | 'info';
  check: (element: Element) => boolean;
  message: string;
}

export interface ComplianceResult {
  rule: UIGuidelineRule;
  element: Element;
  passed: boolean;
  message: string;
}

// VS Code Color Tokens
export const VSCodeColors = {
  // Foreground colors
  foreground: 'var(--vscode-foreground)',
  disabledForeground: 'var(--vscode-disabledForeground)',
  errorForeground: 'var(--vscode-errorForeground)',
  descriptionForeground: 'var(--vscode-descriptionForeground)',
  
  // Background colors
  background: 'var(--vscode-editor-background)',
  sidebarBackground: 'var(--vscode-sideBar-background)',
  activityBarBackground: 'var(--vscode-activityBar-background)',
  
  // Button colors
  buttonBackground: 'var(--vscode-button-background)',
  buttonForeground: 'var(--vscode-button-foreground)',
  buttonHoverBackground: 'var(--vscode-button-hoverBackground)',
  buttonSecondaryBackground: 'var(--vscode-button-secondaryBackground)',
  buttonSecondaryForeground: 'var(--vscode-button-secondaryForeground)',
  
  // Input colors
  inputBackground: 'var(--vscode-input-background)',
  inputForeground: 'var(--vscode-input-foreground)',
  inputBorder: 'var(--vscode-input-border)',
  inputPlaceholderForeground: 'var(--vscode-input-placeholderForeground)',
  
  // Focus colors
  focusBorder: 'var(--vscode-focusBorder)',
  
  // List colors
  listActiveSelectionBackground: 'var(--vscode-list-activeSelectionBackground)',
  listActiveSelectionForeground: 'var(--vscode-list-activeSelectionForeground)',
  listHoverBackground: 'var(--vscode-list-hoverBackground)',
  
  // Badge colors
  badgeBackground: 'var(--vscode-badge-background)',
  badgeForeground: 'var(--vscode-badge-foreground)',
  
  // Progress bar
  progressBarBackground: 'var(--vscode-progressBar-background)',
  
  // Notification colors
  notificationInfoBackground: 'var(--vscode-notificationCenterHeader-background)',
  notificationWarningBackground: 'var(--vscode-inputValidation-warningBackground)',
  notificationErrorBackground: 'var(--vscode-inputValidation-errorBackground)',
};

// VS Code Typography
export const VSCodeTypography = {
  fontFamily: 'var(--vscode-font-family)',
  fontSize: 'var(--vscode-font-size)',
  fontWeight: 'var(--vscode-font-weight)',
  lineHeight: '1.4',
  
  // Specific font sizes
  smallFontSize: '11px',
  normalFontSize: '13px',
  largeFontSize: '16px',
};

// VS Code Spacing
export const VSCodeSpacing = {
  xs: '2px',
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  xxl: '24px',
  
  // Component specific
  buttonPadding: '4px 14px',
  inputPadding: '4px 6px',
  listItemPadding: '0 8px',
};

// UI Guidelines Rules
export const uiGuidelineRules: UIGuidelineRule[] = [
  // Color Rules
  {
    id: 'use-vscode-colors',
    name: 'Use VS Code Color Tokens',
    description: 'Components should use VS Code CSS variables for colors',
    category: 'color',
    severity: 'error',
    check: (element) => {
      const computedStyle = window.getComputedStyle(element);
      const backgroundColor = computedStyle.backgroundColor;
      const color = computedStyle.color;
      
      // Check if using hardcoded colors instead of CSS variables
      const hasHardcodedColors = backgroundColor.includes('rgb(') || color.includes('rgb(');
      return !hasHardcodedColors;
    },
    message: 'Use VS Code CSS variables instead of hardcoded colors'
  },
  
  {
    id: 'sufficient-contrast',
    name: 'Sufficient Color Contrast',
    description: 'Text should have sufficient contrast ratio (4.5:1 for normal text)',
    category: 'color',
    severity: 'error',
    check: (element) => {
      // This would need a proper contrast calculation implementation
      // For now, we'll assume it passes if using VS Code colors
      const computedStyle = window.getComputedStyle(element);
      const color = computedStyle.color;
      return color.includes('var(--vscode-');
    },
    message: 'Ensure sufficient color contrast for accessibility'
  },
  
  // Typography Rules
  {
    id: 'use-vscode-fonts',
    name: 'Use VS Code Font Family',
    description: 'Components should use VS Code font family',
    category: 'typography',
    severity: 'warning',
    check: (element) => {
      const computedStyle = window.getComputedStyle(element);
      const fontFamily = computedStyle.fontFamily;
      return fontFamily.includes('var(--vscode-font-family)') || 
             fontFamily.includes('Segoe UI') || 
             fontFamily.includes('system-ui');
    },
    message: 'Use VS Code font family for consistency'
  },
  
  {
    id: 'appropriate-font-size',
    name: 'Appropriate Font Size',
    description: 'Font sizes should be appropriate for VS Code',
    category: 'typography',
    severity: 'warning',
    check: (element) => {
      const computedStyle = window.getComputedStyle(element);
      const fontSize = parseInt(computedStyle.fontSize);
      return fontSize >= 11 && fontSize <= 20;
    },
    message: 'Font size should be between 11px and 20px'
  },
  
  // Spacing Rules
  {
    id: 'consistent-spacing',
    name: 'Consistent Spacing',
    description: 'Use consistent spacing values',
    category: 'spacing',
    severity: 'info',
    check: (element) => {
      const computedStyle = window.getComputedStyle(element);
      const padding = computedStyle.padding;
      const margin = computedStyle.margin;
      
      // Check if using standard spacing values
      const standardValues = ['2px', '4px', '8px', '12px', '16px', '24px'];
      const paddingValues = padding.split(' ');
      const marginValues = margin.split(' ');
      
      const allValues = [...paddingValues, ...marginValues];
      return allValues.every(value => 
        value === '0px' || standardValues.includes(value) || value.includes('var(')
      );
    },
    message: 'Use standard spacing values (2px, 4px, 8px, 12px, 16px, 24px)'
  },
  
  // Icon Rules
  {
    id: 'appropriate-icon-size',
    name: 'Appropriate Icon Size',
    description: 'Icons should be appropriately sized',
    category: 'icons',
    severity: 'warning',
    check: (element) => {
      if (!element.querySelector('svg')) return true;
      
      const svg = element.querySelector('svg');
      if (!svg) return true;
      
      const width = svg.getAttribute('width') || svg.style.width;
      const height = svg.getAttribute('height') || svg.style.height;
      
      const size = parseInt(width || '16');
      return size >= 12 && size <= 24;
    },
    message: 'Icon size should be between 12px and 24px'
  },
  
  // Layout Rules
  {
    id: 'proper-focus-indicators',
    name: 'Proper Focus Indicators',
    description: 'Interactive elements should have visible focus indicators',
    category: 'interaction',
    severity: 'error',
    check: (element) => {
      const tagName = element.tagName.toLowerCase();
      const isInteractive = ['button', 'input', 'select', 'textarea', 'a'].includes(tagName) ||
                           element.hasAttribute('tabindex') ||
                           element.hasAttribute('role');
      
      if (!isInteractive) return true;
      
      // Check if element has focus styles
      const computedStyle = window.getComputedStyle(element, ':focus');
      const outline = computedStyle.outline;
      const boxShadow = computedStyle.boxShadow;
      
      return outline !== 'none' || boxShadow !== 'none';
    },
    message: 'Interactive elements must have visible focus indicators'
  },
  
  {
    id: 'accessible-button-size',
    name: 'Accessible Button Size',
    description: 'Buttons should be at least 24px in height for accessibility',
    category: 'interaction',
    severity: 'warning',
    check: (element) => {
      if (element.tagName.toLowerCase() !== 'button') return true;
      
      const computedStyle = window.getComputedStyle(element);
      const height = parseInt(computedStyle.height);
      const paddingTop = parseInt(computedStyle.paddingTop);
      const paddingBottom = parseInt(computedStyle.paddingBottom);
      
      const totalHeight = height || (paddingTop + paddingBottom + 16); // Assume 16px for text
      return totalHeight >= 24;
    },
    message: 'Buttons should be at least 24px in height'
  }
];

/**
 * Check UI compliance for a given element and its children
 */
export function checkUICompliance(rootElement: Element = document.body): ComplianceResult[] {
  const results: ComplianceResult[] = [];
  const elements = [rootElement, ...Array.from(rootElement.querySelectorAll('*'))];
  
  elements.forEach(element => {
    uiGuidelineRules.forEach(rule => {
      try {
        const passed = rule.check(element);
        results.push({
          rule,
          element,
          passed,
          message: passed ? 'Compliant' : rule.message
        });
      } catch (error) {
        results.push({
          rule,
          element,
          passed: false,
          message: `Error checking rule: ${error}`
        });
      }
    });
  });
  
  return results;
}

/**
 * Get compliance summary
 */
export function getComplianceSummary(results: ComplianceResult[]) {
  const total = results.length;
  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;
  
  const byCategory = results.reduce((acc, result) => {
    const category = result.rule.category;
    if (!acc[category]) {
      acc[category] = { total: 0, passed: 0, failed: 0 };
    }
    acc[category].total++;
    if (result.passed) {
      acc[category].passed++;
    } else {
      acc[category].failed++;
    }
    return acc;
  }, {} as Record<string, { total: number; passed: number; failed: number }>);
  
  const bySeverity = results.reduce((acc, result) => {
    if (!result.passed) {
      const severity = result.rule.severity;
      acc[severity] = (acc[severity] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);
  
  return {
    total,
    passed,
    failed,
    passRate: (passed / total) * 100,
    byCategory,
    bySeverity
  };
}

/**
 * Apply VS Code theme styles to an element
 */
export function applyVSCodeStyles(element: HTMLElement) {
  // Apply VS Code CSS variables
  element.style.setProperty('--vscode-font-family', VSCodeTypography.fontFamily);
  element.style.setProperty('--vscode-font-size', VSCodeTypography.fontSize);
  element.style.setProperty('--vscode-font-weight', VSCodeTypography.fontWeight);
  
  // Add VS Code theme class
  element.classList.add('vscode-theme');
}

/**
 * Generate CSS for VS Code compliance
 */
export function generateVSCodeCSS(): string {
  return `
    .vscode-theme {
      font-family: ${VSCodeTypography.fontFamily};
      font-size: ${VSCodeTypography.fontSize};
      font-weight: ${VSCodeTypography.fontWeight};
      line-height: ${VSCodeTypography.lineHeight};
      color: ${VSCodeColors.foreground};
      background-color: ${VSCodeColors.background};
    }
    
    .vscode-theme button {
      background-color: ${VSCodeColors.buttonBackground};
      color: ${VSCodeColors.buttonForeground};
      border: 1px solid transparent;
      padding: ${VSCodeSpacing.buttonPadding};
      border-radius: 2px;
      font-family: inherit;
      font-size: inherit;
      cursor: pointer;
    }
    
    .vscode-theme button:hover {
      background-color: ${VSCodeColors.buttonHoverBackground};
    }
    
    .vscode-theme button:focus {
      outline: 1px solid ${VSCodeColors.focusBorder};
      outline-offset: 2px;
    }
    
    .vscode-theme input, .vscode-theme textarea, .vscode-theme select {
      background-color: ${VSCodeColors.inputBackground};
      color: ${VSCodeColors.inputForeground};
      border: 1px solid ${VSCodeColors.inputBorder};
      padding: ${VSCodeSpacing.inputPadding};
      border-radius: 2px;
      font-family: inherit;
      font-size: inherit;
    }
    
    .vscode-theme input:focus, .vscode-theme textarea:focus, .vscode-theme select:focus {
      outline: 1px solid ${VSCodeColors.focusBorder};
      outline-offset: -1px;
    }
    
    .vscode-theme input::placeholder {
      color: ${VSCodeColors.inputPlaceholderForeground};
    }
    
    .vscode-theme .progress-bar {
      background-color: ${VSCodeColors.progressBarBackground};
      height: 2px;
      border-radius: 1px;
    }
    
    .vscode-theme .badge {
      background-color: ${VSCodeColors.badgeBackground};
      color: ${VSCodeColors.badgeForeground};
      padding: 2px 6px;
      border-radius: 11px;
      font-size: 11px;
      font-weight: 600;
    }
  `;
}
