# TaoForge Comprehensive Test Suite

This directory contains a comprehensive test suite for the TaoForge autonomous AI software creation system. The test suite covers all major components and provides multiple levels of testing from unit tests to end-to-end workflows.

## Test Structure

### Test Categories

1. **Unit Tests** - Test individual components in isolation
2. **Integration Tests** - Test component interactions and data flow
3. **End-to-End Tests** - Test complete workflows from start to finish
4. **Performance Tests** - Test system performance under load
5. **VS Code Extension Tests** - Test extension functionality and UI

### Test Files

#### Core Component Tests
- `test_orchestrator.py` - Orchestrator API and coordination logic
- `test_pheromone_system.py` - Pheromone-based agent communication
- `test_workflow_engine.py` - BMAD methodology and workflow execution
- `test_agent_executors_comprehensive.py` - All BMAD agents (Analyst, Architect, Developer, QA)

#### Integration Tests
- `test_integration_comprehensive.py` - Component interaction testing
- `test_complete_integration.py` - Full system integration scenarios

#### End-to-End Tests
- `test_end_to_end.py` - Complete project generation workflows

#### Performance Tests
- `test_performance.py` - Load testing and performance benchmarks

#### Extension Tests
- `test_vscode_extension.py` - VS Code extension functionality

#### Utilities
- `test_utilities.py` - Test utilities and shared fixtures
- `conftest.py` - Pytest configuration and global fixtures

## Running Tests

### Quick Start

```bash
# Run all tests
python run_tests.py --all

# Run only unit tests
python run_tests.py --unit

# Run with coverage
python run_tests.py --unit --coverage

# Run specific component tests
python run_tests.py --component orchestrator
python run_tests.py --component pheromone
python run_tests.py --component workflow
python run_tests.py --component agent
python run_tests.py --component vscode
```

### Test Categories

```bash
# Unit tests only
python run_tests.py --unit

# Integration tests
python run_tests.py --integration

# Performance tests
python run_tests.py --performance

# End-to-end tests
python run_tests.py --e2e

# Fast tests (skip slow/performance tests)
python run_tests.py --fast
```

### Advanced Options

```bash
# Verbose output
python run_tests.py --unit --verbose

# Generate comprehensive report
python run_tests.py --report

# Run in Docker
python run_tests.py --docker

# Code quality checks
python run_tests.py --lint --type-check

# Install test dependencies
python run_tests.py --install-deps
```

### Using Pytest Directly

```bash
# Run all tests with pytest
pytest

# Run specific test file
pytest tests/test_orchestrator.py

# Run tests with markers
pytest -m "unit"
pytest -m "integration"
pytest -m "performance"
pytest -m "e2e"

# Run tests with coverage
pytest --cov=src --cov-report=html

# Run tests in parallel
pytest -n auto
```

## Test Markers

Tests are categorized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.e2e` - End-to-end tests
- `@pytest.mark.performance` - Performance tests
- `@pytest.mark.slow` - Tests that take a long time
- `@pytest.mark.api` - Tests requiring API access
- `@pytest.mark.vscode` - VS Code extension tests

## Test Configuration

### Environment Variables

```bash
# Test environment
export AETHERFORGE_ENV=test
export PROJECTS_DIR=/tmp/test_projects
export PHEROMONE_FILE=/tmp/test_pheromones.json

# API configuration
export ORCHESTRATOR_URL=http://localhost:8000
export OPENAI_API_KEY=your_test_key

# Logging
export LOG_LEVEL=debug
```

### Configuration Files

- `pytest.ini` - Pytest configuration
- `conftest.py` - Global fixtures and setup
- `.coveragerc` - Coverage configuration

## Test Data and Fixtures

### Shared Fixtures

The test suite provides several shared fixtures:

- `test_env` - Test environment manager
- `temp_workspace` - Temporary workspace directory
- `sample_project` - Sample project structure
- `mock_openai_api` - Mocked OpenAI API calls
- `mock_file_generation` - Mocked file generation

### Test Data Factory

Use `TestDataFactory` to create consistent test data:

```python
from tests.test_utilities import TestDataFactory

# Create project metadata
metadata = TestDataFactory.create_project_metadata("TestApp", "fullstack")

# Create pheromone data
pheromone = TestDataFactory.create_pheromone_data("test_signal", "project_123")

# Create agent context
context = TestDataFactory.create_agent_context("Create a web app", "/tmp/test")
```

## Writing New Tests

### Test Structure

```python
import pytest
from tests.test_utilities import TestDataFactory

class TestNewComponent:
    """Test the new component"""
    
    @pytest.fixture
    def component_instance(self, test_env):
        """Create component instance for testing"""
        return NewComponent()
    
    @pytest.mark.unit
    def test_component_initialization(self, component_instance):
        """Test component initialization"""
        assert component_instance is not None
        assert component_instance.status == "initialized"
    
    @pytest.mark.integration
    async def test_component_integration(self, component_instance, mock_openai_api):
        """Test component integration"""
        result = await component_instance.process_request("test input")
        assert result["success"] is True
```

### Best Practices

1. **Use descriptive test names** - Test names should clearly describe what is being tested
2. **Use appropriate markers** - Mark tests with appropriate categories
3. **Mock external dependencies** - Use mocks for API calls, file system operations, etc.
4. **Clean up resources** - Use fixtures and context managers for cleanup
5. **Test both success and failure cases** - Include error handling tests
6. **Use test data factories** - Create consistent test data using factories
7. **Keep tests independent** - Tests should not depend on each other

### Mocking Guidelines

```python
# Mock external API calls
with patch('src.agent_executors.call_openai_api') as mock_api:
    mock_api.return_value = "Mock response"
    result = await agent.execute(context)

# Mock file operations
with patch('pathlib.Path.write_text') as mock_write:
    component.save_file("test.txt", "content")
    mock_write.assert_called_once()

# Mock HTTP requests
with patch('requests.post') as mock_post:
    mock_post.return_value.status_code = 200
    mock_post.return_value.json.return_value = {"status": "success"}
```

## Continuous Integration

### GitHub Actions

The test suite is designed to work with CI/CD pipelines:

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: python run_tests.py --install-deps
      - name: Run tests
        run: python run_tests.py --all --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### Docker Testing

```bash
# Build test image
docker build -f Dockerfile.test -t taoforge-test .

# Run tests in container
docker run --rm taoforge-test
```

## Coverage Reports

Coverage reports are generated in multiple formats:

- **HTML Report** - `htmlcov/index.html`
- **Terminal Report** - Displayed during test run
- **XML Report** - `coverage.xml` for CI integration

### Coverage Targets

- **Overall Coverage**: 80% minimum
- **Critical Components**: 90% minimum
- **New Code**: 95% minimum

## Performance Benchmarks

Performance tests establish benchmarks for:

- **Pheromone Operations**: < 10ms per operation
- **API Response Times**: < 100ms average
- **Project Generation**: < 10 seconds for simple projects
- **Memory Usage**: < 100MB increase under load

## Troubleshooting

### Common Issues

1. **Import Errors** - Ensure `src` directory is in Python path
2. **Mock Failures** - Check mock patch paths and return values
3. **Timeout Errors** - Increase timeout for slow tests
4. **File Permission Errors** - Ensure test has write permissions

### Debug Mode

```bash
# Run with debug output
pytest -v -s --log-cli-level=DEBUG

# Run single test with debugging
pytest tests/test_orchestrator.py::TestOrchestratorAPI::test_health_endpoint -v -s
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Add appropriate markers and documentation
3. Include both positive and negative test cases
4. Update this README if adding new test categories
5. Ensure tests pass in CI environment

For questions or issues with the test suite, please refer to the main project documentation or create an issue in the repository.
