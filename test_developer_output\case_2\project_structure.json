{"project_name": "Task Management API", "project_type": "api", "root_path": "test_developer_output\\case_2", "files": {"source": [{"path": "src/app.js", "language": "javascript", "type": "source"}, {"path": "src/server.js", "language": "javascript", "type": "source"}, {"path": "src/config/index.js", "language": "javascript", "type": "source"}, {"path": "src/controllers/index.js", "language": "javascript", "type": "source"}, {"path": "src/services/index.js", "language": "javascript", "type": "source"}, {"path": "src/models/index.js", "language": "javascript", "type": "source"}, {"path": "src/middleware/index.js", "language": "javascript", "type": "source"}, {"path": "src/routes/index.js", "language": "javascript", "type": "source"}, {"path": "src/utils/index.js", "language": "javascript", "type": "source"}, {"path": "src/validation/index.js", "language": "javascript", "type": "source"}], "test": [{"path": "tests/app.js", "language": "javascript", "type": "test"}, {"path": "tests/server.js", "language": "javascript", "type": "test"}, {"path": "tests/config/index.js", "language": "javascript", "type": "test"}, {"path": "tests/controllers/index.js", "language": "javascript", "type": "test"}, {"path": "tests/services/index.js", "language": "javascript", "type": "test"}, {"path": "tests/models/index.js", "language": "javascript", "type": "test"}, {"path": "tests/middleware/index.js", "language": "javascript", "type": "test"}, {"path": "tests/routes/index.js", "language": "javascript", "type": "test"}, {"path": "tests/utils/index.js", "language": "javascript", "type": "test"}, {"path": "tests/validation/index.js", "language": "javascript", "type": "test"}, {"path": "tests/integration/api.integration.test.ts", "language": "typescript", "type": "test"}, {"path": "tests/integration/database.integration.test.ts", "language": "typescript", "type": "test"}], "config": [{"path": "package.json", "language": "json", "type": "config"}, {"path": ".giti<PERSON>re", "language": "text", "type": "config"}, {"path": ".env.example", "language": "text", "type": "config"}, {"path": "jest.config.js", "language": "javascript", "type": "config"}], "documentation": [{"path": "README.md", "language": "markdown", "type": "documentation"}, {"path": "docs/API.md", "language": "markdown", "type": "documentation"}, {"path": "docs/DEVELOPMENT.md", "language": "markdown", "type": "documentation"}, {"path": "docs/DEPLOYMENT.md", "language": "markdown", "type": "documentation"}]}, "dependencies": {}, "scripts": {}, "environment_variables": {}}