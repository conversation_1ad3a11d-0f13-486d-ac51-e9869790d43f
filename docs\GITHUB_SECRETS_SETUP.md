# GitHub Secrets Configuration for TaoForge CI/CD

This document describes the required GitHub secrets for the TaoForge CI/CD pipeline.

## Required Secrets

### Core Application Secrets

#### `OPENAI_API_KEY`
- **Description**: OpenAI API key for AI functionality
- **Environment**: All (staging, production)
- **Format**: `sk-...` (OpenAI API key format)
- **How to get**: 
  1. Go to https://platform.openai.com/api-keys
  2. Create a new API key
  3. Copy the key (starts with `sk-`)

#### `JWT_SECRET`
- **Description**: Secret key for JWT token signing
- **Environment**: All (staging, production)
- **Format**: Base64 encoded string (64+ characters)
- **How to generate**:
  ```bash
  python -c "import secrets; print(secrets.token_urlsafe(64))"
  ```

### Database Secrets

#### `STAGING_DATABASE_URL`
- **Description**: PostgreSQL connection URL for staging
- **Environment**: Staging
- **Format**: `postgresql://username:password@host:port/database`
- **Example**: `postgresql://aetherforge:<EMAIL>:5432/aetherforge_staging`

#### `PRODUCTION_DATABASE_URL`
- **Description**: PostgreSQL connection URL for production
- **Environment**: Production
- **Format**: `postgresql://username:password@host:port/database`
- **Example**: `postgresql://aetherforge:<EMAIL>:5432/aetherforge_production`

#### `STAGING_POSTGRES_PASSWORD`
- **Description**: PostgreSQL password for staging environment
- **Environment**: Staging
- **Format**: Strong password (16+ characters)
- **How to generate**:
  ```bash
  python -c "import secrets, string; print(''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$%^&*') for _ in range(32)))"
  ```

#### `PRODUCTION_POSTGRES_PASSWORD`
- **Description**: PostgreSQL password for production environment
- **Environment**: Production
- **Format**: Strong password (16+ characters)
- **Security**: Use different password than staging

### Redis Secrets

#### `STAGING_REDIS_URL`
- **Description**: Redis connection URL for staging
- **Environment**: Staging
- **Format**: `redis://[username:password@]host:port[/database]`
- **Example**: `redis://staging-redis.example.com:6379/0`

#### `PRODUCTION_REDIS_URL`
- **Description**: Redis connection URL for production
- **Environment**: Production
- **Format**: `redis://[username:password@]host:port[/database]`
- **Example**: `redis://prod-redis.example.com:6379/0`

### Notification Secrets

#### `DEPLOYMENT_WEBHOOK_URL`
- **Description**: Webhook URL for deployment notifications (Slack/Teams/Discord)
- **Environment**: Production
- **Format**: HTTPS webhook URL
- **How to get**:
  - **Slack**: Create incoming webhook in Slack app settings
  - **Teams**: Create incoming webhook in Teams channel
  - **Discord**: Create webhook in Discord channel settings

#### `ALERT_WEBHOOK_URL`
- **Description**: Webhook URL for critical alerts and failures
- **Environment**: Production
- **Format**: HTTPS webhook URL
- **Note**: Can be same as deployment webhook or separate for different channels

#### `SLACK_WEBHOOK` (Optional)
- **Description**: Legacy Slack webhook for notifications
- **Environment**: All
- **Format**: HTTPS webhook URL starting with `https://hooks.slack.com/`

## Setting Up GitHub Secrets

### 1. Navigate to Repository Settings
1. Go to your GitHub repository
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**

### 2. Add Repository Secrets
For each secret listed above:
1. Click **New repository secret**
2. Enter the **Name** (exactly as shown above)
3. Enter the **Value**
4. Click **Add secret**

### 3. Environment-Specific Secrets
For staging and production environments:
1. Go to **Settings** → **Environments**
2. Create environments: `staging` and `production`
3. Add environment-specific secrets to each environment

## Security Best Practices

### Secret Management
- **Never commit secrets** to the repository
- **Use different secrets** for staging and production
- **Rotate secrets regularly** (every 90 days recommended)
- **Use strong, randomly generated passwords**

### Access Control
- **Limit repository access** to necessary team members
- **Use environment protection rules** for production
- **Require reviews** for production deployments
- **Enable branch protection** on main branch

### Monitoring
- **Monitor secret usage** in GitHub Actions logs
- **Set up alerts** for failed deployments
- **Review access logs** regularly

## Validation Script

Use this script to validate your secrets are properly configured:

```bash
# Run from repository root
python scripts/validate_github_secrets.py
```

## Troubleshooting

### Common Issues

#### Secret Not Found
- **Error**: `Context access might be invalid: SECRET_NAME`
- **Solution**: Ensure secret is added to correct environment (repository/staging/production)

#### Invalid Database URL
- **Error**: Connection failed during deployment
- **Solution**: Verify database URL format and credentials

#### Webhook Failures
- **Error**: Notification not received
- **Solution**: Test webhook URL manually with curl

### Testing Secrets
```bash
# Test database connection
psql "$STAGING_DATABASE_URL" -c "SELECT 1;"

# Test Redis connection
redis-cli -u "$STAGING_REDIS_URL" ping

# Test webhook
curl -X POST "$DEPLOYMENT_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{"text":"Test notification"}'
```

## Environment Setup Checklist

### Staging Environment
- [ ] `STAGING_DATABASE_URL` - PostgreSQL connection
- [ ] `STAGING_REDIS_URL` - Redis connection
- [ ] `STAGING_POSTGRES_PASSWORD` - Database password
- [ ] `OPENAI_API_KEY` - OpenAI API access
- [ ] `JWT_SECRET` - JWT signing key

### Production Environment
- [ ] `PRODUCTION_DATABASE_URL` - PostgreSQL connection
- [ ] `PRODUCTION_REDIS_URL` - Redis connection
- [ ] `PRODUCTION_POSTGRES_PASSWORD` - Database password
- [ ] `OPENAI_API_KEY` - OpenAI API access
- [ ] `JWT_SECRET` - JWT signing key
- [ ] `DEPLOYMENT_WEBHOOK_URL` - Success notifications
- [ ] `ALERT_WEBHOOK_URL` - Failure alerts

### Repository Level
- [ ] `GITHUB_TOKEN` - Automatically provided by GitHub
- [ ] `CODECOV_TOKEN` - For code coverage reporting (optional)

## Next Steps

After configuring all secrets:

1. **Test the CI/CD pipeline** by creating a pull request
2. **Verify staging deployment** by pushing to `develop` branch
3. **Test production deployment** by creating a release
4. **Monitor deployment notifications** in your configured channels
5. **Review and update secrets** as needed

For additional help, see:
- [GitHub Secrets Documentation](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [TaoForge Deployment Guide](./PRODUCTION_DEPLOYMENT_CHECKLIST.md)
- [Environment Validation Script](../scripts/validate_environment.py)
