"""
Comprehensive tests for the project generator standalone module.
Tests project generation, file creation, and API integration.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Import the project generator
import sys
sys.path.insert(0, 'src')

from project_generator_standalone import ProjectGenerator

class TestProjectGenerator:
    """Test the standalone project generator"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing"""
        temp_path = tempfile.mkdtemp()
        yield Path(temp_path)
        shutil.rmtree(temp_path, ignore_errors=True)
    
    def test_project_generator_initialization(self):
        """Test project generator initialization"""
        generator = ProjectGenerator()
        
        # Should initialize without errors
        assert generator is not None
        # API manager might be None if not configured
        assert hasattr(generator, 'api_manager')
    
    @pytest.mark.asyncio
    async def test_generate_project_basic(self, temp_dir):
        """Test basic project generation"""
        generator = ProjectGenerator()
        
        result = await generator.generate_project(
            prompt="Create a simple web application",
            project_name="TestApp",
            project_type="web_application",
            project_path=str(temp_dir)
        )
        
        # Should return a result dictionary
        assert isinstance(result, dict)
        assert "success" in result
        assert "project_id" in result
        assert "project_name" in result
        assert "files_created" in result
        
        # Should create project directory
        project_path = temp_dir / "TestApp"
        assert project_path.exists()
        
        # Should create basic structure
        assert (project_path / "README.md").exists()
        assert (project_path / "src").exists()
        assert (project_path / "docs").exists()
    
    @pytest.mark.asyncio
    async def test_generate_project_with_api_manager(self, temp_dir):
        """Test project generation with mocked API manager"""
        generator = ProjectGenerator()
        
        # Mock the API manager
        mock_api_manager = AsyncMock()
        mock_api_manager.generate_text.return_value = "Generated code content"
        mock_api_manager.get_available_providers.return_value = ["openai"]
        
        generator.api_manager = mock_api_manager
        
        result = await generator.generate_project(
            prompt="Create a task management application",
            project_name="TaskManager",
            project_type="fullstack",
            project_path=str(temp_dir)
        )
        
        assert result["success"] == True
        assert len(result["files_created"]) > 0
        
        # Verify API manager was used
        # Note: The current implementation uses fallback generation
        # but this test ensures the structure works with API manager
    
    @pytest.mark.asyncio
    async def test_project_structure_creation(self, temp_dir):
        """Test that proper project structure is created"""
        generator = ProjectGenerator()
        
        await generator._initialize_project_structure(temp_dir, "test_project_123")
        
        # Check directories
        expected_dirs = ["src", "docs", "tests", "config", "server"]
        for dir_name in expected_dirs:
            assert (temp_dir / dir_name).exists()
        
        # Check README
        readme_file = temp_dir / "README.md"
        assert readme_file.exists()
        
        readme_content = readme_file.read_text()
        assert "test_project_123" in readme_content
        assert "Aetherforge" in readme_content
    
    @pytest.mark.asyncio
    async def test_documentation_generation(self, temp_dir):
        """Test documentation generation"""
        generator = ProjectGenerator()
        
        files_created = await generator._generate_documentation(
            "Create a user management system",
            temp_dir,
            "web_application"
        )
        
        assert len(files_created) >= 2
        assert "docs/requirements.md" in files_created
        assert "docs/api_documentation.md" in files_created
        
        # Check files exist
        assert (temp_dir / "docs" / "requirements.md").exists()
        assert (temp_dir / "docs" / "api_documentation.md").exists()
        
        # Check content
        requirements_content = (temp_dir / "docs" / "requirements.md").read_text()
        assert "user management system" in requirements_content.lower()
    
    @pytest.mark.asyncio
    async def test_frontend_code_generation(self, temp_dir):
        """Test frontend code generation"""
        generator = ProjectGenerator()
        
        files_created = await generator._generate_frontend_code(
            "Create a dashboard application",
            temp_dir
        )
        
        assert len(files_created) > 0
        assert "src/App.tsx" in files_created
        assert "src/index.tsx" in files_created
        
        # Check files exist and have content
        app_file = temp_dir / "src" / "App.tsx"
        assert app_file.exists()
        
        app_content = app_file.read_text()
        assert "import React" in app_content
        assert "function App" in app_content
    
    @pytest.mark.asyncio
    async def test_backend_code_generation(self, temp_dir):
        """Test backend code generation"""
        generator = ProjectGenerator()
        
        files_created = await generator._generate_backend_code(
            "Create an API for user management",
            temp_dir
        )
        
        assert len(files_created) > 0
        assert "server/server.js" in files_created
        
        # Check server file
        server_file = temp_dir / "server" / "server.js"
        assert server_file.exists()
        
        server_content = server_file.read_text()
        assert "express" in server_content
        assert "app.listen" in server_content
    
    @pytest.mark.asyncio
    async def test_config_files_generation(self, temp_dir):
        """Test configuration files generation"""
        generator = ProjectGenerator()
        
        files_created = await generator._generate_config_files(temp_dir, "TestProject")
        
        assert len(files_created) > 0
        
        # Should create various config files
        config_files = [f for f in files_created if "config" in f or ".json" in f or "docker" in f.lower()]
        assert len(config_files) > 0
    
    @pytest.mark.asyncio
    async def test_package_config_generation(self, temp_dir):
        """Test package configuration generation"""
        generator = ProjectGenerator()
        
        files_created = await generator._generate_package_config(
            "Create a React application",
            temp_dir,
            "frontend"
        )
        
        assert len(files_created) > 0
        assert "package.json" in files_created
        
        # Check package.json
        package_file = temp_dir / "package.json"
        assert package_file.exists()
        
        import json
        package_data = json.loads(package_file.read_text())
        assert "name" in package_data
        assert "scripts" in package_data
        assert "dependencies" in package_data
    
    @pytest.mark.asyncio
    async def test_project_metadata_creation(self, temp_dir):
        """Test project metadata creation"""
        generator = ProjectGenerator()
        
        result_data = {
            "project_id": "test_123",
            "project_name": "TestApp",
            "files_created": ["src/App.tsx", "package.json"],
            "phases_completed": 5
        }
        
        metadata_file = await generator._create_project_metadata(temp_dir, result_data)
        
        assert metadata_file == "aetherforge_project.json"
        
        # Check metadata file
        metadata_path = temp_dir / "aetherforge_project.json"
        assert metadata_path.exists()
        
        import json
        metadata = json.loads(metadata_path.read_text())
        assert metadata["project_id"] == "test_123"
        assert metadata["project_name"] == "TestApp"
        assert "generation_timestamp" in metadata
    
    @pytest.mark.asyncio
    async def test_error_handling(self, temp_dir):
        """Test error handling in project generation"""
        generator = ProjectGenerator()
        
        # Test with invalid project path (read-only or non-existent parent)
        invalid_path = "/invalid/path/that/does/not/exist"
        
        result = await generator.generate_project(
            prompt="Test project",
            project_name="TestApp",
            project_type="web_application",
            project_path=invalid_path
        )
        
        # Should handle error gracefully
        assert isinstance(result, dict)
        assert "error" in result or result.get("success") == False
    
    def test_api_manager_fallback(self):
        """Test that generator works without API manager"""
        # Ensure no API manager is configured
        with patch.dict('os.environ', {}, clear=True):
            generator = ProjectGenerator()
            
            # Should initialize with None API manager
            assert generator.api_manager is None
    
    @pytest.mark.asyncio
    async def test_different_project_types(self, temp_dir):
        """Test generation of different project types"""
        generator = ProjectGenerator()
        
        project_types = ["web_application", "fullstack", "frontend", "backend", "api"]
        
        for project_type in project_types:
            project_path = temp_dir / f"test_{project_type}"
            
            result = await generator.generate_project(
                prompt=f"Create a {project_type} application",
                project_name=f"Test{project_type.title()}",
                project_type=project_type,
                project_path=str(project_path)
            )
            
            # Each should succeed
            assert isinstance(result, dict)
            assert "project_id" in result
            
            # Should create project directory
            assert project_path.exists()

class TestProjectGeneratorIntegration:
    """Integration tests for project generator"""
    
    @pytest.mark.asyncio
    async def test_full_project_generation_workflow(self):
        """Test complete project generation workflow"""
        with tempfile.TemporaryDirectory() as temp_dir:
            generator = ProjectGenerator()
            
            result = await generator.generate_project(
                prompt="Create a modern task management web application with user authentication, task CRUD operations, and real-time updates",
                project_name="TaskMasterPro",
                project_type="fullstack",
                project_path=temp_dir
            )
            
            # Verify result structure
            assert result["success"] == True
            assert result["project_name"] == "TaskMasterPro"
            assert result["phases_completed"] >= 5
            assert len(result["files_created"]) > 10
            
            # Verify project structure
            project_path = Path(temp_dir) / "TaskMasterPro"
            assert project_path.exists()
            
            # Verify key files
            key_files = [
                "README.md",
                "package.json",
                "src/App.tsx",
                "server/server.js",
                "docs/requirements.md",
                "aetherforge_project.json"
            ]
            
            for file_path in key_files:
                full_path = project_path / file_path
                assert full_path.exists(), f"Missing file: {file_path}"
                assert full_path.stat().st_size > 0, f"Empty file: {file_path}"

if __name__ == "__main__":
    pytest.main([__file__])
