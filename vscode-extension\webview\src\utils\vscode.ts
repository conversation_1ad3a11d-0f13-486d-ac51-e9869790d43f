import { VSCodeAPI, WebviewMessage } from '@/types';

declare global {
  interface Window {
    acquireVsCodeApi(): VSCodeAPI;
  }
}

class VSCodeAPIWrapper {
  private api: VSCodeAPI;
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private requestHandlers: Map<string, (data: any) => void> = new Map();
  private requestCounter = 0;

  constructor() {
    this.api = window.acquireVsCodeApi();
    this.setupMessageListener();
  }

  private setupMessageListener() {
    window.addEventListener('message', (event) => {
      const message: WebviewMessage = event.data;
      
      if (message.requestId) {
        // Handle response to a request
        const handler = this.requestHandlers.get(message.requestId);
        if (handler) {
          handler(message.data);
          this.requestHandlers.delete(message.requestId);
        }
      } else {
        // Handle regular message
        const handler = this.messageHandlers.get(message.command);
        if (handler) {
          handler(message.data);
        }
      }
    });
  }

  /**
   * Send a message to the extension
   */
  postMessage(command: string, data?: any): void {
    this.api.postMessage({ command, data });
  }

  /**
   * Send a request and wait for response
   */
  async sendRequest<T = any>(command: string, data?: any): Promise<T> {
    const requestId = `req_${++this.requestCounter}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.requestHandlers.delete(requestId);
        reject(new Error(`Request timeout: ${command}`));
      }, 30000); // 30 second timeout

      this.requestHandlers.set(requestId, (responseData) => {
        clearTimeout(timeout);
        if (responseData?.error) {
          reject(new Error(responseData.error));
        } else {
          resolve(responseData);
        }
      });

      this.api.postMessage({ command, data, requestId });
    });
  }

  /**
   * Register a message handler
   */
  onMessage(command: string, handler: (data: any) => void): () => void {
    this.messageHandlers.set(command, handler);
    
    // Return unsubscribe function
    return () => {
      this.messageHandlers.delete(command);
    };
  }

  /**
   * Get stored state
   */
  getState<T = any>(): T | undefined {
    return this.api.getState();
  }

  /**
   * Store state
   */
  setState(state: any): void {
    this.api.setState(state);
  }

  /**
   * Update state partially
   */
  updateState(updates: any): void {
    const currentState = this.getState() || {};
    this.setState({ ...currentState, ...updates });
  }
}

// Create singleton instance
export const vscode = new VSCodeAPIWrapper();

// Utility functions for common operations
export const vsCodeUtils = {
  /**
   * Show information message
   */
  showInfo: (message: string, ...actions: string[]) => {
    vscode.postMessage('showInfo', { message, actions });
  },

  /**
   * Show error message
   */
  showError: (message: string, ...actions: string[]) => {
    vscode.postMessage('showError', { message, actions });
  },

  /**
   * Show warning message
   */
  showWarning: (message: string, ...actions: string[]) => {
    vscode.postMessage('showWarning', { message, actions });
  },

  /**
   * Open external URL
   */
  openExternal: (url: string) => {
    vscode.postMessage('openExternal', { url });
  },

  /**
   * Copy to clipboard
   */
  copyToClipboard: (text: string) => {
    vscode.postMessage('copyToClipboard', { text });
  },

  /**
   * Save file
   */
  saveFile: async (content: string, filename?: string) => {
    return vscode.sendRequest('saveFile', { content, filename });
  },

  /**
   * Open file dialog
   */
  openFileDialog: async (options?: any) => {
    return vscode.sendRequest('openFileDialog', options);
  },

  /**
   * Get workspace folders
   */
  getWorkspaceFolders: async () => {
    return vscode.sendRequest('getWorkspaceFolders');
  },

  /**
   * Execute VS Code command
   */
  executeCommand: (command: string, ...args: any[]) => {
    vscode.postMessage('executeCommand', { command, args });
  },

  /**
   * Open folder in VS Code
   */
  openFolder: (path: string, newWindow = false) => {
    vscode.postMessage('openFolder', { path, newWindow });
  },

  /**
   * Refresh connection to orchestrator
   */
  refreshConnection: () => {
    vscode.postMessage('refreshConnection');
  },

  /**
   * Get extension configuration
   */
  getConfiguration: async (section?: string) => {
    return vscode.sendRequest('getConfiguration', { section });
  },

  /**
   * Update extension configuration
   */
  updateConfiguration: (section: string, value: any) => {
    vscode.postMessage('updateConfiguration', { section, value });
  }
};

// React hook for VS Code API
export const useVSCode = () => {
  return { vscode, ...vsCodeUtils };
};

// React hook for message handling
export const useVSCodeMessage = (command: string, handler: (data: any) => void) => {
  React.useEffect(() => {
    const unsubscribe = vscode.onMessage(command, handler);
    return unsubscribe;
  }, [command, handler]);
};

// React hook for state management
export const useVSCodeState = <T>(initialState: T) => {
  const [state, setState] = React.useState<T>(() => {
    const stored = vscode.getState();
    return stored || initialState;
  });

  const updateState = React.useCallback((updates: Partial<T>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      vscode.setState(newState);
      return newState;
    });
  }, []);

  return [state, updateState] as const;
};

// Import React for hooks
import React from 'react';

// Error boundary for webview panels
export class WebviewErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Webview error:', error, errorInfo);
    vscode.postMessage('webviewError', {
      error: error.message,
      stack: error.stack,
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return React.createElement('div', { className: 'error-boundary' },
        React.createElement('h2', null, 'Something went wrong'),
        React.createElement('p', null, this.state.error?.message),
        React.createElement('button', {
          onClick: () => this.setState({ hasError: false })
        }, 'Try again')
      );
    }

    return this.props.children;
  }
}
