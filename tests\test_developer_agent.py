#!/usr/bin/env python3
"""
Comprehensive test suite for the Developer Agent
Tests the actual DeveloperAgent class and its methods
"""

import pytest
import asyncio
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
import sys
import os
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from developer_agent import (
    DeveloperAgent, 
    CodeQuality, 
    ProjectType,
    CodeGenerationContext,
    ProjectStructure,
    CodeFile,
    TestSuite,
    DeveloperAgentError,
    ValidationError,
    FileWriteError,
    QualityGateError
)
from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier, TechnologyChoice


class TestDeveloperAgent:
    """Test the DeveloperAgent class"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.output_path = Path(self.temp_dir) / "test_output"
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_architecture(self) -> SystemArchitecture:
        """Create a test architecture for testing"""
        tech_stack = {
            "backend": [
                TechnologyChoice(
                    name="Node.js",
                    version="18.x",
                    category="Runtime",
                    justification="JavaScript runtime",
                    alternatives=["Python", "Java"],
                    pros=["Fast", "JavaScript ecosystem"],
                    cons=["Single-threaded"],
                    learning_curve="medium",
                    community_support="excellent"
                ),
                TechnologyChoice(
                    name="Express.js",
                    version="4.x",
                    category="Framework",
                    justification="Minimal web framework",
                    alternatives=["Fastify", "Koa"],
                    pros=["Simple", "Flexible"],
                    cons=["Minimal features"],
                    learning_curve="low",
                    community_support="excellent"
                )
            ],
            "testing": [
                TechnologyChoice(
                    name="Jest",
                    version="29.x",
                    category="Testing",
                    justification="Testing framework",
                    alternatives=["Mocha", "Vitest"],
                    pros=["Zero config", "Snapshot testing"],
                    cons=["Can be slow"],
                    learning_curve="low",
                    community_support="excellent"
                )
            ]
        }
        
        return SystemArchitecture(
            project_name="Test Project",
            architecture_pattern=ArchitecturePattern.LAYERED,
            scalability_tier=ScalabilityTier.SMALL,
            components=[],
            data_architecture={},
            security_architecture={},
            deployment_architecture={},
            integration_patterns=[],
            technology_stack=tech_stack,
            quality_attributes={
                "performance": {
                    "response_time": "< 200ms"
                }
            },
            constraints=[],
            assumptions=[],
            risks=[]
        )
    
    @patch('developer_agent.MCPRAGClient')
    def test_developer_agent_initialization(self, mock_mcp_client):
        """Test DeveloperAgent initialization"""
        agent = DeveloperAgent()
        
        assert agent is not None
        assert hasattr(agent, 'mcp_client')
        assert hasattr(agent, 'code_templates')
        assert hasattr(agent, 'coding_standards')
        assert hasattr(agent, 'test_patterns')
    
    @patch('developer_agent.MCPRAGClient')
    def test_input_validation_success(self, mock_mcp_client):
        """Test successful input validation"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()

        # Should not raise any exceptions
        asyncio.run(agent._validate_inputs(architecture, self.output_path, CodeQuality.PRODUCTION))

    @patch('developer_agent.MCPRAGClient')
    def test_input_validation_missing_tech_stack(self, mock_mcp_client):
        """Test input validation with missing technology stack"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()
        architecture.technology_stack = {}

        with pytest.raises(ValidationError, match="Technology stack is required"):
            asyncio.run(agent._validate_inputs(architecture, self.output_path, CodeQuality.PRODUCTION))
    
    @patch('developer_agent.MCPRAGClient')
    def test_code_generation_context_creation(self, mock_mcp_client):
        """Test creation of code generation context"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()
        
        context = asyncio.run(agent._create_generation_context(
            architecture, 
            self.output_path, 
            CodeQuality.PRODUCTION
        ))
        
        assert isinstance(context, CodeGenerationContext)
        assert context.architecture == architecture
        assert context.quality_level == CodeQuality.PRODUCTION
        assert context.target_technologies == architecture.technology_stack
    
    @patch('developer_agent.MCPRAGClient')
    def test_project_structure_generation(self, mock_mcp_client):
        """Test project structure generation"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()
        
        context = asyncio.run(agent._create_generation_context(
            architecture, 
            self.output_path, 
            CodeQuality.PRODUCTION
        ))
        
        structure = asyncio.run(agent._generate_project_structure(context))
        
        assert isinstance(structure, ProjectStructure)
        assert structure.name == "Test Project"
        assert structure.type in [ProjectType.API, ProjectType.BACKEND]
        assert len(structure.source_files) > 0
        # Note: test files are generated in a separate step, not in structure generation
        assert len(structure.config_files) > 0
        assert len(structure.documentation_files) > 0
    
    @patch('developer_agent.MCPRAGClient')
    def test_error_handling(self, mock_mcp_client):
        """Test error handling mechanisms"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()

        # Test with invalid output path (use a path that definitely doesn't exist and can't be created)
        invalid_path = Path("Z:/definitely/invalid/path/that/does/not/exist")

        with pytest.raises(ValidationError, match="Output path is not writable"):
            asyncio.run(agent._validate_inputs(architecture, invalid_path, CodeQuality.PRODUCTION))
    
    @patch('developer_agent.MCPRAGClient')
    def test_technology_stack_processing(self, mock_mcp_client):
        """Test technology stack processing"""
        agent = DeveloperAgent()
        architecture = self.create_test_architecture()
        
        # Test that technology choices are properly processed
        backend_techs = architecture.technology_stack.get("backend", [])
        assert len(backend_techs) == 2
        assert any(tech.name == "Node.js" for tech in backend_techs)
        assert any(tech.name == "Express.js" for tech in backend_techs)
        
        testing_techs = architecture.technology_stack.get("testing", [])
        assert len(testing_techs) == 1
        assert testing_techs[0].name == "Jest"


class TestCodeGeneration:
    """Test code generation functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_code_file_creation(self):
        """Test CodeFile creation"""
        code_file = CodeFile(
            path="src/app.js",
            content="console.log('Hello World');",
            language="javascript",
            file_type="source",
            dependencies=["express"],
            imports=["express"],
            exports=["app"],
            tests_required=True,
            documentation_required=True
        )
        
        assert code_file.path == "src/app.js"
        assert code_file.language == "javascript"
        assert code_file.file_type == "source"
        assert "express" in code_file.dependencies
        assert code_file.tests_required is True
    
    def test_test_suite_creation(self):
        """Test TestSuite creation"""
        test_file = CodeFile(
            path="tests/app.test.js",
            content="test('should work', () => { expect(true).toBe(true); });",
            language="javascript",
            file_type="test"
        )
        
        test_suite = TestSuite(
            name="App Tests",
            test_type="unit",
            files=[test_file],
            coverage_target=80.0,
            framework="jest",
            setup_required=True
        )
        
        assert test_suite.name == "App Tests"
        assert test_suite.test_type == "unit"
        assert len(test_suite.files) == 1
        assert test_suite.coverage_target == 80.0
        assert test_suite.framework == "jest"


class TestQualityGates:
    """Test quality gate functionality"""
    
    def test_code_quality_enum(self):
        """Test CodeQuality enum values"""
        assert CodeQuality.PROTOTYPE.value == "prototype"
        assert CodeQuality.DEVELOPMENT.value == "development"
        assert CodeQuality.PRODUCTION.value == "production"
    
    def test_project_type_enum(self):
        """Test ProjectType enum values"""
        assert ProjectType.FRONTEND.value == "frontend"
        assert ProjectType.BACKEND.value == "backend"
        assert ProjectType.FULLSTACK.value == "fullstack"
        assert ProjectType.API.value == "api"
        assert ProjectType.MOBILE.value == "mobile"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
