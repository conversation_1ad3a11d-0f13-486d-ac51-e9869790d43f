"""
Archon Integration for Aetherforge
Leverages Archon for dynamic agent generation, optimization, and evaluation
throughout the project lifecycle. Provides intelligent agent management and evolution.
"""

import asyncio
import aiohttp
import json
import logging
import os
import uuid
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)

class ArchonIntegrationError(Exception):
    """Base exception for Archon Integration errors"""
    pass

class AgentGenerationError(ArchonIntegrationError):
    """Raised when agent generation fails"""
    pass

class AgentOptimizationError(ArchonIntegrationError):
    """Raised when agent optimization fails"""
    pass

class AgentEvaluationError(ArchonIntegrationError):
    """Raised when agent evaluation fails"""
    pass

class AgentLifecycleStage(Enum):
    """Stages in the agent lifecycle"""
    CONCEPTION = "conception"
    GENERATION = "generation"
    OPTIMIZATION = "optimization"
    EVALUATION = "evaluation"
    DEPLOYMENT = "deployment"
    MONITORING = "monitoring"
    EVOLUTION = "evolution"
    RETIREMENT = "retirement"

class AgentType(Enum):
    """Types of agents that can be generated"""
    ANALYST = "analyst"
    ARCHITECT = "architect"
    DEVELOPER = "developer"
    QA = "qa"
    RESEARCHER = "researcher"
    OPTIMIZER = "optimizer"
    MONITOR = "monitor"
    SPECIALIST = "specialist"

class OptimizationStrategy(Enum):
    """Strategies for agent optimization"""
    PERFORMANCE = "performance"
    ACCURACY = "accuracy"
    EFFICIENCY = "efficiency"
    ADAPTABILITY = "adaptability"
    COLLABORATION = "collaboration"
    SPECIALIZATION = "specialization"

@dataclass
class AgentSpecification:
    """Specification for agent generation"""
    agent_id: str
    agent_type: AgentType
    name: str
    description: str
    capabilities: List[str] = field(default_factory=list)
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    tools: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    performance_targets: Dict[str, float] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentMetrics:
    """Metrics for agent evaluation"""
    agent_id: str
    performance_score: float
    accuracy_score: float
    efficiency_score: float
    collaboration_score: float
    adaptability_score: float
    overall_score: float
    execution_time: float
    resource_usage: Dict[str, float] = field(default_factory=dict)
    error_rate: float = 0.0
    success_rate: float = 0.0
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class AgentEvolutionPlan:
    """Plan for agent evolution and optimization"""
    agent_id: str
    current_version: str
    target_version: str
    optimization_strategies: List[OptimizationStrategy]
    improvement_areas: List[str]
    expected_improvements: Dict[str, float]
    implementation_steps: List[str]
    validation_criteria: List[str]
    rollback_plan: str

@dataclass
class AgentTeamComposition:
    """Composition of an agent team"""
    team_id: str
    project_id: str
    agents: List[AgentSpecification]
    team_dynamics: Dict[str, Any] = field(default_factory=dict)
    coordination_strategy: str = "collaborative"
    communication_patterns: List[str] = field(default_factory=list)
    performance_targets: Dict[str, float] = field(default_factory=dict)

class ArchonIntegration:
    """Comprehensive Archon Integration for dynamic agent management"""
    
    def __init__(self, archon_url: str = None, openai_api_key: str = None):
        self.archon_url = archon_url or os.getenv("ARCHON_URL", "http://localhost:8100")
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Integration configuration
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes for complex operations
        
        # Agent lifecycle tracking
        self.active_agents = {}
        self.agent_metrics = {}
        self.evolution_plans = {}
        
        # Team management
        self.active_teams = {}
        self.team_performance = {}
        
        # Optimization settings
        self.optimization_enabled = True
        self.evaluation_interval = 3600  # 1 hour
        self.evolution_threshold = 0.8  # Trigger evolution when performance drops below 80%
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def generate_agent_team(self, project_prompt: str, project_context: Dict[str, Any],
                                team_size: int = 4) -> AgentTeamComposition:
        """Generate a dynamic agent team using Archon"""
        logger.info(f"Generating agent team for project: {project_context.get('project_id', 'unknown')}")
        
        try:
            # Check Archon service availability
            if not await self._check_archon_availability():
                logger.warning("Archon service unavailable, using fallback team generation")
                return await self._fallback_team_generation(project_prompt, project_context, team_size)
            
            # Prepare team generation request
            team_request = {
                "prompt": project_prompt,
                "context": project_context,
                "team_size": team_size,
                "optimization_enabled": self.optimization_enabled,
                "performance_targets": {
                    "overall_efficiency": 0.85,
                    "collaboration_score": 0.90,
                    "adaptability": 0.80
                }
            }
            
            # Generate team using Archon
            team_composition = await self._generate_team_with_archon(team_request)
            
            # Register team for lifecycle management
            await self._register_team(team_composition)
            
            logger.info(f"Successfully generated team with {len(team_composition.agents)} agents")
            return team_composition
            
        except Exception as e:
            logger.error(f"Agent team generation failed: {e}")
            raise AgentGenerationError(f"Failed to generate agent team: {e}")
    
    async def optimize_agent(self, agent_id: str, optimization_strategy: OptimizationStrategy,
                           performance_data: Dict[str, Any]) -> AgentEvolutionPlan:
        """Optimize an existing agent using Archon"""
        logger.info(f"Optimizing agent {agent_id} with strategy: {optimization_strategy.value}")
        
        try:
            # Get current agent specification
            agent_spec = self.active_agents.get(agent_id)
            if not agent_spec:
                raise AgentOptimizationError(f"Agent {agent_id} not found in active agents")
            
            # Prepare optimization request with JSON-serializable data
            agent_spec_dict = {
                "agent_id": agent_spec.agent_id,
                "agent_type": agent_spec.agent_type.value,
                "name": agent_spec.name,
                "description": agent_spec.description,
                "capabilities": agent_spec.capabilities,
                "requirements": agent_spec.requirements,
                "constraints": agent_spec.constraints,
                "tools": agent_spec.tools,
                "dependencies": agent_spec.dependencies,
                "performance_targets": agent_spec.performance_targets,
                "context": agent_spec.context
            }

            optimization_request = {
                "agent_id": agent_id,
                "agent_specification": agent_spec_dict,
                "optimization_strategy": optimization_strategy.value,
                "performance_data": performance_data,
                "current_metrics": self.agent_metrics.get(agent_id, {}).__dict__ if agent_id in self.agent_metrics else {}
            }
            
            # Perform optimization using Archon
            evolution_plan = await self._optimize_agent_with_archon(optimization_request)
            
            # Store evolution plan
            self.evolution_plans[agent_id] = evolution_plan
            
            logger.info(f"Generated evolution plan for agent {agent_id}")
            return evolution_plan
            
        except Exception as e:
            logger.error(f"Agent optimization failed: {e}")
            raise AgentOptimizationError(f"Failed to optimize agent {agent_id}: {e}")
    
    async def evaluate_agent_performance(self, agent_id: str, 
                                       evaluation_context: Dict[str, Any]) -> AgentMetrics:
        """Evaluate agent performance using Archon"""
        logger.info(f"Evaluating performance for agent {agent_id}")
        
        try:
            # Prepare evaluation request
            evaluation_request = {
                "agent_id": agent_id,
                "evaluation_context": evaluation_context,
                "metrics_to_evaluate": [
                    "performance_score",
                    "accuracy_score", 
                    "efficiency_score",
                    "collaboration_score",
                    "adaptability_score"
                ]
            }
            
            # Perform evaluation using Archon
            metrics = await self._evaluate_agent_with_archon(evaluation_request)
            
            # Store metrics
            self.agent_metrics[agent_id] = metrics
            
            # Check if optimization is needed
            if metrics.overall_score < self.evolution_threshold:
                logger.info(f"Agent {agent_id} performance below threshold, triggering optimization")
                await self._trigger_agent_evolution(agent_id, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Agent evaluation failed: {e}")
            raise AgentEvaluationError(f"Failed to evaluate agent {agent_id}: {e}")
    
    async def evolve_agent_team(self, team_id: str, 
                              evolution_triggers: List[str]) -> AgentTeamComposition:
        """Evolve an entire agent team based on performance and requirements"""
        logger.info(f"Evolving agent team {team_id}")
        
        try:
            # Get current team composition
            current_team = self.active_teams.get(team_id)
            if not current_team:
                raise AgentOptimizationError(f"Team {team_id} not found")
            
            # Analyze team performance
            team_metrics = await self._analyze_team_performance(team_id)
            
            # Generate evolution strategy
            evolution_strategy = await self._generate_team_evolution_strategy(
                current_team, team_metrics, evolution_triggers
            )
            
            # Execute team evolution
            evolved_team = await self._execute_team_evolution(team_id, evolution_strategy)
            
            # Update team registration
            self.active_teams[team_id] = evolved_team
            
            logger.info(f"Successfully evolved team {team_id}")
            return evolved_team
            
        except Exception as e:
            logger.error(f"Team evolution failed: {e}")
            raise AgentOptimizationError(f"Failed to evolve team {team_id}: {e}")
    
    async def monitor_agent_lifecycle(self, agent_id: str) -> Dict[str, Any]:
        """Monitor agent throughout its lifecycle"""
        logger.info(f"Monitoring lifecycle for agent {agent_id}")
        
        try:
            # Get agent current state
            agent_state = await self._get_agent_state(agent_id)
            
            # Determine lifecycle stage
            current_stage = self._determine_lifecycle_stage(agent_state)
            
            # Perform stage-specific monitoring
            monitoring_data = await self._perform_stage_monitoring(agent_id, current_stage)
            
            # Check for lifecycle transitions
            next_stage = await self._check_lifecycle_transitions(agent_id, current_stage, monitoring_data)
            
            if next_stage != current_stage:
                await self._transition_agent_stage(agent_id, current_stage, next_stage)
            
            return {
                "agent_id": agent_id,
                "current_stage": current_stage.value,
                "next_stage": next_stage.value if next_stage != current_stage else None,
                "monitoring_data": monitoring_data,
                "lifecycle_health": self._assess_lifecycle_health(monitoring_data)
            }
            
        except Exception as e:
            logger.error(f"Agent lifecycle monitoring failed: {e}")
            return {
                "agent_id": agent_id,
                "error": str(e),
                "status": "monitoring_failed"
            }

    async def _check_archon_availability(self) -> bool:
        """Check if Archon service is available"""
        try:
            if not self.session:
                return False

            async with self.session.get(f"{self.archon_url}/health",
                                      timeout=aiohttp.ClientTimeout(total=5)) as response:
                return response.status == 200
        except Exception:
            return False

    async def _generate_team_with_archon(self, team_request: Dict[str, Any]) -> AgentTeamComposition:
        """Generate agent team using Archon service"""
        try:
            if not self.session:
                raise AgentGenerationError("Session not initialized")

            async with self.session.post(
                f"{self.archon_url}/generate_team",
                json=team_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_team_composition(data)
                else:
                    raise AgentGenerationError(f"Archon team generation failed: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Archon team generation failed: {e}")
            raise AgentGenerationError(f"Failed to generate team with Archon: {e}")

    async def _optimize_agent_with_archon(self, optimization_request: Dict[str, Any]) -> AgentEvolutionPlan:
        """Optimize agent using Archon service"""
        try:
            if not self.session:
                raise AgentOptimizationError("Session not initialized")

            async with self.session.post(
                f"{self.archon_url}/optimize_agent",
                json=optimization_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_evolution_plan(data)
                else:
                    raise AgentOptimizationError(f"Archon optimization failed: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Archon agent optimization failed: {e}")
            raise AgentOptimizationError(f"Failed to optimize agent with Archon: {e}")

    async def _evaluate_agent_with_archon(self, evaluation_request: Dict[str, Any]) -> AgentMetrics:
        """Evaluate agent using Archon service"""
        try:
            if not self.session:
                raise AgentEvaluationError("Session not initialized")

            async with self.session.post(
                f"{self.archon_url}/evaluate_agent",
                json=evaluation_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_agent_metrics(data)
                else:
                    raise AgentEvaluationError(f"Archon evaluation failed: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Archon agent evaluation failed: {e}")
            raise AgentEvaluationError(f"Failed to evaluate agent with Archon: {e}")

    async def _fallback_team_generation(self, project_prompt: str, project_context: Dict[str, Any],
                                      team_size: int) -> AgentTeamComposition:
        """Fallback team generation when Archon is unavailable"""
        logger.info("Using fallback team generation")

        # Generate basic team composition
        team_id = str(uuid.uuid4())
        project_id = project_context.get("project_id", str(uuid.uuid4()))

        # Create standard BMAD agents
        agents = []

        # Analyst Agent
        analyst = AgentSpecification(
            agent_id=str(uuid.uuid4()),
            agent_type=AgentType.ANALYST,
            name="Requirements Analyst",
            description="Analyzes project requirements and creates specifications",
            capabilities=["requirement_analysis", "specification_creation", "stakeholder_communication"],
            requirements=["project_prompt", "domain_knowledge"],
            tools=["research_engine", "documentation_tools"],
            performance_targets={"accuracy": 0.9, "completeness": 0.85}
        )
        agents.append(analyst)

        # Architect Agent
        architect = AgentSpecification(
            agent_id=str(uuid.uuid4()),
            agent_type=AgentType.ARCHITECT,
            name="System Architect",
            description="Designs system architecture and technical specifications",
            capabilities=["architecture_design", "technology_selection", "system_modeling"],
            requirements=["project_specifications", "technical_constraints"],
            tools=["architecture_tools", "modeling_tools"],
            performance_targets={"scalability": 0.9, "maintainability": 0.85}
        )
        agents.append(architect)

        # Developer Agent
        developer = AgentSpecification(
            agent_id=str(uuid.uuid4()),
            agent_type=AgentType.DEVELOPER,
            name="Code Developer",
            description="Implements code based on architecture and specifications",
            capabilities=["code_generation", "implementation", "debugging"],
            requirements=["architecture_design", "coding_standards"],
            tools=["development_tools", "version_control"],
            performance_targets={"code_quality": 0.9, "efficiency": 0.8}
        )
        agents.append(developer)

        # QA Agent
        qa = AgentSpecification(
            agent_id=str(uuid.uuid4()),
            agent_type=AgentType.QA,
            name="Quality Assurance",
            description="Tests and validates code quality and functionality",
            capabilities=["testing", "validation", "quality_assessment"],
            requirements=["code_implementation", "test_specifications"],
            tools=["testing_frameworks", "quality_tools"],
            performance_targets={"test_coverage": 0.95, "defect_detection": 0.9}
        )
        agents.append(qa)

        return AgentTeamComposition(
            team_id=team_id,
            project_id=project_id,
            agents=agents[:team_size],
            team_dynamics={"fallback": True, "coordination": "sequential"},
            coordination_strategy="bmad_methodology",
            communication_patterns=["sequential_handoff", "feedback_loops"]
        )

    def _parse_team_composition(self, data: Dict[str, Any]) -> AgentTeamComposition:
        """Parse team composition from Archon response"""
        agents = []
        for agent_data in data.get("agents", []):
            agent = AgentSpecification(
                agent_id=agent_data.get("agent_id", str(uuid.uuid4())),
                agent_type=AgentType(agent_data.get("agent_type", "specialist")),
                name=agent_data.get("name", "Generated Agent"),
                description=agent_data.get("description", ""),
                capabilities=agent_data.get("capabilities", []),
                requirements=agent_data.get("requirements", []),
                constraints=agent_data.get("constraints", []),
                tools=agent_data.get("tools", []),
                dependencies=agent_data.get("dependencies", []),
                performance_targets=agent_data.get("performance_targets", {}),
                context=agent_data.get("context", {})
            )
            agents.append(agent)

        return AgentTeamComposition(
            team_id=data.get("team_id", str(uuid.uuid4())),
            project_id=data.get("project_id", str(uuid.uuid4())),
            agents=agents,
            team_dynamics=data.get("team_dynamics", {}),
            coordination_strategy=data.get("coordination_strategy", "collaborative"),
            communication_patterns=data.get("communication_patterns", []),
            performance_targets=data.get("performance_targets", {})
        )

    def _parse_evolution_plan(self, data: Dict[str, Any]) -> AgentEvolutionPlan:
        """Parse evolution plan from Archon response"""
        return AgentEvolutionPlan(
            agent_id=data.get("agent_id", ""),
            current_version=data.get("current_version", "1.0.0"),
            target_version=data.get("target_version", "1.1.0"),
            optimization_strategies=[OptimizationStrategy(s) for s in data.get("optimization_strategies", [])],
            improvement_areas=data.get("improvement_areas", []),
            expected_improvements=data.get("expected_improvements", {}),
            implementation_steps=data.get("implementation_steps", []),
            validation_criteria=data.get("validation_criteria", []),
            rollback_plan=data.get("rollback_plan", "")
        )

    def _parse_agent_metrics(self, data: Dict[str, Any]) -> AgentMetrics:
        """Parse agent metrics from Archon response"""
        return AgentMetrics(
            agent_id=data.get("agent_id", ""),
            performance_score=data.get("performance_score", 0.0),
            accuracy_score=data.get("accuracy_score", 0.0),
            efficiency_score=data.get("efficiency_score", 0.0),
            collaboration_score=data.get("collaboration_score", 0.0),
            adaptability_score=data.get("adaptability_score", 0.0),
            overall_score=data.get("overall_score", 0.0),
            execution_time=data.get("execution_time", 0.0),
            resource_usage=data.get("resource_usage", {}),
            error_rate=data.get("error_rate", 0.0),
            success_rate=data.get("success_rate", 0.0)
        )

    async def _register_team(self, team_composition: AgentTeamComposition):
        """Register team for lifecycle management"""
        self.active_teams[team_composition.team_id] = team_composition

        # Register individual agents
        for agent in team_composition.agents:
            self.active_agents[agent.agent_id] = agent

        logger.info(f"Registered team {team_composition.team_id} with {len(team_composition.agents)} agents")

    async def _trigger_agent_evolution(self, agent_id: str, metrics: AgentMetrics):
        """Trigger agent evolution based on performance metrics"""
        logger.info(f"Triggering evolution for agent {agent_id}")

        # Determine optimization strategy based on lowest scores
        strategy = OptimizationStrategy.PERFORMANCE
        if metrics.accuracy_score < 0.7:
            strategy = OptimizationStrategy.ACCURACY
        elif metrics.efficiency_score < 0.7:
            strategy = OptimizationStrategy.EFFICIENCY
        elif metrics.collaboration_score < 0.7:
            strategy = OptimizationStrategy.COLLABORATION
        elif metrics.adaptability_score < 0.7:
            strategy = OptimizationStrategy.ADAPTABILITY

        # Create optimization request
        performance_data = {
            "current_metrics": metrics.__dict__,
            "trigger_reason": "performance_threshold",
            "optimization_priority": "high"
        }

        # Schedule optimization
        try:
            evolution_plan = await self.optimize_agent(agent_id, strategy, performance_data)
            logger.info(f"Evolution plan created for agent {agent_id}: {evolution_plan.target_version}")
        except Exception as e:
            logger.error(f"Failed to create evolution plan for agent {agent_id}: {e}")

    async def _analyze_team_performance(self, team_id: str) -> Dict[str, Any]:
        """Analyze overall team performance"""
        team = self.active_teams.get(team_id)
        if not team:
            return {}

        team_metrics = {
            "team_id": team_id,
            "agent_count": len(team.agents),
            "individual_scores": {},
            "team_averages": {},
            "collaboration_effectiveness": 0.0,
            "coordination_efficiency": 0.0
        }

        # Collect individual agent metrics
        total_scores = {
            "performance": 0.0,
            "accuracy": 0.0,
            "efficiency": 0.0,
            "collaboration": 0.0,
            "adaptability": 0.0,
            "overall": 0.0
        }

        agent_count = 0
        for agent in team.agents:
            if agent.agent_id in self.agent_metrics:
                metrics = self.agent_metrics[agent.agent_id]
                team_metrics["individual_scores"][agent.agent_id] = {
                    "performance": metrics.performance_score,
                    "accuracy": metrics.accuracy_score,
                    "efficiency": metrics.efficiency_score,
                    "collaboration": metrics.collaboration_score,
                    "adaptability": metrics.adaptability_score,
                    "overall": metrics.overall_score
                }

                # Add to totals
                total_scores["performance"] += metrics.performance_score
                total_scores["accuracy"] += metrics.accuracy_score
                total_scores["efficiency"] += metrics.efficiency_score
                total_scores["collaboration"] += metrics.collaboration_score
                total_scores["adaptability"] += metrics.adaptability_score
                total_scores["overall"] += metrics.overall_score
                agent_count += 1

        # Calculate averages
        if agent_count > 0:
            for key in total_scores:
                team_metrics["team_averages"][key] = total_scores[key] / agent_count

        # Calculate team-specific metrics
        team_metrics["collaboration_effectiveness"] = team_metrics["team_averages"].get("collaboration", 0.0)
        team_metrics["coordination_efficiency"] = min(team_metrics["team_averages"].get("efficiency", 0.0), 1.0)

        return team_metrics

    async def _generate_team_evolution_strategy(self, current_team: AgentTeamComposition,
                                              team_metrics: Dict[str, Any],
                                              evolution_triggers: List[str]) -> Dict[str, Any]:
        """Generate strategy for team evolution"""
        strategy = {
            "team_id": current_team.team_id,
            "evolution_type": "optimization",
            "target_improvements": {},
            "agent_modifications": [],
            "new_agents": [],
            "retired_agents": [],
            "coordination_changes": {}
        }

        # Analyze evolution triggers
        for trigger in evolution_triggers:
            if trigger == "performance_degradation":
                strategy["evolution_type"] = "performance_boost"
                strategy["target_improvements"]["overall_performance"] = 0.2
            elif trigger == "new_requirements":
                strategy["evolution_type"] = "capability_expansion"
                strategy["new_agents"].append({
                    "type": "specialist",
                    "reason": "new_requirements"
                })
            elif trigger == "technology_change":
                strategy["evolution_type"] = "technology_adaptation"
                strategy["agent_modifications"].append({
                    "modification_type": "tool_update",
                    "scope": "all_agents"
                })

        # Identify underperforming agents
        for agent_id, scores in team_metrics.get("individual_scores", {}).items():
            if scores.get("overall", 0.0) < 0.7:
                strategy["agent_modifications"].append({
                    "agent_id": agent_id,
                    "modification_type": "optimization",
                    "target_score": 0.85
                })

        return strategy

    async def _execute_team_evolution(self, team_id: str, evolution_strategy: Dict[str, Any]) -> AgentTeamComposition:
        """Execute team evolution based on strategy"""
        current_team = self.active_teams[team_id]
        evolved_agents = []

        # Process existing agents
        for agent in current_team.agents:
            # Check if agent needs modification
            agent_modifications = [
                mod for mod in evolution_strategy.get("agent_modifications", [])
                if mod.get("agent_id") == agent.agent_id
            ]

            if agent_modifications:
                # Apply modifications to agent
                evolved_agent = await self._evolve_agent(agent, agent_modifications[0])
                evolved_agents.append(evolved_agent)
            else:
                # Keep agent as is
                evolved_agents.append(agent)

        # Add new agents if specified
        for new_agent_spec in evolution_strategy.get("new_agents", []):
            new_agent = await self._create_specialized_agent(new_agent_spec, current_team.project_id)
            evolved_agents.append(new_agent)

        # Create evolved team composition
        evolved_team = AgentTeamComposition(
            team_id=team_id,
            project_id=current_team.project_id,
            agents=evolved_agents,
            team_dynamics={
                **current_team.team_dynamics,
                "evolution_applied": True,
                "evolution_timestamp": datetime.now().isoformat()
            },
            coordination_strategy=current_team.coordination_strategy,
            communication_patterns=current_team.communication_patterns,
            performance_targets=current_team.performance_targets
        )

        return evolved_team

    async def _evolve_agent(self, agent: AgentSpecification, modification: Dict[str, Any]) -> AgentSpecification:
        """Evolve a single agent based on modification specification"""
        evolved_agent = AgentSpecification(
            agent_id=agent.agent_id,
            agent_type=agent.agent_type,
            name=agent.name,
            description=agent.description,
            capabilities=agent.capabilities.copy(),
            requirements=agent.requirements.copy(),
            constraints=agent.constraints.copy(),
            tools=agent.tools.copy(),
            dependencies=agent.dependencies.copy(),
            performance_targets=agent.performance_targets.copy(),
            context={**agent.context, "evolved": True}
        )

        # Apply modifications based on type
        mod_type = modification.get("modification_type", "optimization")

        if mod_type == "optimization":
            # Enhance performance targets
            target_score = modification.get("target_score", 0.85)
            evolved_agent.performance_targets["target_overall"] = target_score
            evolved_agent.capabilities.append("performance_optimized")

        elif mod_type == "tool_update":
            # Update tools
            evolved_agent.tools.append("enhanced_tools")
            evolved_agent.capabilities.append("tool_enhanced")

        elif mod_type == "capability_expansion":
            # Add new capabilities
            new_capabilities = modification.get("new_capabilities", [])
            evolved_agent.capabilities.extend(new_capabilities)

        return evolved_agent

    async def _create_specialized_agent(self, agent_spec: Dict[str, Any], project_id: str) -> AgentSpecification:
        """Create a new specialized agent"""
        agent_type = AgentType(agent_spec.get("type", "specialist"))

        return AgentSpecification(
            agent_id=str(uuid.uuid4()),
            agent_type=agent_type,
            name=f"Specialized {agent_type.value.title()}",
            description=f"Specialized agent created for {agent_spec.get('reason', 'project needs')}",
            capabilities=[f"{agent_type.value}_specialized", "adaptive", "collaborative"],
            requirements=["project_context", "team_coordination"],
            tools=["specialized_tools", "communication_tools"],
            performance_targets={"specialization": 0.9, "adaptability": 0.8},
            context={"project_id": project_id, "creation_reason": agent_spec.get("reason", "")}
        )

    async def _get_agent_state(self, agent_id: str) -> Dict[str, Any]:
        """Get current state of an agent"""
        agent = self.active_agents.get(agent_id)
        metrics = self.agent_metrics.get(agent_id)
        evolution_plan = self.evolution_plans.get(agent_id)

        return {
            "agent_id": agent_id,
            "specification": agent.__dict__ if agent else {},
            "metrics": metrics.__dict__ if metrics else {},
            "evolution_plan": evolution_plan.__dict__ if evolution_plan else {},
            "last_updated": datetime.now().isoformat()
        }

    def _determine_lifecycle_stage(self, agent_state: Dict[str, Any]) -> AgentLifecycleStage:
        """Determine current lifecycle stage of an agent"""
        metrics = agent_state.get("metrics", {})
        evolution_plan = agent_state.get("evolution_plan", {})

        # Check if agent has metrics (deployed and running)
        if metrics:
            overall_score = metrics.get("overall_score", 0.0)

            if overall_score < 0.5:
                return AgentLifecycleStage.RETIREMENT
            elif overall_score < 0.7 and evolution_plan:
                return AgentLifecycleStage.EVOLUTION
            elif overall_score >= 0.8:
                return AgentLifecycleStage.MONITORING
            else:
                return AgentLifecycleStage.OPTIMIZATION

        # Check if agent has evolution plan (being optimized)
        elif evolution_plan:
            return AgentLifecycleStage.OPTIMIZATION

        # Default to deployment stage
        return AgentLifecycleStage.DEPLOYMENT

    async def _perform_stage_monitoring(self, agent_id: str, stage: AgentLifecycleStage) -> Dict[str, Any]:
        """Perform monitoring specific to lifecycle stage"""
        monitoring_data = {
            "agent_id": agent_id,
            "stage": stage.value,
            "timestamp": datetime.now().isoformat(),
            "health_status": "healthy",
            "performance_indicators": {},
            "alerts": []
        }

        if stage == AgentLifecycleStage.DEPLOYMENT:
            # Monitor deployment readiness
            monitoring_data["performance_indicators"] = {
                "deployment_readiness": 0.9,
                "configuration_completeness": 0.95,
                "dependency_satisfaction": 0.9
            }

        elif stage == AgentLifecycleStage.MONITORING:
            # Monitor operational performance
            metrics = self.agent_metrics.get(agent_id)
            if metrics:
                monitoring_data["performance_indicators"] = {
                    "overall_performance": metrics.overall_score,
                    "efficiency": metrics.efficiency_score,
                    "error_rate": metrics.error_rate,
                    "success_rate": metrics.success_rate
                }

                # Check for performance alerts
                if metrics.overall_score < 0.8:
                    monitoring_data["alerts"].append("Performance below optimal threshold")
                if metrics.error_rate > 0.1:
                    monitoring_data["alerts"].append("High error rate detected")

        elif stage == AgentLifecycleStage.OPTIMIZATION:
            # Monitor optimization progress
            evolution_plan = self.evolution_plans.get(agent_id)
            if evolution_plan:
                monitoring_data["performance_indicators"] = {
                    "optimization_progress": 0.5,  # Would be calculated based on actual progress
                    "expected_improvement": sum(evolution_plan.expected_improvements.values()) / len(evolution_plan.expected_improvements) if evolution_plan.expected_improvements else 0.0
                }

        elif stage == AgentLifecycleStage.EVOLUTION:
            # Monitor evolution process
            monitoring_data["performance_indicators"] = {
                "evolution_progress": 0.3,  # Would be calculated based on actual progress
                "adaptation_success": 0.8
            }

        return monitoring_data

    async def _check_lifecycle_transitions(self, agent_id: str, current_stage: AgentLifecycleStage,
                                         monitoring_data: Dict[str, Any]) -> AgentLifecycleStage:
        """Check if agent should transition to a different lifecycle stage"""
        performance_indicators = monitoring_data.get("performance_indicators", {})
        alerts = monitoring_data.get("alerts", [])

        if current_stage == AgentLifecycleStage.DEPLOYMENT:
            # Check if ready for monitoring
            readiness = performance_indicators.get("deployment_readiness", 0.0)
            if readiness >= 0.9:
                return AgentLifecycleStage.MONITORING

        elif current_stage == AgentLifecycleStage.MONITORING:
            # Check if optimization needed
            performance = performance_indicators.get("overall_performance", 0.0)
            if performance < 0.7 or len(alerts) > 2:
                return AgentLifecycleStage.OPTIMIZATION
            elif performance < 0.5:
                return AgentLifecycleStage.RETIREMENT

        elif current_stage == AgentLifecycleStage.OPTIMIZATION:
            # Check if optimization complete
            progress = performance_indicators.get("optimization_progress", 0.0)
            if progress >= 0.9:
                return AgentLifecycleStage.MONITORING

        elif current_stage == AgentLifecycleStage.EVOLUTION:
            # Check if evolution complete
            progress = performance_indicators.get("evolution_progress", 0.0)
            if progress >= 0.9:
                return AgentLifecycleStage.MONITORING

        return current_stage

    async def _transition_agent_stage(self, agent_id: str, from_stage: AgentLifecycleStage,
                                    to_stage: AgentLifecycleStage):
        """Transition agent from one lifecycle stage to another"""
        logger.info(f"Transitioning agent {agent_id} from {from_stage.value} to {to_stage.value}")

        # Perform stage-specific transition actions
        if to_stage == AgentLifecycleStage.OPTIMIZATION:
            # Trigger optimization process
            await self._initiate_agent_optimization(agent_id)

        elif to_stage == AgentLifecycleStage.EVOLUTION:
            # Trigger evolution process
            await self._initiate_agent_evolution(agent_id)

        elif to_stage == AgentLifecycleStage.RETIREMENT:
            # Prepare for retirement
            await self._prepare_agent_retirement(agent_id)

        # Update agent context with stage transition
        if agent_id in self.active_agents:
            agent = self.active_agents[agent_id]
            agent.context["lifecycle_stage"] = to_stage.value
            agent.context["last_transition"] = datetime.now().isoformat()

    def _assess_lifecycle_health(self, monitoring_data: Dict[str, Any]) -> str:
        """Assess overall health of agent lifecycle"""
        performance_indicators = monitoring_data.get("performance_indicators", {})
        alerts = monitoring_data.get("alerts", [])

        # Calculate health score
        health_score = 1.0

        for indicator, value in performance_indicators.items():
            if value < 0.5:
                health_score -= 0.3
            elif value < 0.7:
                health_score -= 0.1

        # Reduce score for alerts
        health_score -= len(alerts) * 0.1

        # Determine health status
        if health_score >= 0.8:
            return "excellent"
        elif health_score >= 0.6:
            return "good"
        elif health_score >= 0.4:
            return "fair"
        else:
            return "poor"

    async def _initiate_agent_optimization(self, agent_id: str):
        """Initiate optimization process for an agent"""
        logger.info(f"Initiating optimization for agent {agent_id}")

        # Get current metrics
        metrics = self.agent_metrics.get(agent_id)
        if metrics:
            performance_data = {
                "current_metrics": metrics.__dict__,
                "optimization_trigger": "lifecycle_transition",
                "priority": "medium"
            }

            # Determine optimization strategy
            strategy = OptimizationStrategy.PERFORMANCE
            if metrics.efficiency_score < metrics.accuracy_score:
                strategy = OptimizationStrategy.EFFICIENCY
            elif metrics.accuracy_score < 0.7:
                strategy = OptimizationStrategy.ACCURACY

            # Schedule optimization
            try:
                await self.optimize_agent(agent_id, strategy, performance_data)
            except Exception as e:
                logger.error(f"Failed to initiate optimization for agent {agent_id}: {e}")

    async def _initiate_agent_evolution(self, agent_id: str):
        """Initiate evolution process for an agent"""
        logger.info(f"Initiating evolution for agent {agent_id}")
        # Evolution logic would be implemented here
        pass

    async def _prepare_agent_retirement(self, agent_id: str):
        """Prepare agent for retirement"""
        logger.info(f"Preparing retirement for agent {agent_id}")

        # Mark agent for retirement
        if agent_id in self.active_agents:
            agent = self.active_agents[agent_id]
            agent.context["retirement_scheduled"] = True
            agent.context["retirement_timestamp"] = datetime.now().isoformat()


# Archon Integration Executor for orchestrator integration
class ArchonIntegrationExecutor:
    """Archon Integration executor for orchestrator integration"""

    def __init__(self):
        self.archon_integration = None

    async def execute_agent_generation(self, project_id: str, project_prompt: str,
                                     project_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute agent team generation as part of orchestrator workflow"""
        try:
            logger.info(f"Executing agent generation for project {project_id}")

            # Initialize Archon integration
            async with ArchonIntegration() as archon:
                self.archon_integration = archon

                # Generate agent team
                team_composition = await archon.generate_agent_team(
                    project_prompt,
                    project_context,
                    team_size=project_context.get("team_size", 4)
                )

                # Format results for orchestrator
                return {
                    "success": True,
                    "project_id": project_id,
                    "team_id": team_composition.team_id,
                    "agent_count": len(team_composition.agents),
                    "agents": [
                        {
                            "agent_id": agent.agent_id,
                            "agent_type": agent.agent_type.value,
                            "name": agent.name,
                            "description": agent.description,
                            "capabilities": agent.capabilities,
                            "tools": agent.tools
                        }
                        for agent in team_composition.agents
                    ],
                    "coordination_strategy": team_composition.coordination_strategy,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Agent generation execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }
