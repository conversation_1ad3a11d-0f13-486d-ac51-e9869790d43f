import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);

export interface FileOperation {
  type: 'create' | 'modify' | 'delete' | 'move' | 'copy';
  path: string;
  content?: string;
  newPath?: string;
  encoding?: BufferEncoding;
}

export interface WorkspaceInfo {
  rootPath: string;
  folders: readonly vscode.WorkspaceFolder[];
  activeEditor?: vscode.TextEditor;
  openFiles: string[];
}

export interface FileWatcherOptions {
  pattern: string;
  ignoreCreateEvents?: boolean;
  ignoreChangeEvents?: boolean;
  ignoreDeleteEvents?: boolean;
}

export class WorkspaceManager {
  private watchers: Map<string, vscode.FileSystemWatcher> = new Map();
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
  }

  /**
   * Get comprehensive workspace information
   */
  getWorkspaceInfo(): WorkspaceInfo | null {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      return null;
    }

    const rootPath = workspaceFolders[0].uri.fsPath;
    const activeEditor = vscode.window.activeTextEditor;
    const openFiles = vscode.workspace.textDocuments.map(doc => doc.fileName);

    return {
      rootPath,
      folders: [...workspaceFolders],
      activeEditor,
      openFiles
    };
  }

  /**
   * Create a new file with content
   */
  async createFile(
    relativePath: string, 
    content: string = '', 
    encoding: BufferEncoding = 'utf8',
    overwrite: boolean = false
  ): Promise<vscode.Uri> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    const fileUri = vscode.Uri.file(fullPath);

    // Check if file exists
    if (!overwrite && fs.existsSync(fullPath)) {
      throw new Error(`File already exists: ${relativePath}`);
    }

    // Ensure directory exists
    const dirPath = path.dirname(fullPath);
    await mkdir(dirPath, { recursive: true });

    // Write file
    await writeFile(fullPath, content, encoding);

    // Notify VS Code about the new file
    await vscode.workspace.fs.stat(fileUri);

    return fileUri;
  }

  /**
   * Read file content
   */
  async readFile(relativePath: string, encoding: BufferEncoding = 'utf8'): Promise<string> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File does not exist: ${relativePath}`);
    }

    return await readFile(fullPath, encoding);
  }

  /**
   * Modify existing file content
   */
  async modifyFile(
    relativePath: string, 
    content: string, 
    encoding: BufferEncoding = 'utf8'
  ): Promise<vscode.Uri> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    const fileUri = vscode.Uri.file(fullPath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`File does not exist: ${relativePath}`);
    }

    // Write updated content
    await writeFile(fullPath, content, encoding);

    // Refresh the file in VS Code if it's open
    const document = vscode.workspace.textDocuments.find(doc => 
      doc.fileName === fullPath
    );

    if (document && document.isDirty) {
      await document.save();
    }

    return fileUri;
  }

  /**
   * Delete a file or directory
   */
  async deleteFile(relativePath: string, recursive: boolean = false): Promise<void> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File does not exist: ${relativePath}`);
    }

    const stats = await stat(fullPath);
    
    if (stats.isDirectory()) {
      if (recursive) {
        await this.deleteDirectoryRecursive(fullPath);
      } else {
        await rmdir(fullPath);
      }
    } else {
      await unlink(fullPath);
    }
  }

  /**
   * Move/rename a file
   */
  async moveFile(oldPath: string, newPath: string): Promise<vscode.Uri> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const oldFullPath = path.join(workspaceInfo.rootPath, oldPath);
    const newFullPath = path.join(workspaceInfo.rootPath, newPath);
    const newUri = vscode.Uri.file(newFullPath);

    if (!fs.existsSync(oldFullPath)) {
      throw new Error(`Source file does not exist: ${oldPath}`);
    }

    // Ensure target directory exists
    const newDir = path.dirname(newFullPath);
    await mkdir(newDir, { recursive: true });

    // Use VS Code's file system API for proper move operation
    const oldUri = vscode.Uri.file(oldFullPath);
    await vscode.workspace.fs.rename(oldUri, newUri);

    return newUri;
  }

  /**
   * Copy a file
   */
  async copyFile(sourcePath: string, targetPath: string): Promise<vscode.Uri> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const sourceFullPath = path.join(workspaceInfo.rootPath, sourcePath);
    const targetFullPath = path.join(workspaceInfo.rootPath, targetPath);
    const targetUri = vscode.Uri.file(targetFullPath);

    if (!fs.existsSync(sourceFullPath)) {
      throw new Error(`Source file does not exist: ${sourcePath}`);
    }

    // Ensure target directory exists
    const targetDir = path.dirname(targetFullPath);
    await mkdir(targetDir, { recursive: true });

    // Use VS Code's file system API for proper copy operation
    const sourceUri = vscode.Uri.file(sourceFullPath);
    await vscode.workspace.fs.copy(sourceUri, targetUri);

    return targetUri;
  }

  /**
   * List directory contents
   */
  async listDirectory(relativePath: string = ''): Promise<string[]> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Directory does not exist: ${relativePath}`);
    }

    const stats = await stat(fullPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path is not a directory: ${relativePath}`);
    }

    return await readdir(fullPath);
  }

  /**
   * Check if file or directory exists
   */
  fileExists(relativePath: string): boolean {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      return false;
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    return fs.existsSync(fullPath);
  }

  /**
   * Get file stats
   */
  async getFileStats(relativePath: string): Promise<fs.Stats> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    return await stat(fullPath);
  }

  /**
   * Create directory
   */
  async createDirectory(relativePath: string, recursive: boolean = true): Promise<vscode.Uri> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    const dirUri = vscode.Uri.file(fullPath);

    await mkdir(fullPath, { recursive });

    return dirUri;
  }

  /**
   * Recursively delete directory
   */
  private async deleteDirectoryRecursive(dirPath: string): Promise<void> {
    const entries = await readdir(dirPath);

    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry);
      const stats = await stat(entryPath);

      if (stats.isDirectory()) {
        await this.deleteDirectoryRecursive(entryPath);
      } else {
        await unlink(entryPath);
      }
    }

    await rmdir(dirPath);
  }

  /**
   * Execute batch file operations
   */
  async executeBatchOperations(operations: FileOperation[]): Promise<vscode.Uri[]> {
    const results: vscode.Uri[] = [];

    for (const operation of operations) {
      try {
        let result: vscode.Uri;

        switch (operation.type) {
          case 'create':
            result = await this.createFile(
              operation.path,
              operation.content || '',
              operation.encoding || 'utf8'
            );
            break;

          case 'modify':
            result = await this.modifyFile(
              operation.path,
              operation.content || '',
              operation.encoding || 'utf8'
            );
            break;

          case 'delete':
            await this.deleteFile(operation.path, true);
            result = vscode.Uri.file(operation.path);
            break;

          case 'move':
            if (!operation.newPath) {
              throw new Error('newPath is required for move operation');
            }
            result = await this.moveFile(operation.path, operation.newPath);
            break;

          case 'copy':
            if (!operation.newPath) {
              throw new Error('newPath is required for copy operation');
            }
            result = await this.copyFile(operation.path, operation.newPath);
            break;

          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }

        results.push(result);
      } catch (error) {
        vscode.window.showErrorMessage(`Failed to execute ${operation.type} operation on ${operation.path}: ${error}`);
        throw error;
      }
    }

    return results;
  }

  /**
   * Setup file system watcher
   */
  setupFileWatcher(
    id: string,
    options: FileWatcherOptions,
    callbacks: {
      onCreate?: (uri: vscode.Uri) => void;
      onChange?: (uri: vscode.Uri) => void;
      onDelete?: (uri: vscode.Uri) => void;
    }
  ): vscode.FileSystemWatcher {
    // Dispose existing watcher if it exists
    if (this.watchers.has(id)) {
      this.watchers.get(id)?.dispose();
    }

    const watcher = vscode.workspace.createFileSystemWatcher(
      options.pattern,
      options.ignoreCreateEvents,
      options.ignoreChangeEvents,
      options.ignoreDeleteEvents
    );

    if (callbacks.onCreate && !options.ignoreCreateEvents) {
      watcher.onDidCreate(callbacks.onCreate);
    }

    if (callbacks.onChange && !options.ignoreChangeEvents) {
      watcher.onDidChange(callbacks.onChange);
    }

    if (callbacks.onDelete && !options.ignoreDeleteEvents) {
      watcher.onDidDelete(callbacks.onDelete);
    }

    this.watchers.set(id, watcher);
    this.context.subscriptions.push(watcher);

    return watcher;
  }

  /**
   * Remove file watcher
   */
  removeFileWatcher(id: string): void {
    const watcher = this.watchers.get(id);
    if (watcher) {
      watcher.dispose();
      this.watchers.delete(id);
    }
  }

  /**
   * Open file in editor
   */
  async openFile(relativePath: string, options?: vscode.TextDocumentShowOptions): Promise<vscode.TextEditor> {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      throw new Error('No workspace folder is open');
    }

    const fullPath = path.join(workspaceInfo.rootPath, relativePath);
    const fileUri = vscode.Uri.file(fullPath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`File does not exist: ${relativePath}`);
    }

    const document = await vscode.workspace.openTextDocument(fileUri);
    return await vscode.window.showTextDocument(document, options);
  }

  /**
   * Insert text at specific position in file
   */
  async insertTextAtPosition(
    relativePath: string,
    position: vscode.Position,
    text: string
  ): Promise<boolean> {
    const editor = await this.openFile(relativePath);

    return await editor.edit(editBuilder => {
      editBuilder.insert(position, text);
    });
  }

  /**
   * Replace text in file
   */
  async replaceTextInFile(
    relativePath: string,
    range: vscode.Range,
    text: string
  ): Promise<boolean> {
    const editor = await this.openFile(relativePath);

    return await editor.edit(editBuilder => {
      editBuilder.replace(range, text);
    });
  }

  /**
   * Find and replace text in file
   */
  async findAndReplace(
    relativePath: string,
    searchText: string,
    replaceText: string,
    replaceAll: boolean = false
  ): Promise<number> {
    const content = await this.readFile(relativePath);
    let replacements = 0;

    if (replaceAll) {
      const newContent = content.replace(new RegExp(searchText, 'g'), replaceText);
      replacements = (content.match(new RegExp(searchText, 'g')) || []).length;
      await this.modifyFile(relativePath, newContent);
    } else {
      const newContent = content.replace(searchText, replaceText);
      replacements = content.includes(searchText) ? 1 : 0;
      await this.modifyFile(relativePath, newContent);
    }

    return replacements;
  }

  /**
   * Get workspace relative path from absolute path
   */
  getRelativePath(absolutePath: string): string | null {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      return null;
    }

    return path.relative(workspaceInfo.rootPath, absolutePath);
  }

  /**
   * Get absolute path from workspace relative path
   */
  getAbsolutePath(relativePath: string): string | null {
    const workspaceInfo = this.getWorkspaceInfo();
    if (!workspaceInfo) {
      return null;
    }

    return path.join(workspaceInfo.rootPath, relativePath);
  }

  /**
   * Dispose all watchers
   */
  dispose(): void {
    this.watchers.forEach(watcher => watcher.dispose());
    this.watchers.clear();
  }
}
