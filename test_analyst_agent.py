#!/usr/bin/env python3
"""
Test script for the Analyst Agent
Demonstrates the comprehensive analysis and documentation generation capabilities
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from analyst_agent import AnalystAgent, ProjectSpecification

async def test_analyst_agent():
    """Test the analyst agent with various project types"""
    
    test_cases = [
        {
            "prompt": "Create a task management web application with user authentication, project boards, and real-time collaboration features",
            "project_type": "web_application",
            "output_dir": "test_output/task_manager"
        },
        {
            "prompt": "Build a REST API for a blog platform with user management, post creation, commenting, and content moderation",
            "project_type": "api_service", 
            "output_dir": "test_output/blog_api"
        },
        {
            "prompt": "Develop a mobile fitness tracking app with workout logging, progress analytics, and social features",
            "project_type": "mobile_application",
            "output_dir": "test_output/fitness_app"
        }
    ]
    
    analyst = AnalystAgent()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {test_case['project_type'].upper()}")
        print(f"{'='*60}")
        print(f"Prompt: {test_case['prompt']}")
        print(f"Output: {test_case['output_dir']}")
        
        try:
            # Analyze the prompt
            print("\n🔍 Starting analysis...")
            specification = await analyst.analyze_prompt(
                test_case['prompt'], 
                test_case['project_type']
            )
            
            print(f"✅ Analysis complete!")
            print(f"   Project Name: {specification.project_name}")
            print(f"   User Stories: {len(specification.user_stories)}")
            print(f"   Functional Requirements: {len(specification.requirements.get('functional', []))}")
            print(f"   Non-Functional Requirements: {len(specification.requirements.get('non_functional', []))}")
            print(f"   Success Metrics: {len(specification.success_metrics)}")
            
            # Generate documentation
            print("\n📄 Generating documentation...")
            output_path = Path(test_case['output_dir'])
            generated_files = await analyst.generate_project_documents(specification, output_path)
            
            print(f"✅ Generated {len(generated_files)} files:")
            for file in generated_files:
                print(f"   - {file}")
            
            # Display sample user story
            if specification.user_stories:
                story = specification.user_stories[0]
                print(f"\n📋 Sample User Story:")
                print(f"   {story.get('id', 'US-001')}: {story.get('title', 'Sample Story')}")
                print(f"   As a {story.get('role', 'user')}")
                print(f"   I want {story.get('action', 'to perform an action')}")
                print(f"   So that {story.get('benefit', 'I can achieve my goal')}")
                print(f"   Priority: {story.get('priority', 'Medium')} | Points: {story.get('story_points', 'TBD')}")
            
            # Display technical stack summary
            print(f"\n🛠️ Technical Stack Summary:")
            frontend = specification.technical_stack.get('frontend', {})
            backend = specification.technical_stack.get('backend', {})
            database = specification.technical_stack.get('database', {})
            
            if frontend:
                print(f"   Frontend: {frontend.get('framework', 'N/A')} + {frontend.get('language', 'N/A')}")
            if backend:
                print(f"   Backend: {backend.get('framework', 'N/A')} + {backend.get('language', 'N/A')}")
            if database:
                print(f"   Database: {database.get('primary', 'N/A')} + {database.get('orm', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Test case {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎉 All test cases completed!")
    print("📁 Check the test_output/ directory for generated documentation")
    print(f"{'='*60}")

async def test_mcp_integration():
    """Test MCP-RAG integration capabilities"""
    print("\n🔬 Testing MCP-RAG Integration...")
    
    analyst = AnalystAgent()
    
    # Test MCP client connectivity
    async with analyst.mcp_client:
        print("📡 Testing MCP-RAG connectivity...")
        
        # Test getting available sources
        sources = await analyst.mcp_client.get_available_sources()
        print(f"   Available sources: {len(sources)} found")
        if sources:
            print(f"   Sample sources: {sources[:3]}")
        
        # Test RAG query
        print("🔍 Testing RAG query...")
        rag_result = await analyst.mcp_client.perform_rag_query(
            "web development best practices",
            match_count=3
        )
        
        if rag_result.get("results"):
            print(f"   RAG query successful: {len(rag_result['results'])} results")
        else:
            print(f"   RAG query returned no results (service may be unavailable)")
        
        # Test code search
        print("💻 Testing code search...")
        code_result = await analyst.mcp_client.search_code_examples(
            "React component example",
            match_count=2
        )
        
        if code_result.get("results"):
            print(f"   Code search successful: {len(code_result['results'])} examples")
        else:
            print(f"   Code search returned no results (service may be unavailable)")

def print_usage():
    """Print usage information"""
    print("""
🤖 Analyst Agent Test Suite

Usage:
    python test_analyst_agent.py [command]

Commands:
    test        Run comprehensive test cases (default)
    mcp         Test MCP-RAG integration only
    quick       Run a single quick test
    help        Show this help message

Examples:
    python test_analyst_agent.py test
    python test_analyst_agent.py mcp
    python test_analyst_agent.py quick

The analyst agent will:
1. 🔍 Analyze natural language prompts
2. 🔬 Conduct research using MCP-RAG (if available)
3. 📋 Extract requirements and generate user stories
4. 🏗️ Define technical architecture
5. 📄 Generate comprehensive project documentation

Generated files include:
- Project Brief
- Requirements Document  
- User Stories
- Technical Specification
- Architecture Document
- Project README
""")

async def quick_test():
    """Run a quick test with a simple prompt"""
    print("🚀 Running quick test...")
    
    analyst = AnalystAgent()
    prompt = "Create a simple todo list web app with user accounts"
    
    try:
        specification = await analyst.analyze_prompt(prompt, "web_application")
        output_path = Path("quick_test_output")
        generated_files = await analyst.generate_project_documents(specification, output_path)
        
        print(f"✅ Quick test complete!")
        print(f"   Project: {specification.project_name}")
        print(f"   Files: {len(generated_files)} generated")
        print(f"   Output: {output_path}")
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "mcp":
        await test_mcp_integration()
    elif command == "quick":
        await quick_test()
    elif command == "test":
        await test_analyst_agent()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
