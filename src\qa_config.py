"""
Configuration for the QA Agent
Defines quality gates, test configurations, and validation rules
"""

from dataclasses import dataclass
from typing import Dict, List, Any
from enum import Enum

class QualityLevel(Enum):
    """Quality assurance levels"""
    BASIC = "basic"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    ENTERPRISE = "enterprise"

@dataclass
class QAConfig:
    """Configuration for the QA Agent"""
    
    # MCP-RAG Configuration
    mcp_url: str = "http://localhost:8051"
    mcp_timeout: int = 60
    mcp_enabled: bool = True
    
    # OpenAI Configuration
    openai_api_key: str = None
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 4000
    
    # Test Execution Configuration
    default_quality_level: str = "standard"
    enable_unit_tests: bool = True
    enable_integration_tests: bool = True
    enable_e2e_tests: bool = True
    enable_security_tests: bool = True
    enable_performance_tests: bool = True
    
    # Coverage Configuration
    min_coverage_basic: float = 60.0
    min_coverage_standard: float = 80.0
    min_coverage_comprehensive: float = 90.0
    min_coverage_enterprise: float = 95.0
    
    # Quality Gates
    max_critical_issues: int = 0
    max_high_issues: int = 2
    max_medium_issues: int = 10
    
    # Test Timeouts
    unit_test_timeout: int = 300  # 5 minutes
    integration_test_timeout: int = 600  # 10 minutes
    e2e_test_timeout: int = 1800  # 30 minutes
    
    # Security Configuration
    enable_security_scan: bool = True
    security_scan_timeout: int = 600
    
    # Performance Configuration
    enable_performance_analysis: bool = True
    performance_threshold_ms: int = 2000
    
    @staticmethod
    def get_quality_gates(quality_level: QualityLevel) -> Dict[str, Any]:
        """Get quality gates for a specific quality level"""
        gates = {
            QualityLevel.BASIC: {
                "min_coverage": 60.0,
                "max_critical_issues": 5,
                "max_high_issues": 10,
                "max_medium_issues": 20,
                "required_tests": ["unit"],
                "security_required": False,
                "performance_required": False
            },
            QualityLevel.STANDARD: {
                "min_coverage": 80.0,
                "max_critical_issues": 2,
                "max_high_issues": 5,
                "max_medium_issues": 10,
                "required_tests": ["unit", "integration"],
                "security_required": True,
                "performance_required": False
            },
            QualityLevel.COMPREHENSIVE: {
                "min_coverage": 90.0,
                "max_critical_issues": 0,
                "max_high_issues": 2,
                "max_medium_issues": 5,
                "required_tests": ["unit", "integration", "e2e"],
                "security_required": True,
                "performance_required": True
            },
            QualityLevel.ENTERPRISE: {
                "min_coverage": 95.0,
                "max_critical_issues": 0,
                "max_high_issues": 0,
                "max_medium_issues": 2,
                "required_tests": ["unit", "integration", "e2e", "performance", "security"],
                "security_required": True,
                "performance_required": True,
                "accessibility_required": True
            }
        }
        return gates.get(quality_level, gates[QualityLevel.STANDARD])
    
    @staticmethod
    def get_test_framework_config(framework: str) -> Dict[str, Any]:
        """Get configuration for specific test frameworks"""
        configs = {
            "jest": {
                "command": "npm test",
                "coverage_command": "npm run test:coverage",
                "config_file": "jest.config.js",
                "test_pattern": "**/*.test.{js,ts,jsx,tsx}",
                "coverage_threshold": 80,
                "setup_files": ["jest.setup.js"],
                "test_environment": "jsdom",
                "collect_coverage_from": [
                    "src/**/*.{js,ts,jsx,tsx}",
                    "!src/**/*.d.ts",
                    "!src/index.tsx",
                    "!src/serviceWorker.ts"
                ]
            },
            "playwright": {
                "command": "npx playwright test",
                "config_file": "playwright.config.ts",
                "test_pattern": "**/*.spec.{js,ts}",
                "browsers": ["chromium", "firefox", "webkit"],
                "headless": True,
                "screenshot": "only-on-failure",
                "video": "retain-on-failure",
                "trace": "retain-on-failure"
            },
            "cypress": {
                "command": "npx cypress run",
                "config_file": "cypress.config.js",
                "test_pattern": "cypress/e2e/**/*.cy.{js,ts}",
                "browser": "chrome",
                "headless": True,
                "video": True,
                "screenshot": True
            },
            "pytest": {
                "command": "pytest",
                "coverage_command": "pytest --cov",
                "config_file": "pytest.ini",
                "test_pattern": "test_*.py",
                "coverage_threshold": 80,
                "markers": ["unit", "integration", "e2e"]
            },
            "vitest": {
                "command": "npx vitest run",
                "coverage_command": "npx vitest run --coverage",
                "config_file": "vitest.config.ts",
                "test_pattern": "**/*.{test,spec}.{js,ts,jsx,tsx}",
                "coverage_threshold": 80
            }
        }
        return configs.get(framework, {})
    
    @staticmethod
    def get_security_rules() -> Dict[str, List[str]]:
        """Get security validation rules"""
        return {
            "authentication": [
                "JWT implementation present",
                "Password hashing implemented",
                "Session management configured",
                "Multi-factor authentication support"
            ],
            "authorization": [
                "Role-based access control",
                "Permission validation",
                "Resource-level authorization",
                "API endpoint protection"
            ],
            "input_validation": [
                "Input sanitization",
                "SQL injection prevention",
                "XSS protection",
                "CSRF protection",
                "File upload validation"
            ],
            "data_protection": [
                "Sensitive data encryption",
                "Secure data transmission",
                "Data masking in logs",
                "PII protection"
            ],
            "security_headers": [
                "Content Security Policy",
                "X-Frame-Options",
                "X-XSS-Protection",
                "Strict-Transport-Security",
                "X-Content-Type-Options"
            ]
        }
    
    @staticmethod
    def get_performance_rules() -> Dict[str, List[str]]:
        """Get performance validation rules"""
        return {
            "response_time": [
                "API response time < 2s",
                "Page load time < 3s",
                "Database query time < 100ms",
                "Cache hit ratio > 80%"
            ],
            "resource_optimization": [
                "Image optimization",
                "CSS/JS minification",
                "Gzip compression enabled",
                "CDN usage for static assets"
            ],
            "caching": [
                "HTTP caching headers",
                "Application-level caching",
                "Database query caching",
                "Static asset caching"
            ],
            "scalability": [
                "Connection pooling",
                "Load balancing ready",
                "Horizontal scaling support",
                "Resource monitoring"
            ]
        }
    
    @staticmethod
    def get_code_quality_rules() -> Dict[str, List[str]]:
        """Get code quality validation rules"""
        return {
            "syntax_and_style": [
                "No syntax errors",
                "Consistent code formatting",
                "Proper indentation",
                "Naming conventions followed"
            ],
            "error_handling": [
                "Try-catch blocks implemented",
                "Error logging present",
                "Graceful error responses",
                "Error boundary components (React)"
            ],
            "documentation": [
                "Function documentation",
                "API documentation",
                "README file present",
                "Code comments for complex logic"
            ],
            "maintainability": [
                "DRY principle followed",
                "SOLID principles applied",
                "Proper separation of concerns",
                "Modular code structure"
            ],
            "testing": [
                "Unit tests present",
                "Integration tests present",
                "Test coverage adequate",
                "Test quality high"
            ]
        }
    
    @staticmethod
    def get_accessibility_rules() -> Dict[str, List[str]]:
        """Get accessibility validation rules"""
        return {
            "semantic_html": [
                "Proper HTML semantics",
                "Heading hierarchy",
                "Form labels",
                "Alt text for images"
            ],
            "keyboard_navigation": [
                "Tab order logical",
                "Focus indicators visible",
                "Keyboard shortcuts",
                "Skip links present"
            ],
            "screen_reader": [
                "ARIA labels",
                "ARIA roles",
                "Screen reader compatibility",
                "Text alternatives"
            ],
            "visual_design": [
                "Color contrast ratio",
                "Text scaling support",
                "Responsive design",
                "Motion preferences"
            ]
        }
    
    @staticmethod
    def get_validation_patterns() -> Dict[str, List[str]]:
        """Get validation patterns for different technologies"""
        return {
            "javascript": [
                r"test\s*\(",
                r"describe\s*\(",
                r"it\s*\(",
                r"expect\s*\(",
                r"assert\s*\("
            ],
            "typescript": [
                r"test\s*\(",
                r"describe\s*\(",
                r"it\s*\(",
                r"expect\s*\(",
                r"interface\s+\w+",
                r"type\s+\w+\s*="
            ],
            "react": [
                r"import\s+React",
                r"useState\s*\(",
                r"useEffect\s*\(",
                r"render\s*\(",
                r"screen\."
            ],
            "express": [
                r"app\.(get|post|put|delete)",
                r"router\.(get|post|put|delete)",
                r"middleware",
                r"req\.",
                r"res\."
            ],
            "security": [
                r"helmet\s*\(",
                r"cors\s*\(",
                r"bcrypt\.",
                r"jwt\.",
                r"authenticate"
            ]
        }
