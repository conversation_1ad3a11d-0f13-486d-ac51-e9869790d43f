# Integration Guide: Connecting Aetherforge with External Systems

This comprehensive guide covers integrating Aetherforge with external systems, services, and tools to create powerful development workflows.

## 🌐 Integration Architecture

### Integration Patterns

```mermaid
graph TB
    subgraph "Aetherforge Core"
        O[Orchestrator]
        API[REST API]
        WE[Workflow Engine]
        AS[Agent System]
    end

    subgraph "Integration Layer"
        CA[Component Adapters]
        WH[Webhooks]
        SDK[SDKs]
        CLI[CLI Tools]
    end

    subgraph "External Systems"
        CI[CI/CD Systems]
        VCS[Version Control]
        CLOUD[Cloud Providers]
        DB[Databases]
        TOOLS[Dev Tools]
        APIS[Third-party APIs]
    end

    O --> CA
    API --> WH
    WE --> SDK
    AS --> CLI

    CA --> CI
    CA --> VCS
    CA --> CLOUD
    CA --> DB
    WH --> TOOLS
    SDK --> APIS
```

### Integration Types

| Type | Purpose | Examples | Complexity |
|------|---------|----------|------------|
| **API Integration** | Direct service communication | REST APIs, GraphQL | Low |
| **Webhook Integration** | Event-driven communication | GitHub webhooks, Slack notifications | Medium |
| **Database Integration** | Data persistence and retrieval | PostgreSQL, MongoDB, Redis | Medium |
| **Cloud Integration** | Cloud service utilization | AWS, Azure, GCP | High |
| **CI/CD Integration** | Deployment automation | Jenkins, GitHub Actions, GitLab CI | High |
| **Tool Integration** | Development tool connectivity | IDEs, monitoring, analytics | Medium |

## 🔧 API Integration

### REST API Integration

```python
# integrations/api/rest_client.py
import aiohttp
import asyncio
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

class AuthType(Enum):
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"

@dataclass
class APIConfig:
    base_url: str
    auth_type: AuthType
    credentials: Dict[str, str]
    timeout: int = 30
    retry_count: int = 3
    rate_limit: Optional[int] = None

class RESTAPIIntegration:
    """Generic REST API integration client"""

    def __init__(self, config: APIConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = None

        if config.rate_limit:
            self.rate_limiter = asyncio.Semaphore(config.rate_limit)

    async def __aenter__(self):
        headers = self._build_headers()
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _build_headers(self) -> Dict[str, str]:
        """Build request headers based on auth type"""
        headers = {"Content-Type": "application/json"}

        if self.config.auth_type == AuthType.API_KEY:
            headers["X-API-Key"] = self.config.credentials["api_key"]
        elif self.config.auth_type == AuthType.BEARER_TOKEN:
            headers["Authorization"] = f"Bearer {self.config.credentials['token']}"
        elif self.config.auth_type == AuthType.BASIC_AUTH:
            import base64
            credentials = f"{self.config.credentials['username']}:{self.config.credentials['password']}"
            encoded = base64.b64encode(credentials.encode()).decode()
            headers["Authorization"] = f"Basic {encoded}"

        return headers

    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        for attempt in range(self.config.retry_count):
            try:
                if self.rate_limiter:
                    async with self.rate_limiter:
                        return await self._make_request(method, url, **kwargs)
                else:
                    return await self._make_request(method, url, **kwargs)

            except aiohttp.ClientError as e:
                if attempt == self.config.retry_count - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    async def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make actual HTTP request"""
        async with self.session.request(method, url, **kwargs) as response:
            response.raise_for_status()

            if response.content_type == 'application/json':
                return await response.json()
            else:
                return {"content": await response.text()}

    # Convenience methods
    async def get(self, endpoint: str, params: Dict = None) -> Dict[str, Any]:
        return await self.request("GET", endpoint, params=params)

    async def post(self, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        return await self.request("POST", endpoint, json=data)

    async def put(self, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        return await self.request("PUT", endpoint, json=data)

    async def delete(self, endpoint: str) -> Dict[str, Any]:
        return await self.request("DELETE", endpoint)
```

### GraphQL Integration

```python
# integrations/api/graphql_client.py
import aiohttp
import json
from typing import Dict, Any, Optional

class GraphQLIntegration:
    """GraphQL API integration client"""

    def __init__(self, endpoint: str, headers: Dict[str, str] = None):
        self.endpoint = endpoint
        self.headers = headers or {}
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(headers=self.headers)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def query(self, query: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute GraphQL query"""
        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        async with self.session.post(self.endpoint, json=payload) as response:
            response.raise_for_status()
            result = await response.json()

            if "errors" in result:
                raise Exception(f"GraphQL errors: {result['errors']}")

            return result.get("data", {})

    async def mutation(self, mutation: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute GraphQL mutation"""
        return await self.query(mutation, variables)
```

## 🔗 Webhook Integration

### Webhook Server

```python
# integrations/webhooks/webhook_server.py
from fastapi import FastAPI, Request, HTTPException
from typing import Dict, Any, Callable, List
import asyncio
import logging
import hmac
import hashlib

logger = logging.getLogger(__name__)

class WebhookServer:
    """Webhook server for receiving external events"""

    def __init__(self, app: FastAPI):
        self.app = app
        self.handlers: Dict[str, List[Callable]] = {}
        self.secrets: Dict[str, str] = {}

        # Register webhook endpoints
        self._setup_routes()

    def _setup_routes(self):
        """Setup webhook routes"""

        @self.app.post("/webhooks/{provider}")
        async def handle_webhook(provider: str, request: Request):
            """Handle incoming webhook"""
            try:
                # Get request body
                body = await request.body()
                headers = dict(request.headers)

                # Verify signature if secret is configured
                if provider in self.secrets:
                    if not self._verify_signature(provider, body, headers):
                        raise HTTPException(status_code=401, detail="Invalid signature")

                # Parse JSON payload
                try:
                    payload = await request.json()
                except:
                    payload = {"raw_body": body.decode()}

                # Execute handlers
                await self._execute_handlers(provider, payload, headers)

                return {"status": "success", "message": "Webhook processed"}

            except Exception as e:
                logger.error(f"Webhook processing failed: {e}")
                raise HTTPException(status_code=500, detail=str(e))

    def register_handler(self, provider: str, handler: Callable):
        """Register webhook handler"""
        if provider not in self.handlers:
            self.handlers[provider] = []
        self.handlers[provider].append(handler)

    def set_secret(self, provider: str, secret: str):
        """Set webhook secret for signature verification"""
        self.secrets[provider] = secret

    def _verify_signature(self, provider: str, body: bytes, headers: Dict[str, str]) -> bool:
        """Verify webhook signature"""
        secret = self.secrets.get(provider)
        if not secret:
            return True

        # GitHub-style signature verification
        if provider == "github":
            signature = headers.get("x-hub-signature-256", "")
            expected = "sha256=" + hmac.new(
                secret.encode(), body, hashlib.sha256
            ).hexdigest()
            return hmac.compare_digest(signature, expected)

        # Add other providers as needed
        return True

    async def _execute_handlers(self, provider: str, payload: Dict[str, Any], headers: Dict[str, str]):
        """Execute registered handlers"""
        if provider not in self.handlers:
            logger.warning(f"No handlers registered for provider: {provider}")
            return

        context = {
            "provider": provider,
            "payload": payload,
            "headers": headers
        }

        for handler in self.handlers[provider]:
            try:
                await handler(context)
            except Exception as e:
                logger.error(f"Handler failed: {e}")
```

### GitHub Integration

```python
# integrations/github/github_integration.py
from integrations.api.rest_client import RESTAPIIntegration, APIConfig, AuthType
from typing import Dict, Any, List

class GitHubIntegration:
    """GitHub API integration"""

    def __init__(self, token: str):
        config = APIConfig(
            base_url="https://api.github.com",
            auth_type=AuthType.BEARER_TOKEN,
            credentials={"token": token}
        )
        self.client = RESTAPIIntegration(config)

    async def __aenter__(self):
        await self.client.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.__aexit__(exc_type, exc_val, exc_tb)

    async def create_repository(self, name: str, description: str = "", private: bool = False) -> Dict[str, Any]:
        """Create a new repository"""
        data = {
            "name": name,
            "description": description,
            "private": private,
            "auto_init": True
        }
        return await self.client.post("user/repos", data)

    async def create_pull_request(self, owner: str, repo: str, title: str, head: str, base: str, body: str = "") -> Dict[str, Any]:
        """Create a pull request"""
        data = {
            "title": title,
            "head": head,
            "base": base,
            "body": body
        }
        return await self.client.post(f"repos/{owner}/{repo}/pulls", data)

    async def create_issue(self, owner: str, repo: str, title: str, body: str = "", labels: List[str] = None) -> Dict[str, Any]:
        """Create an issue"""
        data = {
            "title": title,
            "body": body,
            "labels": labels or []
        }
        return await self.client.post(f"repos/{owner}/{repo}/issues", data)

    async def get_repository_contents(self, owner: str, repo: str, path: str = "") -> Dict[str, Any]:
        """Get repository contents"""
        return await self.client.get(f"repos/{owner}/{repo}/contents/{path}")

    async def create_file(self, owner: str, repo: str, path: str, content: str, message: str, branch: str = "main") -> Dict[str, Any]:
        """Create a file in repository"""
        import base64

        data = {
            "message": message,
            "content": base64.b64encode(content.encode()).decode(),
            "branch": branch
        }
        return await self.client.put(f"repos/{owner}/{repo}/contents/{path}", data)
```

## ☁️ Cloud Provider Integration

### AWS Integration

```python
# integrations/cloud/aws_integration.py
import boto3
import asyncio
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError

class AWSIntegration:
    """AWS services integration"""

    def __init__(self, access_key: str, secret_key: str, region: str = "us-east-1"):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )

    async def deploy_lambda_function(self, function_name: str, code_zip: bytes, handler: str, runtime: str = "python3.9") -> Dict[str, Any]:
        """Deploy Lambda function"""
        lambda_client = self.session.client('lambda')

        try:
            # Create or update function
            try:
                response = lambda_client.update_function_code(
                    FunctionName=function_name,
                    ZipFile=code_zip
                )
            except ClientError as e:
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                    # Function doesn't exist, create it
                    response = lambda_client.create_function(
                        FunctionName=function_name,
                        Runtime=runtime,
                        Role=self._get_lambda_role_arn(),
                        Handler=handler,
                        Code={'ZipFile': code_zip}
                    )
                else:
                    raise

            return response

        except Exception as e:
            raise Exception(f"Lambda deployment failed: {e}")

    async def create_s3_bucket(self, bucket_name: str) -> Dict[str, Any]:
        """Create S3 bucket"""
        s3_client = self.session.client('s3')

        try:
            if self.region == 'us-east-1':
                response = s3_client.create_bucket(Bucket=bucket_name)
            else:
                response = s3_client.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={'LocationConstraint': self.region}
                )

            return response

        except Exception as e:
            raise Exception(f"S3 bucket creation failed: {e}")

    async def deploy_ecs_service(self, service_config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy ECS service"""
        ecs_client = self.session.client('ecs')

        try:
            # Create task definition
            task_def_response = ecs_client.register_task_definition(**service_config['task_definition'])

            # Create or update service
            service_response = ecs_client.create_service(
                cluster=service_config['cluster'],
                serviceName=service_config['service_name'],
                taskDefinition=task_def_response['taskDefinition']['taskDefinitionArn'],
                desiredCount=service_config.get('desired_count', 1)
            )

            return service_response

        except Exception as e:
            raise Exception(f"ECS deployment failed: {e}")

    def _get_lambda_role_arn(self) -> str:
        """Get or create Lambda execution role"""
        # Implementation for getting/creating IAM role
        return "arn:aws:iam::123456789012:role/lambda-execution-role"
```

### Docker Integration

```python
# integrations/docker/docker_integration.py
import docker
import asyncio
from typing import Dict, Any, List, Optional

class DockerIntegration:
    """Docker integration for containerization"""

    def __init__(self):
        self.client = docker.from_env()

    async def build_image(self, dockerfile_path: str, tag: str, build_args: Dict[str, str] = None) -> Dict[str, Any]:
        """Build Docker image"""
        try:
            image, logs = self.client.images.build(
                path=dockerfile_path,
                tag=tag,
                buildargs=build_args or {},
                rm=True
            )

            return {
                "image_id": image.id,
                "tags": image.tags,
                "size": image.attrs['Size'],
                "created": image.attrs['Created']
            }

        except Exception as e:
            raise Exception(f"Docker build failed: {e}")

    async def run_container(self, image: str, name: str, environment: Dict[str, str] = None, ports: Dict[str, int] = None) -> Dict[str, Any]:
        """Run Docker container"""
        try:
            container = self.client.containers.run(
                image=image,
                name=name,
                environment=environment or {},
                ports=ports or {},
                detach=True
            )

            return {
                "container_id": container.id,
                "name": container.name,
                "status": container.status,
                "image": container.image.tags
            }

        except Exception as e:
            raise Exception(f"Container run failed: {e}")

    async def push_image(self, repository: str, tag: str = "latest") -> Dict[str, Any]:
        """Push image to registry"""
        try:
            response = self.client.images.push(repository, tag=tag)
            return {"status": "success", "response": response}

        except Exception as e:
            raise Exception(f"Image push failed: {e}")
```

## 🗄️ Database Integration

### PostgreSQL Integration

```python
# integrations/database/postgresql_integration.py
import asyncpg
import asyncio
from typing import Dict, Any, List, Optional

class PostgreSQLIntegration:
    """PostgreSQL database integration"""

    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.pool: Optional[asyncpg.Pool] = None

    async def __aenter__(self):
        self.pool = await asyncpg.create_pool(self.connection_string)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.pool:
            await self.pool.close()

    async def execute_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute SELECT query"""
        async with self.pool.acquire() as connection:
            rows = await connection.fetch(query, *args)
            return [dict(row) for row in rows]

    async def execute_command(self, command: str, *args) -> str:
        """Execute INSERT/UPDATE/DELETE command"""
        async with self.pool.acquire() as connection:
            return await connection.execute(command, *args)

    async def create_tables_from_schema(self, schema: Dict[str, Any]) -> None:
        """Create tables from schema definition"""
        async with self.pool.acquire() as connection:
            for table_name, table_def in schema.items():
                columns = []
                for col_name, col_def in table_def['columns'].items():
                    column_sql = f"{col_name} {col_def['type']}"
                    if col_def.get('primary_key'):
                        column_sql += " PRIMARY KEY"
                    if col_def.get('not_null'):
                        column_sql += " NOT NULL"
                    if col_def.get('unique'):
                        column_sql += " UNIQUE"
                    columns.append(column_sql)

                create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
                await connection.execute(create_sql)

    async def backup_database(self, backup_path: str) -> Dict[str, Any]:
        """Create database backup"""
        import subprocess

        try:
            result = subprocess.run([
                'pg_dump', self.connection_string, '-f', backup_path
            ], capture_output=True, text=True)

            if result.returncode == 0:
                return {"status": "success", "backup_path": backup_path}
            else:
                raise Exception(f"Backup failed: {result.stderr}")

        except Exception as e:
            raise Exception(f"Database backup failed: {e}")
```

## 🔄 CI/CD Integration

### GitHub Actions Integration

```python
# integrations/cicd/github_actions.py
from integrations.github.github_integration import GitHubIntegration
from typing import Dict, Any

class GitHubActionsIntegration:
    """GitHub Actions CI/CD integration"""

    def __init__(self, github_integration: GitHubIntegration):
        self.github = github_integration

    async def create_workflow_file(self, owner: str, repo: str, workflow_name: str, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create GitHub Actions workflow file"""
        import yaml

        workflow_yaml = yaml.dump(workflow_config, default_flow_style=False)
        file_path = f".github/workflows/{workflow_name}.yml"

        return await self.github.create_file(
            owner=owner,
            repo=repo,
            path=file_path,
            content=workflow_yaml,
            message=f"Add {workflow_name} workflow"
        )

    def generate_deployment_workflow(self, project_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate deployment workflow configuration"""
        return {
            "name": "Deploy to Production",
            "on": {
                "push": {
                    "branches": ["main"]
                }
            },
            "jobs": {
                "deploy": {
                    "runs-on": "ubuntu-latest",
                    "steps": [
                        {
                            "name": "Checkout code",
                            "uses": "actions/checkout@v3"
                        },
                        {
                            "name": "Setup Node.js",
                            "uses": "actions/setup-node@v3",
                            "with": {
                                "node-version": "18"
                            }
                        },
                        {
                            "name": "Install dependencies",
                            "run": "npm ci"
                        },
                        {
                            "name": "Run tests",
                            "run": "npm test"
                        },
                        {
                            "name": "Build application",
                            "run": "npm run build"
                        },
                        {
                            "name": "Deploy to production",
                            "run": "npm run deploy",
                            "env": {
                                "DEPLOY_TOKEN": "${{ secrets.DEPLOY_TOKEN }}"
                            }
                        }
                    ]
                }
            }
        }
```

## 📊 Monitoring Integration

### Prometheus Integration

```python
# integrations/monitoring/prometheus_integration.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import time
from typing import Dict, Any

class PrometheusIntegration:
    """Prometheus monitoring integration"""

    def __init__(self, port: int = 8000):
        self.port = port

        # Define metrics
        self.project_counter = Counter('aetherforge_projects_total', 'Total number of projects created')
        self.agent_execution_time = Histogram('aetherforge_agent_execution_seconds', 'Agent execution time', ['agent_type'])
        self.active_projects = Gauge('aetherforge_active_projects', 'Number of active projects')
        self.error_counter = Counter('aetherforge_errors_total', 'Total number of errors', ['error_type'])

    def start_metrics_server(self):
        """Start Prometheus metrics server"""
        start_http_server(self.port)

    def record_project_creation(self):
        """Record project creation"""
        self.project_counter.inc()

    def record_agent_execution(self, agent_type: str, execution_time: float):
        """Record agent execution time"""
        self.agent_execution_time.labels(agent_type=agent_type).observe(execution_time)

    def update_active_projects(self, count: int):
        """Update active projects count"""
        self.active_projects.set(count)

    def record_error(self, error_type: str):
        """Record error occurrence"""
        self.error_counter.labels(error_type=error_type).inc()
```

## 🔗 Next Steps

- **[Component Adapters](component-adapters.md)** - Service integration patterns
- **[API Extensions](api-extensions.md)** - Extend the REST API
- **[Custom Agents](custom-agents.md)** - Create specialized agents
- **[Plugin Development](plugin-development.md)** - Create plugins

## 📖 Resources

- [Integration Examples Repository](https://github.com/aetherforge/integration-examples)
- [Community Integrations](https://github.com/aetherforge/community-integrations)
- [Integration Best Practices](https://docs.aetherforge.dev/best-practices/integrations)