#!/usr/bin/env python3
"""
Cross-Platform Test Runner for TaoForge
Supports Windows, macOS, and Linux environments
"""

import os
import sys
import platform
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

class CrossPlatformTestRunner:
    """Cross-platform test execution manager"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.is_windows = self.platform == "windows"
        
        # Platform-specific configurations
        self.python_cmd = self._get_python_command()
        self.docker_compose_cmd = self._get_docker_compose_command()
        
    def _get_python_command(self) -> str:
        """Get the appropriate Python command for the platform"""
        for cmd in ["python", "python3", "py"]:
            try:
                result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
                if result.returncode == 0 and "Python 3" in result.stdout:
                    return cmd
            except FileNotFoundError:
                continue
        raise RuntimeError("Python 3 not found")
    
    def _get_docker_compose_command(self) -> List[str]:
        """Get the appropriate Docker Compose command"""
        for cmd in [["docker", "compose"], ["docker-compose"]]:
            try:
                result = subprocess.run(cmd + ["--version"], capture_output=True, text=True)
                if result.returncode == 0:
                    return cmd
            except FileNotFoundError:
                continue
        return None
    
    def log(self, level: str, message: str):
        """Cross-platform logging"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # Use simple ASCII icons for cross-platform compatibility
        icons = {"ERROR": "X", "SUCCESS": "OK", "WARNING": "!", "INFO": "i"}
        
        icon = icons.get(level, "")
        print(f"{icon} [{timestamp}] {message}")
    
    def setup_test_environment(self) -> bool:
        """Set up test environment"""
        self.log("INFO", "Setting up test environment...")
        
        # Set environment variables
        test_env = {
            "AETHERFORGE_ENV": "test",
            "PYTHONPATH": str(self.project_root),
            "PROJECTS_DIR": str(self.project_root / "test_projects"),
            "PHEROMONE_FILE": str(self.project_root / "test_pheromones.json"),
            "LOG_LEVEL": "debug"
        }
        
        for key, value in test_env.items():
            os.environ[key] = value
        
        # Create test directories
        test_dirs = ["test_projects", "test_results", "coverage_reports"]
        for dir_name in test_dirs:
            (self.project_root / dir_name).mkdir(exist_ok=True)
        
        self.log("SUCCESS", "Test environment configured")
        return True
    
    def install_test_dependencies(self) -> bool:
        """Install test dependencies"""
        self.log("INFO", "Installing test dependencies...")
        
        test_packages = [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0", 
            "pytest-cov>=4.0.0",
            "pytest-html>=3.1.0",
            "pytest-xdist>=3.0.0",
            "pytest-timeout>=2.1.0"
        ]
        
        try:
            for package in test_packages:
                result = subprocess.run(
                    [self.python_cmd, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True
                )
                if result.returncode != 0:
                    self.log("WARNING", f"Failed to install {package}: {result.stderr}")
            
            self.log("SUCCESS", "Test dependencies installed")
            return True
        except Exception as e:
            self.log("ERROR", f"Failed to install dependencies: {e}")
            return False
    
    def run_unit_tests(self) -> bool:
        """Run unit tests"""
        self.log("INFO", "Running unit tests...")
        
        cmd = [
            self.python_cmd, "-m", "pytest",
            "tests/",
            "-v",
            "--ignore=tests/test_integration_comprehensive.py",
            "--ignore=tests/test_complete_integration.py", 
            "--ignore=tests/test_end_to_end.py",
            "--ignore=tests/test_production_integration.py",
            "--cov=src",
            "--cov-report=html:coverage_reports/unit",
            "--html=test_results/unit_report.html",
            "--self-contained-html",
            "--timeout=60"
        ]
        
        try:
            result = subprocess.run(cmd, cwd=self.project_root)
            if result.returncode == 0:
                self.log("SUCCESS", "Unit tests passed")
                return True
            else:
                self.log("ERROR", "Unit tests failed")
                return False
        except Exception as e:
            self.log("ERROR", f"Failed to run unit tests: {e}")
            return False
    
    def run_integration_tests(self) -> bool:
        """Run integration tests"""
        self.log("INFO", "Running integration tests...")
        
        # Check if Docker is available for integration tests
        if self.docker_compose_cmd is None:
            self.log("WARNING", "Docker not available, skipping integration tests")
            return True
        
        cmd = [
            self.python_cmd, "-m", "pytest",
            "tests/test_integration_comprehensive.py",
            "tests/test_complete_integration.py",
            "-v",
            "--timeout=300",
            "--html=test_results/integration_report.html"
        ]
        
        try:
            result = subprocess.run(cmd, cwd=self.project_root)
            if result.returncode == 0:
                self.log("SUCCESS", "Integration tests passed")
                return True
            else:
                self.log("WARNING", "Some integration tests failed")
                return False
        except Exception as e:
            self.log("ERROR", f"Failed to run integration tests: {e}")
            return False
    
    def run_docker_tests(self) -> bool:
        """Run tests in Docker environment"""
        if self.docker_compose_cmd is None:
            self.log("WARNING", "Docker not available, skipping Docker tests")
            return True
        
        self.log("INFO", "Running tests in Docker environment...")
        
        compose_file = self.project_root / "docker-compose.test.yml"
        if not compose_file.exists():
            self.log("WARNING", "docker-compose.test.yml not found, skipping Docker tests")
            return True
        
        try:
            # Run unit tests in Docker
            result = subprocess.run(
                self.docker_compose_cmd + ["-f", str(compose_file), "run", "--rm", "unit-tests"],
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                self.log("SUCCESS", "Docker tests passed")
                return True
            else:
                self.log("WARNING", "Some Docker tests failed")
                return False
        except Exception as e:
            self.log("ERROR", f"Failed to run Docker tests: {e}")
            return False
    
    def run_code_quality_checks(self) -> bool:
        """Run code quality checks"""
        self.log("INFO", "Running code quality checks...")
        
        checks = [
            ("Black formatting", [self.python_cmd, "-m", "black", "--check", "--diff", "src/", "tests/"]),
            ("Flake8 linting", [self.python_cmd, "-m", "flake8", "src/", "tests/", "--max-line-length=88"])
        ]
        
        all_passed = True
        
        for check_name, cmd in checks:
            try:
                result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
                if result.returncode == 0:
                    self.log("SUCCESS", f"{check_name} passed")
                else:
                    self.log("WARNING", f"{check_name} failed: {result.stdout}")
                    all_passed = False
            except FileNotFoundError:
                self.log("WARNING", f"{check_name} tool not available")
            except Exception as e:
                self.log("ERROR", f"Failed to run {check_name}: {e}")
                all_passed = False
        
        return all_passed
    
    def generate_test_report(self, results: Dict[str, bool]) -> bool:
        """Generate comprehensive test report"""
        self.log("INFO", "Generating test report...")
        
        report = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": results,
            "overall_status": "PASSED" if all(results.values()) else "FAILED"
        }
        
        report_file = self.project_root / "test_results" / "test_summary.json"
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            self.log("SUCCESS", f"Test report saved to {report_file}")
            return True
        except Exception as e:
            self.log("ERROR", f"Failed to save test report: {e}")
            return False
    
    def run_all_tests(self, include_docker: bool = True, include_integration: bool = True) -> bool:
        """Run all test suites"""
        self.log("INFO", f"Starting comprehensive test suite on {platform.system()}...")
        
        # Setup
        if not self.setup_test_environment():
            return False
        
        if not self.install_test_dependencies():
            return False
        
        # Run test suites
        results = {}
        
        results["unit_tests"] = self.run_unit_tests()
        
        if include_integration:
            results["integration_tests"] = self.run_integration_tests()
        
        if include_docker:
            results["docker_tests"] = self.run_docker_tests()
        
        results["code_quality"] = self.run_code_quality_checks()
        
        # Generate report
        self.generate_test_report(results)
        
        # Summary
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        self.log("INFO", f"Test Summary: {passed}/{total} test suites passed")
        
        if all(results.values()):
            self.log("SUCCESS", "All tests passed! TaoForge is ready for deployment.")
            return True
        else:
            self.log("WARNING", "Some tests failed. Review the results before deployment.")
            return False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cross-platform test runner for TaoForge")
    parser.add_argument("--no-docker", action="store_true", help="Skip Docker tests")
    parser.add_argument("--no-integration", action="store_true", help="Skip integration tests")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    
    args = parser.parse_args()
    
    runner = CrossPlatformTestRunner()
    
    if args.unit_only:
        success = runner.setup_test_environment() and runner.run_unit_tests()
    else:
        success = runner.run_all_tests(
            include_docker=not args.no_docker,
            include_integration=not args.no_integration
        )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
