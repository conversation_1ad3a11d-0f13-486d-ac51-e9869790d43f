#!/usr/bin/env python3
"""
Final Production Readiness Validation for TaoForge
Comprehensive validation that handles all edge cases and missing dependencies
"""

import os
import sys
import platform
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

class FinalValidator:
    """Final comprehensive validation for production readiness"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.is_windows = self.platform == "windows"
        
        # Results tracking
        self.results = {}
        self.issues = []
        self.fixes = []
        self.score = 0
        self.max_score = 0
        
    def log(self, level: str, message: str):
        """Cross-platform logging"""
        icons = {
            "ERROR": "❌" if not self.is_windows else "X",
            "SUCCESS": "✅" if not self.is_windows else "✓", 
            "WARNING": "⚠️" if not self.is_windows else "!",
            "INFO": "ℹ️" if not self.is_windows else "i"
        }
        
        icon = icons.get(level, "")
        print(f"{icon} {message}")
    
    def validate_core_files(self) -> bool:
        """Validate core project files exist"""
        self.log("INFO", "Validating core project files...")
        self.max_score += 20
        
        required_files = [
            "requirements.txt",
            "docker-compose.yml",
            "docker-compose.prod.yml", 
            "docker-compose.test.yml",
            "Dockerfile.test",
            "src/orchestrator.py",
            "scripts/deploy.sh",
            "scripts/deploy_production.sh",
            "scripts/health_check.sh",
            "scripts/backup.sh",
            ".env.example",
            ".github/workflows/ci-cd.yml"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.issues.extend([f"Missing: {f}" for f in missing_files])
            score = max(0, 20 - len(missing_files) * 2)
            self.score += score
            self.log("WARNING", f"Missing {len(missing_files)} files, partial score: {score}/20")
            return False
        else:
            self.score += 20
            self.log("SUCCESS", "All core files present (20/20)")
            return True
    
    def validate_deployment_scripts(self) -> bool:
        """Validate deployment scripts are executable and functional"""
        self.log("INFO", "Validating deployment scripts...")
        self.max_score += 15
        
        scripts = [
            "scripts/deploy.sh",
            "scripts/deploy_production.sh", 
            "scripts/health_check.sh",
            "scripts/backup.sh",
            "scripts/validate_environment.py",
            "scripts/run_tests_cross_platform.py"
        ]
        
        working_scripts = 0
        for script in scripts:
            script_path = self.project_root / script
            if script_path.exists():
                # Test if script can be parsed/imported
                try:
                    if script.endswith('.py'):
                        # Test Python syntax
                        with open(script_path, 'r') as f:
                            compile(f.read(), script_path, 'exec')
                    working_scripts += 1
                except Exception as e:
                    self.issues.append(f"Script error in {script}: {e}")
            else:
                self.issues.append(f"Missing script: {script}")
        
        score = int((working_scripts / len(scripts)) * 15)
        self.score += score
        
        if score >= 12:
            self.log("SUCCESS", f"Deployment scripts validated ({score}/15)")
            return True
        else:
            self.log("WARNING", f"Some deployment scripts have issues ({score}/15)")
            return False
    
    def validate_docker_configuration(self) -> bool:
        """Validate Docker configuration files"""
        self.log("INFO", "Validating Docker configuration...")
        self.max_score += 15
        
        compose_files = [
            "docker-compose.yml",
            "docker-compose.prod.yml",
            "docker-compose.test.yml"
        ]
        
        valid_configs = 0
        for compose_file in compose_files:
            compose_path = self.project_root / compose_file
            if compose_path.exists():
                try:
                    # Basic YAML validation
                    import yaml
                    with open(compose_path, 'r') as f:
                        config = yaml.safe_load(f)
                    
                    # Check for required sections
                    if 'services' in config and len(config['services']) > 0:
                        valid_configs += 1
                        self.log("SUCCESS", f"Valid: {compose_file}")
                    else:
                        self.issues.append(f"Invalid Docker config: {compose_file}")
                except Exception as e:
                    self.issues.append(f"Docker config error in {compose_file}: {e}")
            else:
                self.issues.append(f"Missing Docker config: {compose_file}")
        
        score = int((valid_configs / len(compose_files)) * 15)
        self.score += score
        
        if score >= 10:
            self.log("SUCCESS", f"Docker configuration validated ({score}/15)")
            return True
        else:
            self.log("WARNING", f"Docker configuration issues ({score}/15)")
            return False
    
    def validate_test_framework(self) -> bool:
        """Validate test framework and run basic tests"""
        self.log("INFO", "Validating test framework...")
        self.max_score += 20
        
        # Check test files exist
        test_files = [
            "tests/conftest.py",
            "tests/test_production_integration.py",
            "tests/test_integration_comprehensive.py",
            "tests/test_complete_integration.py"
        ]
        
        existing_tests = 0
        for test_file in test_files:
            if (self.project_root / test_file).exists():
                existing_tests += 1
        
        # Try to run a simple test validation
        test_score = 0
        try:
            # Check if pytest is available
            try:
                import pytest
                test_score += 3
                self.log("SUCCESS", "pytest is available")
            except ImportError:
                self.log("WARNING", "pytest not installed")

            # Check if test files have valid Python syntax
            syntax_valid = True
            for test_file in test_files:
                test_path = self.project_root / "tests" / test_file
                if test_path.exists():
                    try:
                        with open(test_path, 'r', encoding='utf-8', errors='ignore') as f:
                            compile(f.read(), test_path, 'exec')
                    except SyntaxError:
                        syntax_valid = False
                        break

            if syntax_valid:
                test_score += 4
                self.log("SUCCESS", "Test files have valid syntax")
            else:
                self.log("WARNING", "Some test files have syntax errors")

            # Check for conftest.py (test configuration)
            if (self.project_root / "tests" / "conftest.py").exists():
                test_score += 3
                self.log("SUCCESS", "Test configuration (conftest.py) found")
            else:
                self.log("WARNING", "Test configuration (conftest.py) missing")

        except Exception as e:
            self.log("WARNING", f"Could not validate test framework: {e}")
            test_score = 5  # Give partial credit for existing test files
        
        file_score = int((existing_tests / len(test_files)) * 10)
        total_score = file_score + test_score
        self.score += total_score
        
        if total_score >= 15:
            self.log("SUCCESS", f"Test framework validated ({total_score}/20)")
            return True
        else:
            self.log("WARNING", f"Test framework needs improvement ({total_score}/20)")
            return False
    
    def validate_ci_cd_pipeline(self) -> bool:
        """Validate CI/CD pipeline configuration"""
        self.log("INFO", "Validating CI/CD pipeline...")
        self.max_score += 10
        
        github_workflow = self.project_root / ".github" / "workflows" / "ci-cd.yml"
        
        if not github_workflow.exists():
            self.issues.append("Missing GitHub Actions workflow")
            self.log("WARNING", "CI/CD pipeline not configured (0/10)")
            return False
        
        try:
            import yaml
            with open(github_workflow, 'r') as f:
                workflow = yaml.safe_load(f)
            
            # Check for required jobs
            required_jobs = ['test', 'lint', 'docker-build']
            existing_jobs = list(workflow.get('jobs', {}).keys())
            
            job_score = 0
            for job in required_jobs:
                if job in existing_jobs:
                    job_score += 1
            
            score = int((job_score / len(required_jobs)) * 10)
            self.score += score
            
            if score >= 7:
                self.log("SUCCESS", f"CI/CD pipeline configured ({score}/10)")
                return True
            else:
                self.log("WARNING", f"CI/CD pipeline incomplete ({score}/10)")
                return False
                
        except Exception as e:
            self.issues.append(f"CI/CD pipeline error: {e}")
            self.log("WARNING", "CI/CD pipeline has errors (0/10)")
            return False
    
    def validate_documentation(self) -> bool:
        """Validate documentation completeness"""
        self.log("INFO", "Validating documentation...")
        self.max_score += 10
        
        required_docs = [
            "README.md",
            "docs/PRODUCTION_DEPLOYMENT_CHECKLIST.md",
            "docs/GITHUB_SECRETS_SETUP.md",
            "PRODUCTION_READINESS_ASSESSMENT.md"
        ]
        
        existing_docs = 0
        for doc in required_docs:
            doc_path = self.project_root / doc
            if doc_path.exists() and doc_path.stat().st_size > 100:  # At least 100 bytes
                existing_docs += 1
        
        score = int((existing_docs / len(required_docs)) * 10)
        self.score += score
        
        if score >= 7:
            self.log("SUCCESS", f"Documentation validated ({score}/10)")
            return True
        else:
            self.log("WARNING", f"Documentation incomplete ({score}/10)")
            return False
    
    def validate_security_configuration(self) -> bool:
        """Validate security configuration"""
        self.log("INFO", "Validating security configuration...")
        self.max_score += 10
        
        security_files = [
            "nginx/nginx.conf",
            ".env.example",
            "monitoring/prometheus.yml",
            "SECURITY.md",
            "nginx/ssl/README.md"
        ]

        security_score = 0
        for file_path in security_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                security_score += 1

                # Check for security-related content
                try:
                    content = full_path.read_text(encoding='utf-8', errors='ignore')
                    security_terms = ['ssl', 'https', 'security', 'auth', 'jwt', 'cors', 'csrf', 'encryption', 'certificate']
                    if any(term in content.lower() for term in security_terms):
                        security_score += 1
                except:
                    pass

        # Additional security checks
        if (self.project_root / "SECURITY.md").exists():
            security_score += 2  # Bonus for dedicated security documentation

        score = min(10, security_score)
        self.score += score
        
        if score >= 7:
            self.log("SUCCESS", f"Security configuration validated ({score}/10)")
            return True
        else:
            self.log("WARNING", f"Security configuration needs improvement ({score}/10)")
            return False
    
    def generate_final_report(self) -> bool:
        """Generate final validation report"""
        percentage = int((self.score / self.max_score) * 100) if self.max_score > 0 else 0
        
        print("\n" + "=" * 60)
        print("🎯 TAOFORGE FINAL PRODUCTION READINESS REPORT")
        print("=" * 60)
        print(f"Platform: {platform.system()} {platform.release()}")
        print(f"Python: {platform.python_version()}")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("")
        
        # Overall score
        if percentage >= 95:
            self.log("SUCCESS", f"OVERALL SCORE: {self.score}/{self.max_score} ({percentage}%)")
            self.log("SUCCESS", "🎉 PRODUCTION READY!")
        elif percentage >= 85:
            self.log("WARNING", f"OVERALL SCORE: {self.score}/{self.max_score} ({percentage}%)")
            self.log("WARNING", "⚠️ MOSTLY READY - Minor issues to address")
        else:
            self.log("ERROR", f"OVERALL SCORE: {self.score}/{self.max_score} ({percentage}%)")
            self.log("ERROR", "❌ NOT READY - Significant issues to resolve")
        
        print("")
        print("Component Breakdown:")
        for component, result in self.results.items():
            status = "✅" if result else "❌"
            print(f"  {status} {component}")
        
        if self.fixes:
            print("\nFixes Applied:")
            for fix in self.fixes:
                print(f"  ✅ {fix}")
        
        if self.issues:
            print("\nIssues Found:")
            for issue in self.issues[:10]:  # Show first 10 issues
                print(f"  ❌ {issue}")
            if len(self.issues) > 10:
                print(f"  ... and {len(self.issues) - 10} more issues")
        
        print("\nNext Steps:")
        if percentage >= 95:
            print("  1. ✅ Ready for production deployment!")
            print("  2. 🚀 Run: ./scripts/deploy_production.sh")
            print("  3. 📊 Monitor: ./scripts/health_check.sh")
        elif percentage >= 85:
            print("  1. 🔧 Address minor issues listed above")
            print("  2. 🧪 Run comprehensive tests")
            print("  3. 🚀 Proceed with deployment")
        else:
            print("  1. 🔧 Fix critical issues listed above")
            print("  2. 🧪 Re-run validation")
            print("  3. 📚 Review documentation")
        
        print("=" * 60)
        
        return percentage >= 95
    
    def run_final_validation(self) -> bool:
        """Run complete final validation"""
        self.log("INFO", "🎯 Starting Final Production Readiness Validation...")
        
        validations = [
            ("Core Files", self.validate_core_files),
            ("Deployment Scripts", self.validate_deployment_scripts),
            ("Docker Configuration", self.validate_docker_configuration),
            ("Test Framework", self.validate_test_framework),
            ("CI/CD Pipeline", self.validate_ci_cd_pipeline),
            ("Documentation", self.validate_documentation),
            ("Security Configuration", self.validate_security_configuration)
        ]
        
        for name, validator in validations:
            try:
                result = validator()
                self.results[name] = result
            except Exception as e:
                self.log("ERROR", f"Validation failed for {name}: {e}")
                self.results[name] = False
        
        return self.generate_final_report()

def main():
    """Main entry point"""
    validator = FinalValidator()
    success = validator.run_final_validation()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
