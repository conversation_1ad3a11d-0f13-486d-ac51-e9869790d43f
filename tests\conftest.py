"""
Pytest configuration and fixtures for Aetherforge tests
"""

import pytest
import tempfile
import os
import shutil
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Optional dependency imports with graceful fallbacks
try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    docker = None
    DOCKER_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    requests = None
    REQUESTS_AVAILABLE = False

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    asyncpg = None
    ASYNCPG_AVAILABLE = False

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False


@pytest.fixture(scope="session")
def test_data_dir():
    """Create a temporary directory for test data"""
    temp_dir = tempfile.mkdtemp(prefix="aetherforge_test_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture(scope="function")
def temp_projects_dir(test_data_dir):
    """Create a temporary projects directory for each test"""
    projects_dir = Path(test_data_dir) / "projects"
    projects_dir.mkdir(exist_ok=True)
    return str(projects_dir)


@pytest.fixture(scope="function")
def temp_pheromone_file(test_data_dir):
    """Create a temporary pheromone file for each test"""
    pheromone_file = Path(test_data_dir) / "test_pheromones.json"
    return str(pheromone_file)


@pytest.fixture(scope="function")
def mock_vscode():
    """Mock VS Code API"""
    mock = Mock()
    
    # Mock common VS Code objects
    mock.window.createWebviewPanel.return_value = Mock()
    mock.window.showInformationMessage.return_value = None
    mock.window.showErrorMessage.return_value = None
    mock.window.showWarningMessage.return_value = None
    
    mock.workspace.workspaceFolders = [
        Mock(uri=Mock(fsPath="/test/workspace"))
    ]
    mock.workspace.getConfiguration.return_value = Mock()
    
    mock.commands.executeCommand.return_value = None
    
    mock.Uri.file.return_value = Mock()
    mock.ViewColumn.One = 1
    mock.ConfigurationTarget.Workspace = "workspace"
    
    return mock


@pytest.fixture(scope="function")
def mock_webview():
    """Mock webview object"""
    mock = Mock()
    mock.postMessage = Mock()
    mock.onDidReceiveMessage = Mock()
    return mock


@pytest.fixture(scope="function")
def mock_extension_context():
    """Mock VS Code extension context"""
    mock = Mock()
    mock.subscriptions = []
    mock.extensionPath = "/test/extension/path"
    return mock


@pytest.fixture(scope="function")
def sample_project_data():
    """Sample project data for testing"""
    return {
        "prompt": "Create a task management application with user authentication",
        "project_name": "TaskManager",
        "project_type": "fullstack",
        "workflow": "greenfield-fullstack"
    }


@pytest.fixture(scope="function")
def sample_pheromone_data():
    """Sample pheromone data for testing"""
    return {
        "signal": "test_signal",
        "payload": {
            "message": "Test pheromone",
            "data": {"key": "value"}
        },
        "project_id": "test_project_123",
        "agent_id": "test_agent",
        "trail_id": "test_trail"
    }


@pytest.fixture(scope="function")
def mock_orchestrator_response():
    """Mock orchestrator API response"""
    return {
        "status": "success",
        "project_id": "test-project-123",
        "project_slug": "TaskManager",
        "project_path": "/projects/TaskManager",
        "message": "Project creation started",
        "workflow": "greenfield-fullstack",
        "agent_team": {
            "agents": [
                {
                    "role": "analyst",
                    "name": "Requirements Analyst",
                    "capabilities": ["requirements_analysis", "user_story_creation"]
                },
                {
                    "role": "architect", 
                    "name": "System Architect",
                    "capabilities": ["system_design", "technology_selection"]
                },
                {
                    "role": "developer",
                    "name": "Full Stack Developer", 
                    "capabilities": ["frontend_development", "backend_development"]
                },
                {
                    "role": "qa",
                    "name": "Quality Assurance",
                    "capabilities": ["testing", "validation"]
                }
            ]
        }
    }


@pytest.fixture(scope="function")
def mock_component_status():
    """Mock component status response"""
    return {
        "components": {
            "orchestrator": "running",
            "archon": "running", 
            "mcp-crawl4ai": "offline",
            "pheromind": "running",
            "bmad": "unknown"
        },
        "timestamp": "2023-12-01T12:00:00Z",
        "overall_status": "healthy"
    }


@pytest.fixture(scope="function")
def mock_projects_list():
    """Mock projects list response"""
    return [
        {
            "id": "project-1",
            "name": "TaskManager",
            "slug": "TaskManager",
            "path": "/projects/TaskManager",
            "status": "completed",
            "created": "2023-12-01T10:00:00Z",
            "completed": "2023-12-01T11:30:00Z"
        },
        {
            "id": "project-2", 
            "name": "BlogApp",
            "slug": "BlogApp",
            "path": "/projects/BlogApp",
            "status": "in_progress",
            "created": "2023-12-01T11:00:00Z",
            "completed": None
        }
    ]


@pytest.fixture(scope="function")
def mock_pheromone_statistics():
    """Mock pheromone statistics response"""
    return {
        "total_pheromones": 42,
        "unique_signals": 15,
        "projects_with_pheromones": 3,
        "pheromones_today": 12,
        "top_signals": [
            {"signal": "project_creation_started", "count": 8},
            {"signal": "agent_task_completed", "count": 15},
            {"signal": "phase_completed", "count": 6}
        ]
    }


# Test markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )
    config.addinivalue_line(
        "markers", "vscode: mark test as VS Code extension test"
    )


# Skip integration tests by default unless explicitly requested
def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle markers"""
    if config.getoption("--integration"):
        # Don't skip integration tests if explicitly requested
        return
    
    skip_integration = pytest.mark.skip(reason="need --integration option to run")
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(skip_integration)


def pytest_addoption(parser):
    """Add custom command line options"""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="run integration tests"
    )
    parser.addoption(
        "--slow",
        action="store_true", 
        default=False,
        help="run slow tests"
    )


# Environment setup for tests
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_projects_dir, temp_pheromone_file):
    """Setup test environment variables"""
    monkeypatch.setenv("PROJECTS_DIR", temp_projects_dir)
    monkeypatch.setenv("PHEROMONE_FILE", temp_pheromone_file)
    monkeypatch.setenv("AETHERFORGE_ENV", "test")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")


# Mock external dependencies
@pytest.fixture(autouse=True)
def mock_external_apis(monkeypatch):
    """Mock external API calls by default"""
    # Mock requests to prevent actual HTTP calls in unit tests
    mock_requests = Mock()
    mock_requests.post.return_value = Mock(status_code=200, json=lambda: {"status": "success"})
    mock_requests.get.return_value = Mock(status_code=200, json=lambda: {"status": "healthy"})
    
    monkeypatch.setattr("requests.post", mock_requests.post)
    monkeypatch.setattr("requests.get", mock_requests.get)


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """Cleanup test files after each test"""
    yield
    
    # Clean up any test files that might have been created
    test_files = [
        "test_pheromones.json",
        "test_projects.json", 
        "test_config.json"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)


# Additional fixtures for dependency handling
@pytest.fixture
def mock_docker_client():
    """Mock Docker client for tests when Docker is not available"""
    if DOCKER_AVAILABLE:
        try:
            client = docker.from_env()
            yield client
        except Exception:
            # Docker daemon not running, use mock
            yield create_mock_docker_client()
    else:
        yield create_mock_docker_client()


def create_mock_docker_client():
    """Create a mock Docker client"""
    mock_client = Mock()
    mock_container = Mock()
    mock_container.name = "test_container"
    mock_container.stats.return_value = {
        "memory": {"limit": 1024 * 1024 * 1024}  # 1GB
    }
    mock_client.containers.list.return_value = [mock_container]
    return mock_client


@pytest.fixture
def mock_requests_session():
    """Mock requests session for tests"""
    if REQUESTS_AVAILABLE:
        yield requests
    else:
        mock_requests = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "healthy"}
        mock_requests.get.return_value = mock_response
        mock_requests.post.return_value = mock_response
        yield mock_requests


# Skip markers for missing dependencies
def skip_if_no_docker():
    """Skip test if Docker is not available"""
    return pytest.mark.skipif(not DOCKER_AVAILABLE, reason="Docker not available")


def skip_if_no_requests():
    """Skip test if requests is not available"""
    return pytest.mark.skipif(not REQUESTS_AVAILABLE, reason="requests not available")


def skip_if_no_database():
    """Skip test if database dependencies are not available"""
    return pytest.mark.skipif(not ASYNCPG_AVAILABLE, reason="asyncpg not available")


def skip_if_no_redis():
    """Skip test if Redis dependencies are not available"""
    return pytest.mark.skipif(not REDIS_AVAILABLE, reason="redis not available")
