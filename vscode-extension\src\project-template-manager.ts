import * as vscode from 'vscode';
import * as path from 'path';
import { WorkspaceManager, FileOperation } from './workspace-manager';

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  type: ProjectType;
  files: TemplateFile[];
  directories: string[];
  dependencies?: PackageDependency[];
  scripts?: Record<string, string>;
  configuration?: Record<string, any>;
}

export interface TemplateFile {
  path: string;
  content: string;
  template?: boolean; // If true, content will be processed as template
  encoding?: BufferEncoding;
}

export interface PackageDependency {
  name: string;
  version: string;
  type: 'dependency' | 'devDependency' | 'peerDependency';
}

export enum ProjectType {
  WEB_APPLICATION = 'web-application',
  API_SERVICE = 'api-service',
  MOBILE_APPLICATION = 'mobile-application',
  DESKTOP_APPLICATION = 'desktop-application',
  LIBRARY = 'library',
  CLI_TOOL = 'cli-tool',
  MICROSERVICE = 'microservice',
  DATA_PLATFORM = 'data-platform',
  GAME = 'game',
  BLOCKCHAIN = 'blockchain'
}

export interface ProjectConfig {
  name: string;
  description: string;
  type: ProjectType;
  author?: string;
  license?: string;
  version?: string;
  repository?: string;
  keywords?: string[];
  features?: string[];
  technologies?: string[];
}

export class ProjectTemplateManager {
  private workspaceManager: WorkspaceManager;
  private templates: Map<string, ProjectTemplate> = new Map();

  constructor(workspaceManager: WorkspaceManager) {
    this.workspaceManager = workspaceManager;
    this.initializeDefaultTemplates();
  }

  /**
   * Initialize default project templates
   */
  private initializeDefaultTemplates(): void {
    // React Web Application Template
    this.registerTemplate({
      id: 'react-web-app',
      name: 'React Web Application',
      description: 'Modern React application with TypeScript, Vite, and Tailwind CSS',
      type: ProjectType.WEB_APPLICATION,
      directories: [
        'src',
        'src/components',
        'src/pages',
        'src/hooks',
        'src/utils',
        'src/types',
        'src/assets',
        'public',
        'tests'
      ],
      files: [
        {
          path: 'package.json',
          content: this.getReactPackageJson(),
          template: true
        },
        {
          path: 'vite.config.ts',
          content: this.getViteConfig()
        },
        {
          path: 'tsconfig.json',
          content: this.getTypeScriptConfig()
        },
        {
          path: 'tailwind.config.js',
          content: this.getTailwindConfig()
        },
        {
          path: 'src/App.tsx',
          content: this.getReactAppComponent(),
          template: true
        },
        {
          path: 'src/main.tsx',
          content: this.getReactMain()
        },
        {
          path: 'src/index.css',
          content: this.getBaseCss()
        },
        {
          path: 'index.html',
          content: this.getIndexHtml(),
          template: true
        },
        {
          path: 'README.md',
          content: this.getReadmeTemplate(),
          template: true
        },
        {
          path: '.gitignore',
          content: this.getGitignore()
        }
      ]
    });

    // Node.js API Service Template
    this.registerTemplate({
      id: 'nodejs-api',
      name: 'Node.js API Service',
      description: 'Express.js API service with TypeScript, MongoDB, and authentication',
      type: ProjectType.API_SERVICE,
      directories: [
        'src',
        'src/controllers',
        'src/models',
        'src/routes',
        'src/middleware',
        'src/services',
        'src/utils',
        'src/types',
        'tests',
        'docs'
      ],
      files: [
        {
          path: 'package.json',
          content: this.getNodeApiPackageJson(),
          template: true
        },
        {
          path: 'tsconfig.json',
          content: this.getTypeScriptConfig()
        },
        {
          path: 'src/app.ts',
          content: this.getNodeApiApp()
        },
        {
          path: 'src/server.ts',
          content: this.getNodeApiServer()
        },
        {
          path: '.env.example',
          content: this.getNodeApiEnv()
        },
        {
          path: 'README.md',
          content: this.getReadmeTemplate(),
          template: true
        },
        {
          path: '.gitignore',
          content: this.getGitignore()
        }
      ]
    });

    // Python CLI Tool Template
    this.registerTemplate({
      id: 'python-cli',
      name: 'Python CLI Tool',
      description: 'Python command-line tool with Click, pytest, and packaging',
      type: ProjectType.CLI_TOOL,
      directories: [
        'src',
        'tests',
        'docs'
      ],
      files: [
        {
          path: 'pyproject.toml',
          content: this.getPythonProjectConfig(),
          template: true
        },
        {
          path: 'src/main.py',
          content: this.getPythonCliMain(),
          template: true
        },
        {
          path: 'requirements.txt',
          content: this.getPythonCliRequirements()
        },
        {
          path: 'README.md',
          content: this.getReadmeTemplate(),
          template: true
        },
        {
          path: '.gitignore',
          content: this.getGitignore()
        }
      ]
    });
  }

  /**
   * Register a new project template
   */
  registerTemplate(template: ProjectTemplate): void {
    this.templates.set(template.id, template);
  }

  /**
   * Get all available templates
   */
  getTemplates(): ProjectTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get template by ID
   */
  getTemplate(id: string): ProjectTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * Create project from template
   */
  async createProjectFromTemplate(
    templateId: string,
    projectConfig: ProjectConfig,
    targetPath: string = ''
  ): Promise<void> {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    const projectPath = path.join(targetPath, projectConfig.name);

    try {
      // Create project directory
      await this.workspaceManager.createDirectory(projectPath);

      // Create subdirectories
      for (const dir of template.directories) {
        await this.workspaceManager.createDirectory(path.join(projectPath, dir));
      }

      // Create files
      const fileOperations: FileOperation[] = template.files.map(file => ({
        type: 'create',
        path: path.join(projectPath, file.path),
        content: file.template 
          ? this.processTemplate(file.content, projectConfig)
          : file.content,
        encoding: file.encoding || 'utf8'
      }));

      await this.workspaceManager.executeBatchOperations(fileOperations);

      // Create project metadata
      await this.createProjectMetadata(projectPath, projectConfig, template);

      vscode.window.showInformationMessage(
        `✅ Project "${projectConfig.name}" created successfully!`,
        'Open Project'
      ).then(selection => {
        if (selection === 'Open Project') {
          const projectUri = vscode.Uri.file(
            this.workspaceManager.getAbsolutePath(projectPath) || projectPath
          );
          vscode.commands.executeCommand('vscode.openFolder', projectUri, true);
        }
      });

    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create project: ${error}`);
      throw error;
    }
  }

  /**
   * Process template content with project configuration
   */
  private processTemplate(content: string, config: ProjectConfig): string {
    return content
      .replace(/\{\{name\}\}/g, config.name)
      .replace(/\{\{description\}\}/g, config.description || '')
      .replace(/\{\{author\}\}/g, config.author || 'Unknown')
      .replace(/\{\{license\}\}/g, config.license || 'MIT')
      .replace(/\{\{version\}\}/g, config.version || '1.0.0')
      .replace(/\{\{repository\}\}/g, config.repository || '')
      .replace(/\{\{keywords\}\}/g, (config.keywords || []).join(', '))
      .replace(/\{\{features\}\}/g, (config.features || []).join('\n- '))
      .replace(/\{\{technologies\}\}/g, (config.technologies || []).join(', '));
  }

  /**
   * Create project metadata file
   */
  private async createProjectMetadata(
    projectPath: string,
    config: ProjectConfig,
    template: ProjectTemplate
  ): Promise<void> {
    const metadata = {
      projectId: this.generateProjectId(),
      name: config.name,
      description: config.description,
      type: config.type,
      template: template.id,
      createdAt: new Date().toISOString(),
      version: config.version || '1.0.0',
      author: config.author,
      license: config.license,
      repository: config.repository,
      keywords: config.keywords,
      features: config.features,
      technologies: config.technologies,
      aetherforge: {
        version: '1.0.0',
        lastModified: new Date().toISOString(),
        status: 'initialized'
      }
    };

    await this.workspaceManager.createFile(
      path.join(projectPath, '.aetherforge.json'),
      JSON.stringify(metadata, null, 2)
    );
  }

  /**
   * Generate unique project ID
   */
  private generateProjectId(): string {
    return `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Template content methods (simplified for brevity)
  private getReactPackageJson(): string {
    return `{
  "name": "{{name}}",
  "version": "{{version}}",
  "description": "{{description}}",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.24",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0",
    "vitest": "^0.34.0"
  }
}`;
  }

  private getViteConfig(): string {
    return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  }
})`;
  }

  private getTypeScriptConfig(): string {
    return `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`;
  }

  private getTailwindConfig(): string {
    return `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;
  }

  private getReactAppComponent(): string {
    return `import React from 'react'
import './App.css'

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Welcome to {{name}}
          </h1>
          <p className="text-gray-600 mb-6">
            {{description}}
          </p>
          <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Get Started
          </button>
        </div>
      </div>
    </div>
  )
}

export default App`;
  }

  private getReactMain(): string {
    return `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`;
  }

  private getBaseCss(): string {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}`;
  }

  private getIndexHtml(): string {
    return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{name}}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
  }

  private getReadmeTemplate(): string {
    return `# {{name}}

{{description}}

## Features

{{features}}

## Technologies

{{technologies}}

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository
\`\`\`bash
git clone {{repository}}
cd {{name}}
\`\`\`

2. Install dependencies
\`\`\`bash
npm install
\`\`\`

3. Start the development server
\`\`\`bash
npm run dev
\`\`\`

## Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run preview\` - Preview production build
- \`npm test\` - Run tests

## License

This project is licensed under the {{license}} License.

## Author

{{author}}
`;
  }

  private getGitignore(): string {
    return `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port`;
  }

  private getNodeApiPackageJson(): string {
    return `{
  "name": "{{name}}",
  "version": "{{version}}",
  "description": "{{description}}",
  "main": "dist/server.js",
  "scripts": {
    "start": "node dist/server.js",
    "dev": "ts-node-dev --respawn --transpile-only src/server.ts",
    "build": "tsc",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix"
  },
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.0",
    "mongoose": "^7.4.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "joi": "^17.9.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/morgan": "^1.9.0",
    "@types/bcryptjs": "^2.4.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/node": "^20.4.0",
    "@types/jest": "^29.5.0",
    "typescript": "^5.1.0",
    "ts-node-dev": "^2.0.0",
    "jest": "^29.6.0",
    "ts-jest": "^29.1.0",
    "eslint": "^8.45.0",
    "@typescript-eslint/eslint-plugin": "^6.2.0",
    "@typescript-eslint/parser": "^6.2.0"
  }
}`;
  }

  private getNodeApiApp(): string {
    return `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

dotenv.config();

const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to {{name}} API' });
});

app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

export default app;`;
  }

  private getNodeApiServer(): string {
    return `import app from './app';

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(\`🚀 Server running on port \${PORT}\`);
});`;
  }

  private getNodeApiEnv(): string {
    return `# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/{{name}}

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# Logging
LOG_LEVEL=info`;
  }

  private getPythonProjectConfig(): string {
    return `[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "{{name}}"
version = "{{version}}"
description = "{{description}}"
authors = [
    {name = "{{author}}", email = "<EMAIL>"},
]
license = {text = "{{license}}"}
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "click>=8.0.0",
    "rich>=10.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
]

[project.scripts]
{{name}} = "{{name}}.main:cli"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true`;
  }

  private getPythonCliMain(): string {
    return `#!/usr/bin/env python3
"""
{{name}} - {{description}}
"""

import click
from rich.console import Console
from rich.table import Table

console = Console()

@click.group()
@click.version_option(version="{{version}}")
def cli():
    """{{description}}"""
    pass

@cli.command()
@click.option('--name', default='World', help='Name to greet')
@click.option('--count', default=1, help='Number of greetings')
def hello(name: str, count: int):
    """Say hello to someone."""
    for _ in range(count):
        console.print(f"Hello, {name}!", style="bold green")

@cli.command()
def status():
    """Show application status."""
    table = Table(title="{{name}} Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")

    table.add_row("Application", "Running")
    table.add_row("Version", "{{version}}")
    table.add_row("Author", "{{author}}")

    console.print(table)

if __name__ == '__main__':
    cli()`;
  }

  private getPythonCliRequirements(): string {
    return `click>=8.0.0
rich>=10.0.0

# Development dependencies
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0
mypy>=0.991`;
  }
}
