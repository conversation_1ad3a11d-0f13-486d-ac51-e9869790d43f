{"name": "finaltestapp", "version": "1.0.0", "description": "Generated by Aetherforge: Create a simple task management web application with user authentication", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon server/index.js", "dev:client": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start": "node server/index.js", "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "concurrently": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.0.0", "nodemon": "^3.0.0", "typescript": "^5.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}