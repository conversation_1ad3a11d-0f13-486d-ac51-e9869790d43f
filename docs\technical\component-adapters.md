# Component Adapters: Service Integration Patterns

This guide covers creating component adapters to integrate external services and systems with Aetherforge's agent ecosystem.

## 🔧 Adapter Architecture

### Adapter Pattern Overview

```mermaid
graph TB
    subgraph "Agent System"
        A1[Analyst Agent]
        A2[Architect Agent]
        A3[Developer Agent]
        A4[QA Agent]
    end
    
    subgraph "Adapter Layer"
        CA1[Database Adapter]
        CA2[Cloud Adapter]
        CA3[API Adapter]
        CA4[Tool Adapter]
    end
    
    subgraph "External Services"
        DB[(Database)]
        CLOUD[Cloud Services]
        API[External APIs]
        TOOLS[Dev Tools]
    end
    
    A1 --> CA1
    A2 --> CA2
    A3 --> CA3
    A4 --> CA4
    
    CA1 --> DB
    CA2 --> CLOUD
    CA3 --> API
    CA4 --> TOOLS
```

### Adapter Types

| Adapter Type | Purpose | Interface | Examples |
|--------------|---------|-----------|----------|
| **Data Adapters** | Data source integration | Read/Write operations | Databases, file systems, APIs |
| **Service Adapters** | External service integration | Service-specific operations | Cloud providers, SaaS tools |
| **Tool Adapters** | Development tool integration | Tool-specific commands | IDEs, build tools, testing frameworks |
| **Protocol Adapters** | Communication protocol handling | Protocol translation | HTTP, gRPC, WebSocket, message queues |

## 🏗️ Base Adapter Framework

### Abstract Base Adapter

```python
# adapters/base_adapter.py
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

class AdapterStatus(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class OperationType(Enum):
    READ = "read"
    WRITE = "write"
    EXECUTE = "execute"
    QUERY = "query"

@dataclass
class AdapterCapability:
    name: str
    description: str
    operations: List[OperationType]
    required_permissions: List[str]
    optional_config: Dict[str, Any] = None

@dataclass
class AdapterConfig:
    adapter_id: str
    adapter_type: str
    connection_params: Dict[str, Any]
    timeout: int = 30
    retry_count: int = 3
    health_check_interval: int = 60

class BaseAdapter(ABC):
    """Base class for all component adapters"""
    
    def __init__(self, config: AdapterConfig):
        self.config = config
        self.status = AdapterStatus.DISCONNECTED
        self.capabilities: List[AdapterCapability] = []
        self.logger = logging.getLogger(f"adapter.{config.adapter_id}")
        self.connection = None
        self.health_check_task = None
    
    @abstractmethod
    async def connect(self) -> bool:
        """Establish connection to external service"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Close connection to external service"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Check adapter health and connection status"""
        pass
    
    @abstractmethod
    async def execute_operation(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute adapter-specific operation"""
        pass
    
    async def initialize(self) -> bool:
        """Initialize adapter and establish connection"""
        try:
            self.status = AdapterStatus.CONNECTING
            
            if await self.connect():
                self.status = AdapterStatus.CONNECTED
                await self._start_health_monitoring()
                self.logger.info(f"Adapter {self.config.adapter_id} initialized successfully")
                return True
            else:
                self.status = AdapterStatus.ERROR
                self.logger.error(f"Failed to connect adapter {self.config.adapter_id}")
                return False
                
        except Exception as e:
            self.status = AdapterStatus.ERROR
            self.logger.error(f"Adapter initialization failed: {e}")
            return False
    
    async def shutdown(self) -> None:
        """Shutdown adapter and cleanup resources"""
        try:
            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            await self.disconnect()
            self.status = AdapterStatus.DISCONNECTED
            self.logger.info(f"Adapter {self.config.adapter_id} shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Adapter shutdown failed: {e}")
    
    async def _start_health_monitoring(self):
        """Start periodic health monitoring"""
        async def health_monitor():
            while self.status == AdapterStatus.CONNECTED:
                try:
                    health_result = await self.health_check()
                    if not health_result.get("healthy", False):
                        self.logger.warning(f"Health check failed for {self.config.adapter_id}")
                        self.status = AdapterStatus.ERROR
                        break
                    
                    await asyncio.sleep(self.config.health_check_interval)
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Health monitoring error: {e}")
                    self.status = AdapterStatus.ERROR
                    break
        
        self.health_check_task = asyncio.create_task(health_monitor())
    
    def get_capabilities(self) -> List[AdapterCapability]:
        """Get adapter capabilities"""
        return self.capabilities
    
    def supports_operation(self, operation: str) -> bool:
        """Check if adapter supports specific operation"""
        return any(operation in cap.operations for cap in self.capabilities)
    
    async def validate_params(self, operation: str, params: Dict[str, Any]) -> bool:
        """Validate operation parameters"""
        # Override in subclasses for specific validation
        return True
```

### Database Adapter Example

```python
# adapters/database/postgresql_adapter.py
import asyncpg
from typing import Dict, Any, List
from adapters.base_adapter import BaseAdapter, AdapterConfig, AdapterCapability, OperationType

class PostgreSQLAdapter(BaseAdapter):
    """PostgreSQL database adapter"""
    
    def __init__(self, config: AdapterConfig):
        super().__init__(config)
        
        # Define capabilities
        self.capabilities = [
            AdapterCapability(
                name="query_execution",
                description="Execute SQL queries",
                operations=[OperationType.READ, OperationType.QUERY],
                required_permissions=["database_read"]
            ),
            AdapterCapability(
                name="data_modification",
                description="Insert, update, delete data",
                operations=[OperationType.WRITE],
                required_permissions=["database_write"]
            ),
            AdapterCapability(
                name="schema_management",
                description="Create, alter, drop tables",
                operations=[OperationType.EXECUTE],
                required_permissions=["database_admin"]
            )
        ]
        
        self.pool = None
    
    async def connect(self) -> bool:
        """Connect to PostgreSQL database"""
        try:
            connection_string = self._build_connection_string()
            self.pool = await asyncpg.create_pool(
                connection_string,
                min_size=1,
                max_size=10,
                command_timeout=self.config.timeout
            )
            return True
            
        except Exception as e:
            self.logger.error(f"PostgreSQL connection failed: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from PostgreSQL database"""
        try:
            if self.pool:
                await self.pool.close()
                self.pool = None
            return True
            
        except Exception as e:
            self.logger.error(f"PostgreSQL disconnection failed: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check database connection health"""
        try:
            if not self.pool:
                return {"healthy": False, "error": "No connection pool"}
            
            async with self.pool.acquire() as connection:
                result = await connection.fetchval("SELECT 1")
                
                return {
                    "healthy": result == 1,
                    "pool_size": len(self.pool._holders),
                    "available_connections": len([h for h in self.pool._holders if h._con is None])
                }
                
        except Exception as e:
            return {"healthy": False, "error": str(e)}
    
    async def execute_operation(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute database operation"""
        if not await self.validate_params(operation, params):
            raise ValueError(f"Invalid parameters for operation: {operation}")
        
        if operation == "query":
            return await self._execute_query(params)
        elif operation == "insert":
            return await self._execute_insert(params)
        elif operation == "update":
            return await self._execute_update(params)
        elif operation == "delete":
            return await self._execute_delete(params)
        elif operation == "create_table":
            return await self._create_table(params)
        else:
            raise ValueError(f"Unsupported operation: {operation}")
    
    async def _execute_query(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute SELECT query"""
        query = params["query"]
        query_params = params.get("params", [])
        
        async with self.pool.acquire() as connection:
            rows = await connection.fetch(query, *query_params)
            return {
                "status": "success",
                "rows": [dict(row) for row in rows],
                "row_count": len(rows)
            }
    
    async def _execute_insert(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute INSERT statement"""
        table = params["table"]
        data = params["data"]
        
        columns = list(data.keys())
        values = list(data.values())
        placeholders = [f"${i+1}" for i in range(len(values))]
        
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)}) RETURNING *"
        
        async with self.pool.acquire() as connection:
            row = await connection.fetchrow(query, *values)
            return {
                "status": "success",
                "inserted_row": dict(row) if row else None
            }
    
    async def _create_table(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create table from schema"""
        table_name = params["table_name"]
        schema = params["schema"]
        
        columns = []
        for col_name, col_def in schema.items():
            column_sql = f"{col_name} {col_def['type']}"
            if col_def.get("primary_key"):
                column_sql += " PRIMARY KEY"
            if col_def.get("not_null"):
                column_sql += " NOT NULL"
            if col_def.get("unique"):
                column_sql += " UNIQUE"
            columns.append(column_sql)
        
        create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
        
        async with self.pool.acquire() as connection:
            await connection.execute(create_sql)
            return {"status": "success", "table_created": table_name}
    
    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string"""
        params = self.config.connection_params
        return f"postgresql://{params['user']}:{params['password']}@{params['host']}:{params['port']}/{params['database']}"
```

### Cloud Service Adapter

```python
# adapters/cloud/aws_adapter.py
import boto3
from typing import Dict, Any, List
from adapters.base_adapter import BaseAdapter, AdapterConfig, AdapterCapability, OperationType

class AWSAdapter(BaseAdapter):
    """AWS cloud services adapter"""
    
    def __init__(self, config: AdapterConfig):
        super().__init__(config)
        
        self.capabilities = [
            AdapterCapability(
                name="lambda_management",
                description="Deploy and manage Lambda functions",
                operations=[OperationType.EXECUTE, OperationType.WRITE],
                required_permissions=["aws_lambda"]
            ),
            AdapterCapability(
                name="s3_operations",
                description="S3 bucket and object operations",
                operations=[OperationType.READ, OperationType.WRITE],
                required_permissions=["aws_s3"]
            ),
            AdapterCapability(
                name="ecs_deployment",
                description="Deploy containerized applications",
                operations=[OperationType.EXECUTE],
                required_permissions=["aws_ecs"]
            )
        ]
        
        self.session = None
        self.clients = {}
    
    async def connect(self) -> bool:
        """Initialize AWS session and clients"""
        try:
            params = self.config.connection_params
            self.session = boto3.Session(
                aws_access_key_id=params["access_key"],
                aws_secret_access_key=params["secret_key"],
                region_name=params.get("region", "us-east-1")
            )
            
            # Initialize commonly used clients
            self.clients = {
                "lambda": self.session.client("lambda"),
                "s3": self.session.client("s3"),
                "ecs": self.session.client("ecs"),
                "iam": self.session.client("iam")
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"AWS connection failed: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """Cleanup AWS clients"""
        try:
            # Close client connections
            for client in self.clients.values():
                if hasattr(client, "close"):
                    client.close()
            
            self.clients = {}
            self.session = None
            return True
            
        except Exception as e:
            self.logger.error(f"AWS disconnection failed: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check AWS service connectivity"""
        try:
            # Test STS to verify credentials
            sts_client = self.session.client("sts")
            identity = sts_client.get_caller_identity()
            
            return {
                "healthy": True,
                "account_id": identity.get("Account"),
                "user_id": identity.get("UserId"),
                "arn": identity.get("Arn")
            }
            
        except Exception as e:
            return {"healthy": False, "error": str(e)}
    
    async def execute_operation(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute AWS operation"""
        if operation == "deploy_lambda":
            return await self._deploy_lambda(params)
        elif operation == "create_s3_bucket":
            return await self._create_s3_bucket(params)
        elif operation == "upload_to_s3":
            return await self._upload_to_s3(params)
        elif operation == "deploy_ecs_service":
            return await self._deploy_ecs_service(params)
        else:
            raise ValueError(f"Unsupported operation: {operation}")
    
    async def _deploy_lambda(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy Lambda function"""
        function_name = params["function_name"]
        code_zip = params["code_zip"]
        handler = params["handler"]
        runtime = params.get("runtime", "python3.9")
        
        try:
            lambda_client = self.clients["lambda"]
            
            # Try to update existing function
            try:
                response = lambda_client.update_function_code(
                    FunctionName=function_name,
                    ZipFile=code_zip
                )
            except lambda_client.exceptions.ResourceNotFoundException:
                # Function doesn't exist, create it
                response = lambda_client.create_function(
                    FunctionName=function_name,
                    Runtime=runtime,
                    Role=await self._get_lambda_role_arn(),
                    Handler=handler,
                    Code={"ZipFile": code_zip}
                )
            
            return {
                "status": "success",
                "function_arn": response["FunctionArn"],
                "function_name": response["FunctionName"],
                "runtime": response["Runtime"]
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def _create_s3_bucket(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create S3 bucket"""
        bucket_name = params["bucket_name"]
        region = params.get("region", self.session.region_name)
        
        try:
            s3_client = self.clients["s3"]
            
            if region == "us-east-1":
                response = s3_client.create_bucket(Bucket=bucket_name)
            else:
                response = s3_client.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={"LocationConstraint": region}
                )
            
            return {
                "status": "success",
                "bucket_name": bucket_name,
                "location": response["Location"]
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def _get_lambda_role_arn(self) -> str:
        """Get or create Lambda execution role"""
        role_name = "aetherforge-lambda-execution-role"
        
        try:
            iam_client = self.clients["iam"]
            
            # Try to get existing role
            try:
                response = iam_client.get_role(RoleName=role_name)
                return response["Role"]["Arn"]
            except iam_client.exceptions.NoSuchEntityException:
                # Create role
                trust_policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"Service": "lambda.amazonaws.com"},
                            "Action": "sts:AssumeRole"
                        }
                    ]
                }
                
                response = iam_client.create_role(
                    RoleName=role_name,
                    AssumeRolePolicyDocument=json.dumps(trust_policy)
                )
                
                # Attach basic execution policy
                iam_client.attach_role_policy(
                    RoleName=role_name,
                    PolicyArn="arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
                )
                
                return response["Role"]["Arn"]
                
        except Exception as e:
            raise Exception(f"Failed to get/create Lambda role: {e}")
```

### API Service Adapter

```python
# adapters/api/rest_adapter.py
import aiohttp
from typing import Dict, Any, List, Optional
from adapters.base_adapter import BaseAdapter, AdapterConfig, AdapterCapability, OperationType

class RESTAPIAdapter(BaseAdapter):
    """Generic REST API adapter"""
    
    def __init__(self, config: AdapterConfig):
        super().__init__(config)
        
        self.capabilities = [
            AdapterCapability(
                name="http_requests",
                description="Make HTTP requests to REST APIs",
                operations=[OperationType.READ, OperationType.WRITE, OperationType.EXECUTE],
                required_permissions=["network_access"]
            ),
            AdapterCapability(
                name="authentication",
                description="Handle API authentication",
                operations=[OperationType.EXECUTE],
                required_permissions=["api_credentials"]
            )
        ]
        
        self.session = None
        self.base_url = None
        self.auth_headers = {}
    
    async def connect(self) -> bool:
        """Initialize HTTP session"""
        try:
            params = self.config.connection_params
            self.base_url = params["base_url"]
            
            # Setup authentication headers
            auth_type = params.get("auth_type", "none")
            if auth_type == "api_key":
                self.auth_headers["X-API-Key"] = params["api_key"]
            elif auth_type == "bearer":
                self.auth_headers["Authorization"] = f"Bearer {params['token']}"
            
            # Create session with timeout
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(
                headers=self.auth_headers,
                timeout=timeout
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"REST API connection failed: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """Close HTTP session"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            return True
            
        except Exception as e:
            self.logger.error(f"REST API disconnection failed: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        try:
            health_endpoint = self.config.connection_params.get("health_endpoint", "/health")
            
            async with self.session.get(f"{self.base_url}{health_endpoint}") as response:
                if response.status == 200:
                    return {"healthy": True, "status_code": response.status}
                else:
                    return {"healthy": False, "status_code": response.status}
                    
        except Exception as e:
            return {"healthy": False, "error": str(e)}
    
    async def execute_operation(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute HTTP operation"""
        if operation == "get":
            return await self._make_request("GET", params)
        elif operation == "post":
            return await self._make_request("POST", params)
        elif operation == "put":
            return await self._make_request("PUT", params)
        elif operation == "delete":
            return await self._make_request("DELETE", params)
        else:
            raise ValueError(f"Unsupported operation: {operation}")
    
    async def _make_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request"""
        endpoint = params["endpoint"]
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        request_params = {
            "params": params.get("query_params"),
            "json": params.get("json_data"),
            "data": params.get("form_data"),
            "headers": params.get("headers", {})
        }
        
        # Remove None values
        request_params = {k: v for k, v in request_params.items() if v is not None}
        
        try:
            async with self.session.request(method, url, **request_params) as response:
                response_data = {
                    "status_code": response.status,
                    "headers": dict(response.headers)
                }
                
                if response.content_type == "application/json":
                    response_data["data"] = await response.json()
                else:
                    response_data["data"] = await response.text()
                
                return response_data
                
        except Exception as e:
            return {"status_code": 0, "error": str(e)}
```

## 🔧 Adapter Registry and Management

### Adapter Registry

```python
# adapters/registry.py
from typing import Dict, Type, List, Optional
from adapters.base_adapter import BaseAdapter, AdapterConfig
import importlib

class AdapterRegistry:
    """Registry for managing adapter types and instances"""
    
    _adapter_types: Dict[str, Type[BaseAdapter]] = {}
    _active_adapters: Dict[str, BaseAdapter] = {}
    
    @classmethod
    def register_adapter_type(cls, adapter_type: str, adapter_class: Type[BaseAdapter]):
        """Register an adapter type"""
        cls._adapter_types[adapter_type] = adapter_class
    
    @classmethod
    def create_adapter(cls, config: AdapterConfig) -> BaseAdapter:
        """Create adapter instance from config"""
        adapter_type = config.adapter_type
        
        if adapter_type not in cls._adapter_types:
            raise ValueError(f"Unknown adapter type: {adapter_type}")
        
        adapter_class = cls._adapter_types[adapter_type]
        return adapter_class(config)
    
    @classmethod
    async def initialize_adapter(cls, config: AdapterConfig) -> bool:
        """Initialize and register adapter"""
        adapter = cls.create_adapter(config)
        
        if await adapter.initialize():
            cls._active_adapters[config.adapter_id] = adapter
            return True
        
        return False
    
    @classmethod
    async def shutdown_adapter(cls, adapter_id: str) -> bool:
        """Shutdown and unregister adapter"""
        if adapter_id in cls._active_adapters:
            adapter = cls._active_adapters[adapter_id]
            await adapter.shutdown()
            del cls._active_adapters[adapter_id]
            return True
        
        return False
    
    @classmethod
    def get_adapter(cls, adapter_id: str) -> Optional[BaseAdapter]:
        """Get active adapter by ID"""
        return cls._active_adapters.get(adapter_id)
    
    @classmethod
    def list_adapters(cls) -> List[Dict[str, Any]]:
        """List all active adapters"""
        return [
            {
                "adapter_id": adapter_id,
                "adapter_type": adapter.config.adapter_type,
                "status": adapter.status.value
            }
            for adapter_id, adapter in cls._active_adapters.items()
        ]

# Register built-in adapter types
from adapters.database.postgresql_adapter import PostgreSQLAdapter
from adapters.cloud.aws_adapter import AWSAdapter
from adapters.api.rest_adapter import RESTAPIAdapter

AdapterRegistry.register_adapter_type("postgresql", PostgreSQLAdapter)
AdapterRegistry.register_adapter_type("aws", AWSAdapter)
AdapterRegistry.register_adapter_type("rest_api", RESTAPIAdapter)
```

## 📊 Testing Adapters

### Adapter Testing Framework

```python
# tests/test_adapters.py
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from adapters.base_adapter import BaseAdapter, AdapterConfig

class MockAdapter(BaseAdapter):
    """Mock adapter for testing"""
    
    async def connect(self) -> bool:
        return True
    
    async def disconnect(self) -> bool:
        return True
    
    async def health_check(self) -> Dict[str, Any]:
        return {"healthy": True}
    
    async def execute_operation(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        return {"status": "success", "operation": operation, "params": params}

@pytest.fixture
def adapter_config():
    return AdapterConfig(
        adapter_id="test_adapter",
        adapter_type="mock",
        connection_params={"test": "value"}
    )

@pytest.fixture
async def mock_adapter(adapter_config):
    adapter = MockAdapter(adapter_config)
    await adapter.initialize()
    yield adapter
    await adapter.shutdown()

@pytest.mark.asyncio
async def test_adapter_initialization(mock_adapter):
    """Test adapter initialization"""
    assert mock_adapter.status.value == "connected"
    assert mock_adapter.config.adapter_id == "test_adapter"

@pytest.mark.asyncio
async def test_adapter_operation(mock_adapter):
    """Test adapter operation execution"""
    result = await mock_adapter.execute_operation("test_op", {"param": "value"})
    
    assert result["status"] == "success"
    assert result["operation"] == "test_op"
    assert result["params"]["param"] == "value"

@pytest.mark.asyncio
async def test_adapter_health_check(mock_adapter):
    """Test adapter health check"""
    health = await mock_adapter.health_check()
    assert health["healthy"] == True
```

## 🔗 Next Steps

- **[API Extensions](api-extensions.md)** - Extend the REST API
- **[Custom Agents](custom-agents.md)** - Create specialized agents
- **[Plugin Development](plugin-development.md)** - Create plugins
- **[Integration Guide](integration-guide.md)** - External system integration

## 📖 Resources

- [Adapter Examples Repository](https://github.com/aetherforge/adapter-examples)
- [Community Adapters](https://github.com/aetherforge/community-adapters)
- [Adapter Development Best Practices](https://docs.aetherforge.dev/best-practices/adapters)
