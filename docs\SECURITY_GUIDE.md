# Aetherforge Security Guide

## API Key Security

### Overview

Aetherforge implements enterprise-grade security for API key management, ensuring your credentials are protected both at rest and in transit.

## Security Architecture

### Encryption at Rest

**Algorithm**: AES-256 with <PERSON><PERSON><PERSON> symmetric encryption
- **Key Size**: 256-bit encryption keys
- **Mode**: <PERSON><PERSON><PERSON> (AES-128 in CBC mode with HMAC-SHA256 for authentication)
- **Authentication**: Built-in message authentication prevents tampering

**Key Derivation**:
- **Function**: PBKDF2-HMAC-SHA256
- **Iterations**: 100,000 (exceeds OWASP recommendations)
- **Salt**: Unique 16-byte random salt per installation
- **Master Key**: Derived from environment variable or secure default

### Storage Security

**File Locations**:
```
~/.aetherforge/
├── keys.enc      # Encrypted API keys
├── key.key       # Derived encryption key
└── salt          # Random salt for key derivation
```

**File Permissions**:
- Directory: `700` (owner read/write/execute only)
- Files: `600` (owner read/write only)

### Network Security

**TLS/SSL**: All API communications use HTTPS/TLS 1.2+
**Certificate Validation**: Full certificate chain validation
**No Proxy Storage**: API keys never stored in network proxies or caches

## Security Best Practices

### 1. Master Key Management

**Set Custom Master Key**:
```bash
export AETHERFORGE_MASTER_KEY="your-secure-passphrase-here"
```

**Requirements**:
- Minimum 20 characters
- Mix of uppercase, lowercase, numbers, symbols
- Unique to your installation
- Store securely (password manager recommended)

### 2. Environment Variables

**Secure Environment Setup**:
```bash
# Use environment variables for additional security
export OPENAI_API_KEY="sk-your-openai-key"
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"
export AETHERFORGE_MASTER_KEY="your-master-key"
```

**Environment File Security**:
```bash
# Secure .env file permissions
chmod 600 .env
```

### 3. File System Security

**Directory Permissions**:
```bash
# Secure the Aetherforge directory
chmod 700 ~/.aetherforge/
chmod 600 ~/.aetherforge/*
```

**Backup Security**:
```bash
# If backing up, encrypt the backup
tar -czf - ~/.aetherforge/ | gpg -c > aetherforge-backup.tar.gz.gpg
```

### 4. API Key Hygiene

**Key Rotation**:
- Rotate API keys every 90 days
- Immediately rotate if compromise suspected
- Use provider key rotation features when available

**Key Validation**:
```bash
# Regularly test your keys
python aetherforge-keys test openai
python aetherforge-keys test anthropic
```

**Monitoring**:
- Monitor API usage for unusual patterns
- Set up billing alerts with providers
- Review API logs regularly

## Threat Model

### Protected Against

✅ **Data at Rest Attacks**
- File system access by unauthorized users
- Disk theft or loss
- Backup compromise

✅ **Network Interception**
- Man-in-the-middle attacks
- Network sniffing
- Proxy logging

✅ **Application-Level Attacks**
- Memory dumps (keys encrypted until use)
- Log file exposure
- Configuration file leaks

✅ **Insider Threats**
- Unauthorized access to configuration
- Accidental key exposure
- Privilege escalation

### Limitations

⚠️ **Runtime Memory**
- Keys temporarily decrypted in memory during use
- Process memory dumps could expose keys
- Mitigation: Use secure memory allocation where possible

⚠️ **Root/Administrator Access**
- Users with root access can access encrypted files
- Mitigation: Secure system administration practices

⚠️ **Social Engineering**
- Users may be tricked into revealing master keys
- Mitigation: Security awareness training

## Compliance Considerations

### Data Protection Regulations

**GDPR Compliance**:
- Data minimization: Only necessary API keys stored
- Encryption: Strong encryption protects personal data
- Right to erasure: Keys can be completely removed

**SOC 2 Type II**:
- Access controls: File system permissions
- Encryption: Industry-standard algorithms
- Monitoring: Audit trails for key operations

### Industry Standards

**NIST Cybersecurity Framework**:
- Identify: Asset inventory of API keys
- Protect: Encryption and access controls
- Detect: Monitoring and validation
- Respond: Key rotation procedures
- Recover: Backup and restore procedures

**OWASP Guidelines**:
- Cryptographic storage: AES-256 encryption
- Key management: Secure key derivation
- Authentication: HMAC message authentication

## Incident Response

### Suspected Key Compromise

1. **Immediate Actions**:
   ```bash
   # Revoke compromised key at provider
   # Remove from Aetherforge
   python aetherforge-keys remove <provider>
   ```

2. **Generate New Key**:
   - Create new API key at provider
   - Update Aetherforge configuration
   - Test new key functionality

3. **Investigation**:
   - Review access logs
   - Check for unauthorized usage
   - Identify compromise vector

4. **Prevention**:
   - Address security gap
   - Update security procedures
   - Consider additional monitoring

### System Compromise

1. **Secure New Environment**:
   ```bash
   # Remove all stored keys
   rm -rf ~/.aetherforge/
   
   # Regenerate all API keys
   # Reconfigure with new keys
   ```

2. **Audit and Recovery**:
   - Review all API usage
   - Check for data exfiltration
   - Implement additional security measures

## Security Monitoring

### Automated Monitoring

**Key Validation Script**:
```bash
#!/bin/bash
# Daily key validation
python aetherforge-keys test openai || echo "OpenAI key issue"
python aetherforge-keys test anthropic || echo "Anthropic key issue"
```

**Usage Monitoring**:
```python
# Monitor API usage patterns
import logging
from api_manager import APIManager

logging.basicConfig(level=logging.INFO)
api_manager = APIManager()

# Log all API calls for monitoring
```

### Manual Audits

**Monthly Security Review**:
- [ ] Verify file permissions
- [ ] Check for unauthorized access
- [ ] Review API usage patterns
- [ ] Test key validation
- [ ] Update master key if needed

**Quarterly Security Assessment**:
- [ ] Rotate all API keys
- [ ] Review security procedures
- [ ] Update dependencies
- [ ] Conduct penetration testing
- [ ] Review compliance requirements

## Advanced Security Features

### Hardware Security Modules (HSM)

For enterprise deployments, consider HSM integration:

```python
# Example HSM integration (requires additional setup)
from api_manager import SecureKeyStorage

class HSMKeyStorage(SecureKeyStorage):
    def __init__(self, hsm_config):
        self.hsm = HSMClient(hsm_config)
    
    def store_key(self, provider, key):
        return self.hsm.encrypt_and_store(provider, key)
```

### Multi-Factor Authentication

Implement additional authentication layers:

```python
# Example MFA integration
class MFAKeyStorage(SecureKeyStorage):
    def load_key(self, provider):
        if not self.verify_mfa():
            raise SecurityError("MFA verification required")
        return super().load_key(provider)
```

### Audit Logging

Enhanced audit trail:

```python
import logging
from datetime import datetime

class AuditLogger:
    def log_key_access(self, provider, operation, user):
        logging.info(f"{datetime.now()}: {user} {operation} {provider} key")
```

## Security Checklist

### Initial Setup
- [ ] Set custom master key
- [ ] Secure file permissions
- [ ] Configure environment variables
- [ ] Test key validation
- [ ] Document security procedures

### Ongoing Maintenance
- [ ] Regular key rotation (90 days)
- [ ] Monitor API usage
- [ ] Update dependencies
- [ ] Review access logs
- [ ] Backup encrypted keys

### Incident Response
- [ ] Document response procedures
- [ ] Test incident response plan
- [ ] Maintain emergency contacts
- [ ] Regular security training
- [ ] Update threat model

## Contact and Support

For security-related questions or to report vulnerabilities:

1. **Security Issues**: Report privately to maintainers
2. **General Questions**: Use project documentation
3. **Best Practices**: Follow this security guide
4. **Compliance**: Consult with security professionals

Remember: Security is a shared responsibility between the Aetherforge system and its users. Follow these guidelines to maintain the highest level of security for your API keys and data.
