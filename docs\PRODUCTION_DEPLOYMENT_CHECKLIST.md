# TaoForge Production Deployment Checklist

## Pre-Deployment Requirements

### ✅ Infrastructure Requirements
- [ ] **Server Specifications**
  - [ ] Minimum 8GB RAM (16GB recommended)
  - [ ] Minimum 50GB disk space (100GB recommended)
  - [ ] 4+ CPU cores
  - [ ] Ubuntu 20.04+ or CentOS 8+ or similar Linux distribution

- [ ] **Network Requirements**
  - [ ] Static IP address or domain name configured
  - [ ] Ports 80, 443, 8000-8503 accessible
  - [ ] SSL certificates obtained (Let's Encrypt or commercial)
  - [ ] DNS records configured

- [ ] **Software Dependencies**
  - [ ] Docker Engine 20.10+ installed
  - [ ] Docker Compose 2.0+ installed
  - [ ] Git installed
  - [ ] Nginx (if not using containerized version)

### ✅ Security Configuration
- [ ] **SSL/TLS Setup**
  - [ ] SSL certificates installed in `nginx/ssl/`
  - [ ] HTTPS redirect configured
  - [ ] Security headers enabled in nginx.conf

- [ ] **Access Control**
  - [ ] Firewall configured (UFW/iptables)
  - [ ] SSH key-based authentication
  - [ ] Non-root user for deployment
  - [ ] Monitoring access credentials set

- [ ] **Environment Variables**
  - [ ] `OPENAI_API_KEY` - OpenAI API key for AI functionality
  - [ ] `POSTGRES_PASSWORD` - Strong database password
  - [ ] `JWT_SECRET` - Random JWT signing secret
  - [ ] `DEPLOYMENT_WEBHOOK_URL` - Slack/Teams webhook for notifications
  - [ ] `ALERT_WEBHOOK_URL` - Alert notification webhook

### ✅ Data & Backup Strategy
- [ ] **Database Setup**
  - [ ] PostgreSQL backup strategy defined
  - [ ] Database migration plan prepared
  - [ ] Connection pooling configured

- [ ] **Volume Management**
  - [ ] Persistent volumes configured
  - [ ] Backup schedule established
  - [ ] Recovery procedures documented

## Deployment Process

### ✅ Pre-Deployment Verification
```bash
# Run production readiness check
./scripts/verify_production_readiness.sh production

# Verify Docker configuration
docker compose -f docker-compose.prod.yml config

# Check system resources
df -h
free -h
```

### ✅ Deployment Steps
1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/taoforge.git
   cd taoforge
   git checkout main
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   nano .env
   ```

3. **SSL Certificate Setup**
   ```bash
   # Copy SSL certificates
   cp your-cert.pem nginx/ssl/cert.pem
   cp your-key.pem nginx/ssl/key.pem
   chmod 600 nginx/ssl/*
   ```

4. **Run Deployment Script**
   ```bash
   # Production deployment with health checks
   sudo ./scripts/deploy_production.sh
   ```

5. **Verify Deployment**
   ```bash
   # Check all services are running
   docker compose -f docker-compose.prod.yml ps

   # Run health checks
   ./scripts/health_check.sh

   # Test API endpoints
   curl -f http://localhost:8000/health
   ```

### ✅ Post-Deployment Configuration

#### Monitoring Setup
- [ ] **Grafana Dashboard**
  - [ ] Access Grafana at `http://your-domain/grafana/`
  - [ ] Default credentials: admin/admin (change immediately)
  - [ ] Import TaoForge dashboards
  - [ ] Configure alert channels

- [ ] **Prometheus Metrics**
  - [ ] Verify metrics collection at `http://your-domain/prometheus/`
  - [ ] Configure retention policies
  - [ ] Set up alerting rules

#### Log Management
- [ ] **Log Aggregation**
  - [ ] Configure log rotation
  - [ ] Set up centralized logging (optional)
  - [ ] Monitor log disk usage

#### Backup Verification
- [ ] **Test Backup Process**
  ```bash
  # Create initial backup
  ./scripts/backup.sh

  # Verify backup integrity
  ls -la /opt/aetherforge/backups/
  ```

## Production Validation

### ✅ Functional Testing
- [ ] **API Endpoints**
  - [ ] Health check: `GET /health`
  - [ ] Project creation: `POST /projects`
  - [ ] Component status: `GET /components/status`

- [ ] **Component Integration**
  - [ ] Orchestrator ↔ Archon communication
  - [ ] Orchestrator ↔ MCP-Crawl4AI communication
  - [ ] Orchestrator ↔ Pheromind communication
  - [ ] Orchestrator ↔ BMAD communication

- [ ] **End-to-End Workflow**
  - [ ] Create test project via API
  - [ ] Verify project generation pipeline
  - [ ] Check generated project structure
  - [ ] Validate project metadata

### ✅ Performance Testing
- [ ] **Load Testing**
  ```bash
  # Test concurrent project creation
  ab -n 10 -c 2 -H "Content-Type: application/json" \
     -p test-project.json http://localhost:8000/projects
  ```

- [ ] **Resource Monitoring**
  - [ ] CPU usage under load
  - [ ] Memory consumption
  - [ ] Disk I/O performance
  - [ ] Network throughput

### ✅ Security Validation
- [ ] **SSL/TLS Testing**
  ```bash
  # Test SSL configuration
  curl -I https://your-domain/
  
  # Check SSL rating
  # Visit: https://www.ssllabs.com/ssltest/
  ```

- [ ] **Security Headers**
  - [ ] X-Frame-Options: DENY
  - [ ] X-Content-Type-Options: nosniff
  - [ ] X-XSS-Protection: 1; mode=block
  - [ ] Strict-Transport-Security (HSTS)

## Monitoring & Maintenance

### ✅ Daily Operations
- [ ] **Health Monitoring**
  ```bash
  # Automated health checks (cron job)
  0 */6 * * * /path/to/taoforge/scripts/health_check.sh
  ```

- [ ] **Log Review**
  - [ ] Check application logs for errors
  - [ ] Monitor system resource usage
  - [ ] Review security logs

### ✅ Weekly Maintenance
- [ ] **Backup Verification**
  - [ ] Test backup restoration process
  - [ ] Verify backup integrity
  - [ ] Clean up old backups

- [ ] **Security Updates**
  - [ ] Update base Docker images
  - [ ] Apply system security patches
  - [ ] Review access logs

### ✅ Monthly Reviews
- [ ] **Performance Analysis**
  - [ ] Review Grafana dashboards
  - [ ] Analyze usage patterns
  - [ ] Optimize resource allocation

- [ ] **Capacity Planning**
  - [ ] Monitor growth trends
  - [ ] Plan infrastructure scaling
  - [ ] Update disaster recovery procedures

## Troubleshooting

### ✅ Common Issues
- [ ] **Service Won't Start**
  ```bash
  # Check logs
  docker compose -f docker-compose.prod.yml logs [service]
  
  # Check resource usage
  docker stats
  ```

- [ ] **Database Connection Issues**
  ```bash
  # Test database connectivity
  docker compose -f docker-compose.prod.yml exec postgres pg_isready
  
  # Check connection pool
  docker compose -f docker-compose.prod.yml exec postgres psql -U aetherforge -c "SELECT * FROM pg_stat_activity;"
  ```

- [ ] **High Resource Usage**
  ```bash
  # Monitor resource usage
  htop
  iotop
  
  # Check container resources
  docker stats --no-stream
  ```

### ✅ Emergency Procedures
- [ ] **Rollback Process**
  ```bash
  # Automatic rollback (if enabled)
  ROLLBACK_ENABLED=true ./scripts/deploy_production.sh
  
  # Manual rollback
  ./scripts/restore.sh /path/to/backup
  ```

- [ ] **Service Recovery**
  ```bash
  # Restart specific service
  docker compose -f docker-compose.prod.yml restart [service]
  
  # Full system restart
  docker compose -f docker-compose.prod.yml down
  docker compose -f docker-compose.prod.yml up -d
  ```

## Sign-off

### ✅ Deployment Approval
- [ ] **Technical Lead Approval**
  - [ ] Code review completed
  - [ ] Security review passed
  - [ ] Performance benchmarks met

- [ ] **Operations Team Approval**
  - [ ] Infrastructure ready
  - [ ] Monitoring configured
  - [ ] Backup procedures tested

- [ ] **Final Deployment**
  - [ ] Deployment completed successfully
  - [ ] All health checks passing
  - [ ] Monitoring alerts configured
  - [ ] Documentation updated

**Deployment Date:** _______________  
**Deployed By:** _______________  
**Approved By:** _______________
