#!/usr/bin/env python3
"""
Comprehensive test suite for the QA Agent
Tests the actual QAAgent class and its methods
"""

import pytest
import asyncio
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
import sys
import os
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qa_agent import (
    QAAgent,
    QAContext,
    QualityLevel,
    TestType,
    TestStatus,
    TestResult,
    TestSuite,
    QualityReport,
    QAAgentError,
    ValidationError,
    TestExecutionError,
    CoverageError,
    QAAgentExecutor
)
from analyst_agent import ProjectSpecification
from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier


class TestQAAgent:
    """Test the QAAgent class"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_path = Path(self.temp_dir) / "test_project"
        self.project_path.mkdir(parents=True, exist_ok=True)
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_project(self, project_type: str = "javascript"):
        """Create a test project structure"""
        # Create package.json
        package_json = {
            "name": "test-project",
            "version": "1.0.0",
            "dependencies": {"express": "^4.18.2"},
            "devDependencies": {"jest": "^29.0.0"},
            "scripts": {"test": "jest", "test:coverage": "jest --coverage"}
        }
        
        with open(self.project_path / "package.json", 'w') as f:
            json.dump(package_json, f, indent=2)
        
        # Create source files
        src_dir = self.project_path / "src"
        src_dir.mkdir(exist_ok=True)
        
        with open(src_dir / "app.js", 'w') as f:
            f.write("const express = require('express');\nconst app = express();\nmodule.exports = app;")
        
        # Create test files
        tests_dir = self.project_path / "tests"
        tests_dir.mkdir(exist_ok=True)
        
        with open(tests_dir / "app.test.js", 'w') as f:
            f.write("test('app should be defined', () => { expect(true).toBe(true); });")
    
    def create_test_specification(self) -> ProjectSpecification:
        """Create a test project specification"""
        return ProjectSpecification(
            project_name="Test Project",
            description="A test project for QA validation",
            requirements={
                "functional": [
                    "User authentication",
                    "Data validation",
                    "Error handling"
                ]
            },
            user_stories=[
                {
                    "id": "US001",
                    "title": "User Login",
                    "description": "As a user, I want to log in",
                    "acceptance_criteria": ["Valid login", "Invalid rejection"]
                }
            ],
            technical_stack={},
            architecture={},
            constraints=[],
            success_metrics=[],
            risks=[]
        )
    
    @patch('qa_agent.MCPRAGClient')
    def test_qa_agent_initialization(self, mock_mcp_client):
        """Test QAAgent initialization"""
        agent = QAAgent()
        
        assert agent is not None
        assert hasattr(agent, 'mcp_client')
        assert hasattr(agent, 'test_frameworks')
        assert hasattr(agent, 'quality_gates')
        assert hasattr(agent, 'validation_rules')
        assert hasattr(agent, 'coverage_thresholds')
    
    @patch('qa_agent.MCPRAGClient')
    def test_qa_context_creation(self, mock_mcp_client):
        """Test QA context creation"""
        self.create_test_project()
        
        context = QAContext(
            project_path=self.project_path,
            quality_level=QualityLevel.STANDARD,
            project_specification=self.create_test_specification()
        )
        
        assert context.project_path == self.project_path
        assert context.quality_level == QualityLevel.STANDARD
        assert context.project_specification is not None
    
    @patch('qa_agent.MCPRAGClient')
    def test_input_validation_success(self, mock_mcp_client):
        """Test successful input validation"""
        agent = QAAgent()
        self.create_test_project()
        
        context = QAContext(
            project_path=self.project_path,
            quality_level=QualityLevel.STANDARD
        )
        
        # Should not raise any exceptions
        asyncio.run(agent._validate_qa_inputs(context))
    
    @patch('qa_agent.MCPRAGClient')
    def test_input_validation_missing_project(self, mock_mcp_client):
        """Test input validation with missing project"""
        agent = QAAgent()
        
        context = QAContext(
            project_path=Path("/nonexistent/path"),
            quality_level=QualityLevel.STANDARD
        )
        
        with pytest.raises(ValidationError, match="Project path does not exist"):
            asyncio.run(agent._validate_qa_inputs(context))
    
    @patch('qa_agent.MCPRAGClient')
    def test_project_structure_analysis(self, mock_mcp_client):
        """Test project structure analysis"""
        agent = QAAgent()
        self.create_test_project()
        
        context = QAContext(
            project_path=self.project_path,
            quality_level=QualityLevel.STANDARD
        )
        
        asyncio.run(agent._analyze_project_structure(context))
        
        assert "project_files" in context.test_config
        assert "test_files" in context.test_config
        assert len(context.test_config["project_files"]) > 0
        assert len(context.test_config["test_files"]) > 0
    
    @patch('qa_agent.MCPRAGClient')
    def test_test_framework_detection(self, mock_mcp_client):
        """Test test framework detection"""
        agent = QAAgent()
        self.create_test_project()
        
        framework = agent._detect_test_framework(self.project_path)
        assert framework == "jest"
    
    @patch('qa_agent.MCPRAGClient')
    def test_test_result_creation(self, mock_mcp_client):
        """Test test result creation"""
        test_result = TestResult(
            test_name="Sample Test",
            test_type=TestType.UNIT,
            status=TestStatus.PASSED,
            duration=1.5,
            coverage=85.0
        )
        
        assert test_result.test_name == "Sample Test"
        assert test_result.test_type == TestType.UNIT
        assert test_result.status == TestStatus.PASSED
        assert test_result.duration == 1.5
        assert test_result.coverage == 85.0
    
    @patch('qa_agent.MCPRAGClient')
    def test_test_suite_creation(self, mock_mcp_client):
        """Test test suite creation"""
        test_results = [
            TestResult("Test 1", TestType.UNIT, TestStatus.PASSED, 1.0),
            TestResult("Test 2", TestType.UNIT, TestStatus.FAILED, 2.0),
            TestResult("Test 3", TestType.UNIT, TestStatus.PASSED, 1.5)
        ]
        
        test_suite = TestSuite(
            name="Unit Tests",
            test_type=TestType.UNIT,
            tests=test_results,
            total_tests=3,
            passed_tests=2,
            failed_tests=1,
            total_duration=4.5,
            coverage_percentage=80.0
        )
        
        assert test_suite.name == "Unit Tests"
        assert test_suite.test_type == TestType.UNIT
        assert len(test_suite.tests) == 3
        assert test_suite.passed_tests == 2
        assert test_suite.failed_tests == 1
    
    @patch('qa_agent.MCPRAGClient')
    def test_quality_report_creation(self, mock_mcp_client):
        """Test quality report creation"""
        test_suite = TestSuite(
            name="Unit Tests",
            test_type=TestType.UNIT,
            total_tests=10,
            passed_tests=8,
            failed_tests=2,
            coverage_percentage=85.0
        )
        
        report = QualityReport(
            project_name="Test Project",
            test_suites=[test_suite],
            overall_coverage=85.0,
            quality_score=75.0,
            requirements_compliance=0.8,
            security_score=0.9,
            performance_score=0.7
        )
        
        assert report.project_name == "Test Project"
        assert len(report.test_suites) == 1
        assert report.overall_coverage == 85.0
        assert report.quality_score == 75.0
    
    @patch('qa_agent.MCPRAGClient')
    def test_coverage_calculation(self, mock_mcp_client):
        """Test coverage calculation"""
        agent = QAAgent()
        
        test_suites = [
            TestSuite("Unit Tests", TestType.UNIT, coverage_percentage=80.0),
            TestSuite("Integration Tests", TestType.INTEGRATION, coverage_percentage=70.0),
            TestSuite("E2E Tests", TestType.E2E, coverage_percentage=90.0)
        ]
        
        overall_coverage = agent._calculate_overall_coverage(test_suites)
        assert overall_coverage == 80.0  # Average of 80, 70, 90
    
    @patch('qa_agent.MCPRAGClient')
    def test_jest_output_parsing(self, mock_mcp_client):
        """Test Jest output parsing"""
        agent = QAAgent()
        
        jest_output = """
        PASS src/components/App.test.tsx (2.5s)
        FAIL src/utils/helper.test.ts (1.2s)
        PASS src/services/api.test.js (3.1s)
        """
        
        test_results = agent._parse_jest_output(jest_output, TestType.UNIT)
        
        assert len(test_results) == 3
        assert test_results[0].status == TestStatus.PASSED
        assert test_results[1].status == TestStatus.FAILED
        assert test_results[2].status == TestStatus.PASSED
    
    @patch('qa_agent.MCPRAGClient')
    def test_security_keyword_detection(self, mock_mcp_client):
        """Test security keyword detection"""
        agent = QAAgent()
        self.create_test_project()
        
        # Add security-related content
        with open(self.project_path / "src" / "security.js", 'w') as f:
            f.write("const helmet = require('helmet');\nconst bcrypt = require('bcrypt');")
        
        context = QAContext(project_path=self.project_path)
        asyncio.run(agent._analyze_project_structure(context))
        
        has_security = asyncio.run(agent._has_security_measures(context))
        assert has_security is True
    
    @patch('qa_agent.MCPRAGClient')
    def test_performance_keyword_detection(self, mock_mcp_client):
        """Test performance keyword detection"""
        agent = QAAgent()
        self.create_test_project()
        
        # Add performance-related content
        with open(self.project_path / "src" / "cache.js", 'w') as f:
            f.write("const cache = require('redis');\nconst compression = require('compression');")
        
        context = QAContext(project_path=self.project_path)
        asyncio.run(agent._analyze_project_structure(context))
        
        has_performance = asyncio.run(agent._has_performance_optimizations(context))
        assert has_performance is True


class TestQAAgentExecutor:
    """Test the QA Agent Executor"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_path = Path(self.temp_dir) / "test_project"
        self.project_path.mkdir(parents=True, exist_ok=True)
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('qa_agent.MCPRAGClient')
    def test_executor_initialization(self, mock_mcp_client):
        """Test QA Agent Executor initialization"""
        executor = QAAgentExecutor()
        
        assert executor is not None
        assert hasattr(executor, 'qa_agent')
        assert isinstance(executor.qa_agent, QAAgent)
    
    @patch('qa_agent.MCPRAGClient')
    def test_specification_data_conversion(self, mock_mcp_client):
        """Test specification data conversion"""
        executor = QAAgentExecutor()
        
        spec_data = {
            "project_name": "Test Project",
            "description": "Test project description",
            "functional_requirements": ["Auth", "Validation"],
            "user_stories": [{"id": "US001", "title": "Login"}]
        }
        
        specification = executor._convert_specification_data(spec_data)
        
        assert specification.project_name == "Test Project"
        assert specification.description == "Test project description"
        assert len(specification.requirements.get("functional", [])) == 2
        assert len(specification.user_stories) == 1


class TestQualityEnums:
    """Test quality-related enums and data structures"""
    
    def test_quality_level_enum(self):
        """Test QualityLevel enum values"""
        assert QualityLevel.BASIC.value == "basic"
        assert QualityLevel.STANDARD.value == "standard"
        assert QualityLevel.COMPREHENSIVE.value == "comprehensive"
        assert QualityLevel.ENTERPRISE.value == "enterprise"
    
    def test_test_type_enum(self):
        """Test TestType enum values"""
        assert TestType.UNIT.value == "unit"
        assert TestType.INTEGRATION.value == "integration"
        assert TestType.E2E.value == "e2e"
        assert TestType.API.value == "api"
        assert TestType.PERFORMANCE.value == "performance"
        assert TestType.SECURITY.value == "security"
    
    def test_test_status_enum(self):
        """Test TestStatus enum values"""
        assert TestStatus.PENDING.value == "pending"
        assert TestStatus.RUNNING.value == "running"
        assert TestStatus.PASSED.value == "passed"
        assert TestStatus.FAILED.value == "failed"
        assert TestStatus.SKIPPED.value == "skipped"
        assert TestStatus.ERROR.value == "error"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
