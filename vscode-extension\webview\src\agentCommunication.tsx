import React from 'react';
import <PERSON>actDOM from 'react-dom/client';
import AgentCommunicationPanel from './components/AgentCommunicationPanel';
import './index.css';

// Create root element
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = ReactDOM.createRoot(container);

// Render the agent communication panel
root.render(
  <React.StrictMode>
    <AgentCommunicationPanel />
  </React.StrictMode>
);

// Handle hot module replacement in development
if (module.hot) {
  module.hot.accept('./components/AgentCommunicationPanel', () => {
    const NextAgentCommunicationPanel = require('./components/AgentCommunicationPanel').default;
    root.render(
      <React.StrictMode>
        <NextAgentCommunicationPanel />
      </React.StrictMode>
    );
  });
}
