["test_api_manager.py::TestAPIConfig::test_api_config_creation", "test_api_manager.py::TestAPIKeyValidator::test_validate_anthropic_key_success", "test_api_manager.py::TestAPIKeyValidator::test_validate_openai_key_success", "test_api_manager.py::TestAPIManager::test_api_manager_initialization", "test_api_manager.py::TestAPIManager::test_api_manager_no_external_providers", "test_api_manager.py::TestAPIManager::test_api_manager_no_providers", "test_api_manager.py::TestAPIManager::test_api_manager_with_config_file", "test_api_manager.py::TestAPIManager::test_generate_text_all_providers_fail", "test_api_manager.py::TestAPIManager::test_generate_text_fallback", "test_api_manager.py::TestAPIManager::test_generate_text_success", "test_api_manager.py::TestAPIManager::test_get_provider_status", "test_api_manager.py::TestAPIManager::test_manager_cleanup", "test_api_manager.py::TestAPIManager::test_rate_limiting", "test_api_manager.py::TestEnhancedAPIManager::test_get_api_key_from_env", "test_api_manager.py::TestEnhancedAPIManager::test_list_configured_providers", "test_api_manager.py::TestEnhancedAPIManager::test_set_api_key_success", "test_api_manager.py::TestGlobalAPIManager::test_get_api_manager_singleton", "test_api_manager.py::TestGlobalAPIManager::test_global_generate_text", "test_api_manager.py::TestRateLimiter::test_can_make_request_when_under_limit", "test_api_manager.py::TestRateLimiter::test_rate_limiter_initialization", "test_api_manager.py::TestRateLimiter::test_time_window_cleanup", "test_api_manager.py::TestSecureKeyStorage::test_delete_key", "test_api_manager.py::TestSecureKeyStorage::test_list_providers", "test_api_manager.py::TestSecureKeyStorage::test_load_nonexistent_key", "test_api_manager.py::TestSecureKeyStorage::test_store_and_load_key", "test_api_resilience.py::TestAPIResilienceLayer::test_cache_functionality", "test_api_resilience.py::TestAPIResilienceLayer::test_degraded_service_fallback", "test_api_resilience.py::TestAPIResilienceLayer::test_get_resilience_stats", "test_api_resilience.py::TestAPIResilienceLayer::test_provider_testing", "test_api_resilience.py::TestAPIResilienceLayer::test_quota_exceeded_handling", "test_api_resilience.py::TestAPIResilienceLayer::test_rate_limit_handling", "test_api_resilience.py::TestAPIResilienceLayer::test_retry_on_failure", "test_api_resilience.py::TestAPIResilienceLayer::test_successful_api_call", "test_api_resilience.py::TestFallbackConfig::test_default_fallback_config", "test_api_resilience.py::TestFallbackConfig::test_fallback_models", "test_api_resilience.py::TestNotificationManager::test_add_notification", "test_api_resilience.py::TestNotificationManager::test_get_notifications_by_type", "test_api_resilience.py::TestNotificationManager::test_notification_limit", "test_api_resilience.py::TestQuotaManager::test_quota_manager_initialization", "test_api_resilience.py::TestQuotaManager::test_quota_warnings", "test_api_resilience.py::TestQuotaManager::test_record_usage", "test_api_resilience.py::TestRetryConfig::test_custom_retry_config", "test_api_resilience.py::TestRetryConfig::test_default_retry_config", "test_archon_integration.py::TestAgentTypes::test_agent_lifecycle_stage_enum", "test_archon_integration.py::TestAgentTypes::test_agent_type_enum", "test_archon_integration.py::TestAgentTypes::test_optimization_strategy_enum", "test_archon_integration.py::TestArchonConfig::test_agent_type_templates", "test_archon_integration.py::TestArchonConfig::test_archon_config_initialization", "test_archon_integration.py::TestArchonConfig::test_archon_mode_config", "test_archon_integration.py::TestArchonConfig::test_lifecycle_stage_configs", "test_archon_integration.py::TestArchonConfig::test_optimization_strategies", "test_archon_integration.py::TestArchonIntegration::test_agent_evolution_plan_creation", "test_archon_integration.py::TestArchonIntegration::test_agent_metrics_creation", "test_archon_integration.py::TestArchonIntegration::test_agent_specification_creation", "test_archon_integration.py::TestArchonIntegration::test_agent_team_composition_creation", "test_archon_integration.py::TestArchonIntegration::test_archon_integration_initialization", "test_archon_integration.py::TestArchonIntegration::test_assess_lifecycle_health", "test_archon_integration.py::TestArchonIntegration::test_determine_lifecycle_stage", "test_archon_integration.py::TestArchonIntegration::test_fallback_team_generation", "test_archon_integration.py::TestArchonIntegration::test_parse_agent_metrics", "test_archon_integration.py::TestArchonIntegration::test_parse_evolution_plan", "test_archon_integration.py::TestArchonIntegration::test_parse_team_composition", "test_archon_integration.py::TestArchonIntegrationExecutor::test_execute_agent_generation_data_structure", "test_archon_integration.py::TestArchonIntegrationExecutor::test_executor_initialization", "test_developer_agent.py::TestCodeGeneration::test_code_file_creation", "test_developer_agent.py::TestCodeGeneration::test_test_suite_creation", "test_developer_agent.py::TestDeveloperAgent::test_code_generation_context_creation", "test_developer_agent.py::TestDeveloperAgent::test_developer_agent_initialization", "test_developer_agent.py::TestDeveloperAgent::test_error_handling", "test_developer_agent.py::TestDeveloperAgent::test_input_validation_missing_tech_stack", "test_developer_agent.py::TestDeveloperAgent::test_input_validation_success", "test_developer_agent.py::TestDeveloperAgent::test_project_structure_generation", "test_developer_agent.py::TestDeveloperAgent::test_technology_stack_processing", "test_developer_agent.py::TestQualityGates::test_code_quality_enum", "test_developer_agent.py::TestQualityGates::test_project_type_enum", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_file_content_quality", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_express_controllers", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_express_middleware", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_express_models", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_express_routes", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_express_services", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_react_hooks", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_generate_react_services", "test_developer_agent_implementations.py::TestDeveloperAgentImplementations::test_javascript_generation", "test_integration.py::TestErrorScenarios::test_concurrent_project_creation", "test_integration.py::TestErrorScenarios::test_invalid_pheromone_requests", "test_integration.py::TestErrorScenarios::test_invalid_project_requests", "test_integration.py::TestPerformance::test_api_response_times", "test_integration.py::TestPerformance::test_pheromone_throughput", "test_integration.py::TestSystemIntegration::test_component_status_monitoring", "test_integration.py::TestSystemIntegration::test_full_project_creation_workflow", "test_integration.py::TestSystemIntegration::test_pheromone_system_integration", "test_orchestrator.py::TestAgentExecution::test_analyst_agent_execution", "test_orchestrator.py::TestAgentExecution::test_architect_agent_execution", "test_orchestrator.py::TestAgentExecution::test_developer_agent_execution", "test_orchestrator.py::TestAgentExecution::test_qa_agent_execution", "test_orchestrator.py::TestErrorHandling::test_invalid_project_data", "test_orchestrator.py::TestErrorHandling::test_missing_pheromone_file", "test_orchestrator.py::TestErrorHandling::test_pheromone_file_corruption", "test_orchestrator.py::TestOrchestratorAPI::test_components_status", "test_orchestrator.py::TestOrchestratorAPI::test_drop_pheromone_endpoint", "test_orchestrator.py::TestOrchestratorAPI::test_health_endpoint", "test_orchestrator.py::TestOrchestratorAPI::test_pheromones_statistics", "test_orchestrator.py::TestOrchestratorAPI::test_projects_list_empty", "test_orchestrator.py::TestPheromoneSystem::test_drop_pheromone", "test_orchestrator.py::TestPheromoneSystem::test_get_pheromones_empty", "test_orchestrator.py::TestPheromoneSystem::test_get_pheromones_with_data", "test_orchestrator.py::TestPheromoneSystem::test_pheromone_filtering_by_project", "test_orchestrator.py::TestPheromoneSystem::test_pheromone_filtering_by_signal", "test_orchestrator.py::TestProjectCreation::test_create_project_structure", "test_orchestrator.py::TestProjectCreation::test_project_creation_api_call_failure", "test_orchestrator.py::TestProjectCreation::test_project_creation_api_call_success", "test_production_integration.py::TestProductionIntegration::test_backup_and_recovery_scripts", "test_production_integration.py::TestProductionIntegration::test_component_service_connectivity", "test_production_integration.py::TestProductionIntegration::test_container_resource_limits", "test_production_integration.py::TestProductionIntegration::test_database_connectivity", "test_production_integration.py::TestProductionIntegration::test_docker_compose_validation", "test_production_integration.py::TestProductionIntegration::test_dockerfile_validation", "test_production_integration.py::TestProductionIntegration::test_environment_variables_validation", "test_production_integration.py::TestProductionIntegration::test_load_balancer_configuration", "test_production_integration.py::TestProductionIntegration::test_log_aggregation_setup", "test_production_integration.py::TestProductionIntegration::test_monitoring_stack_health", "test_production_integration.py::TestProductionIntegration::test_production_performance_limits", "test_production_integration.py::TestProductionIntegration::test_redis_connectivity", "test_production_integration.py::TestProductionIntegration::test_security_configuration", "test_production_integration.py::TestProductionIntegration::test_service_health_endpoints", "test_production_integration.py::TestProductionIntegration::test_ssl_certificate_validation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_api_manager_fallback", "test_project_generator_comprehensive.py::TestProjectGenerator::test_backend_code_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_config_files_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_different_project_types", "test_project_generator_comprehensive.py::TestProjectGenerator::test_documentation_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_error_handling", "test_project_generator_comprehensive.py::TestProjectGenerator::test_frontend_code_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_generate_project_basic", "test_project_generator_comprehensive.py::TestProjectGenerator::test_generate_project_with_api_manager", "test_project_generator_comprehensive.py::TestProjectGenerator::test_package_config_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_generator_initialization", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_metadata_creation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_structure_creation", "test_project_generator_comprehensive.py::TestProjectGeneratorIntegration::test_full_project_generation_workflow", "test_qa_agent.py::TestQAAgent::test_coverage_calculation", "test_qa_agent.py::TestQAAgent::test_input_validation_missing_project", "test_qa_agent.py::TestQAAgent::test_input_validation_success", "test_qa_agent.py::TestQAAgent::test_jest_output_parsing", "test_qa_agent.py::TestQAAgent::test_performance_keyword_detection", "test_qa_agent.py::TestQAAgent::test_project_structure_analysis", "test_qa_agent.py::TestQAAgent::test_qa_agent_initialization", "test_qa_agent.py::TestQAAgent::test_qa_context_creation", "test_qa_agent.py::TestQAAgent::test_quality_report_creation", "test_qa_agent.py::TestQAAgent::test_security_keyword_detection", "test_qa_agent.py::TestQAAgent::test_test_framework_detection", "test_qa_agent.py::TestQAAgent::test_test_result_creation", "test_qa_agent.py::TestQAAgent::test_test_suite_creation", "test_qa_agent.py::TestQAAgentExecutor::test_executor_initialization", "test_qa_agent.py::TestQAAgentExecutor::test_specification_data_conversion", "test_qa_agent.py::TestQualityEnums::test_quality_level_enum", "test_qa_agent.py::TestQualityEnums::test_test_status_enum", "test_qa_agent.py::TestQualityEnums::test_test_type_enum", "test_research_engine.py::TestResearchConfig::test_research_config_initialization", "test_research_engine.py::TestResearchConfig::test_research_mode_config", "test_research_engine.py::TestResearchConfig::test_source_priorities", "test_research_engine.py::TestResearchConfig::test_technology_keywords", "test_research_engine.py::TestResearchEngine::test_crawl_request_creation", "test_research_engine.py::TestResearchEngine::test_deduplicate_results", "test_research_engine.py::TestResearchEngine::test_expand_query", "test_research_engine.py::TestResearchEngine::test_extract_domain", "test_research_engine.py::TestResearchEngine::test_generate_cache_key", "test_research_engine.py::TestResearchEngine::test_get_priority_value", "test_research_engine.py::TestResearchEngine::test_query_pattern_initialization", "test_research_engine.py::TestResearchEngine::test_rank_results", "test_research_engine.py::TestResearchEngine::test_research_context_creation", "test_research_engine.py::TestResearchEngine::test_research_engine_initialization", "test_research_engine.py::TestResearchEngine::test_research_query_creation", "test_research_engine.py::TestResearchEngine::test_research_result_creation", "test_research_engine.py::TestResearchEngine::test_research_template_initialization", "test_research_engine.py::TestResearchEngine::test_source_priority_initialization", "test_research_engine.py::TestResearchEngineExecutor::test_execute_research_data_conversion", "test_research_engine.py::TestResearchEngineExecutor::test_executor_initialization", "test_research_engine.py::TestResearchTypes::test_research_priority_enum", "test_research_engine.py::TestResearchTypes::test_research_type_enum", "test_vscode_extension.py::TestAgentInteractionPanel::test_agent_panel_creation", "test_vscode_extension.py::TestAgentInteractionPanel::test_agent_status_updates", "test_vscode_extension.py::TestAgentInteractionPanel::test_check_system_status", "test_vscode_extension.py::TestAgentInteractionPanel::test_create_project_api_failure", "test_vscode_extension.py::TestAgentInteractionPanel::test_create_project_success", "test_vscode_extension.py::TestAgentInteractionPanel::test_refresh_projects", "test_vscode_extension.py::TestAgentInteractionPanel::test_save_settings", "test_vscode_extension.py::TestAgentInteractionPanel::test_test_connection_failure", "test_vscode_extension.py::TestAgentInteractionPanel::test_test_connection_success", "test_vscode_extension.py::TestAgentInteractionPanel::test_webview_content_generation", "test_vscode_extension.py::TestProjectCreationCommands::test_create_from_template_command", "test_vscode_extension.py::TestProjectCreationCommands::test_create_project_command", "test_vscode_extension.py::TestProjectCreationCommands::test_quick_create_command", "test_vscode_extension.py::TestVSCodeExtensionCore::test_configuration_loading", "test_vscode_extension.py::TestVSCodeExtensionCore::test_extension_activation", "test_vscode_extension.py::TestVSCodeExtensionCore::test_status_bar_initialization", "test_vscode_extension.py::TestVSCodeExtensionHelpers::test_open_project", "test_vscode_extension.py::TestVSCodeExtensionHelpers::test_open_projects_folder", "test_vscode_extension.py::TestVSCodeExtensionHelpers::test_view_project", "test_vscode_extension.py::TestVSCodeExtensionMessageHandling::test_message_routing"]