#!/usr/bin/env python3
"""
Comprehensive verification script for BMAD workflow engine features.
This script verifies that Step 5 has been completed correctly.
"""

import sys
import os
import asyncio
import traceback

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all required classes can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from workflow_engine import (
            # Core classes
            WorkflowExecutionEngine,
            WorkflowTemplateManager,
            WorkflowMonitor,
            
            # Condition classes
            StepCondition,
            ConditionOperator,
            
            # Step classes
            WorkflowStep,
            StepType,
            StepStatus,
            
            # Workflow classes
            WorkflowDefinition,
            WorkflowExecution,
            WorkflowStatus,
            
            # Agent classes
            AgentRequirement,
            AgentSelectionStrategy,
            
            # Monitoring classes
            ExecutionMonitor,
            
            # Other classes
            WorkflowVariable,
            RetryConfig,
            TimeoutConfig
        )
        print("✅ All core classes imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_conditional_logic():
    """Test conditional step logic"""
    print("\n🔍 Testing conditional logic...")
    
    try:
        from workflow_engine import StepCondition, ConditionOperator, WorkflowVariable, WorkflowExecution
        
        # Test simple condition
        condition = StepCondition(
            variable="test_var",
            operator=ConditionOperator.EQUALS,
            value="test_value"
        )
        print("✅ Simple condition created")
        
        # Test complex condition with AND
        sub_condition1 = StepCondition(
            variable="var1",
            operator=ConditionOperator.EQUALS,
            value="value1"
        )
        
        sub_condition2 = StepCondition(
            variable="var2",
            operator=ConditionOperator.NOT_EQUALS,
            value="value2"
        )
        
        complex_condition = StepCondition(
            variable="",
            operator=ConditionOperator.AND,
            value=None,
            sub_conditions=[sub_condition1, sub_condition2]
        )
        print("✅ Complex AND condition created")
        
        # Test OR condition
        or_condition = StepCondition(
            variable="",
            operator=ConditionOperator.OR,
            value=None,
            sub_conditions=[sub_condition1, sub_condition2]
        )
        print("✅ Complex OR condition created")
        
        # Test all operators exist
        operators = [
            ConditionOperator.EQUALS,
            ConditionOperator.NOT_EQUALS,
            ConditionOperator.GREATER_THAN,
            ConditionOperator.LESS_THAN,
            ConditionOperator.CONTAINS,
            ConditionOperator.NOT_CONTAINS,
            ConditionOperator.REGEX_MATCH,
            ConditionOperator.EXISTS,
            ConditionOperator.NOT_EXISTS,
            ConditionOperator.IN_LIST,
            ConditionOperator.NOT_IN_LIST,
            ConditionOperator.STARTS_WITH,
            ConditionOperator.ENDS_WITH,
            ConditionOperator.IS_EMPTY,
            ConditionOperator.IS_NOT_EMPTY,
            ConditionOperator.AND,
            ConditionOperator.OR,
            ConditionOperator.NOT
        ]
        print(f"✅ All {len(operators)} condition operators available")
        
        return True
    except Exception as e:
        print(f"❌ Conditional logic test failed: {e}")
        traceback.print_exc()
        return False

def test_optional_tasks():
    """Test optional task support"""
    print("\n🔍 Testing optional tasks...")
    
    try:
        from workflow_engine import WorkflowStep, StepType
        
        # Test optional step creation
        optional_step = WorkflowStep(
            id="optional_step",
            name="Optional Step",
            type=StepType.TASK,
            description="This step is optional",
            optional=True
        )
        
        assert optional_step.optional == True
        print("✅ Optional step created successfully")
        
        # Test non-optional step
        required_step = WorkflowStep(
            id="required_step",
            name="Required Step",
            type=StepType.TASK,
            description="This step is required",
            optional=False
        )
        
        assert required_step.optional == False
        print("✅ Required step created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Optional tasks test failed: {e}")
        return False

def test_dynamic_agent_assignment():
    """Test dynamic agent assignment"""
    print("\n🔍 Testing dynamic agent assignment...")
    
    try:
        from workflow_engine import AgentRequirement, AgentSelectionStrategy, WorkflowStep, StepType
        
        # Test agent requirements
        agent_req = AgentRequirement(
            capabilities=["python", "backend", "api"],
            min_performance=0.8,
            max_load=0.7,
            preferred_agents=["agent1", "agent2"],
            excluded_agents=["agent3"],
            selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
        )
        print("✅ Agent requirements created")
        
        # Test step with agent requirements
        step_with_agent = WorkflowStep(
            id="agent_step",
            name="Step with Agent Requirements",
            type=StepType.TASK,
            description="Step requiring specific agent capabilities",
            agent_requirements=agent_req
        )
        print("✅ Step with agent requirements created")
        
        # Test all selection strategies
        strategies = [
            AgentSelectionStrategy.ROUND_ROBIN,
            AgentSelectionStrategy.LEAST_LOADED,
            AgentSelectionStrategy.CAPABILITY_MATCH,
            AgentSelectionStrategy.RANDOM,
            AgentSelectionStrategy.PRIORITY_BASED,
            AgentSelectionStrategy.CUSTOM
        ]
        print(f"✅ All {len(strategies)} agent selection strategies available")
        
        return True
    except Exception as e:
        print(f"❌ Dynamic agent assignment test failed: {e}")
        return False

def test_parallel_execution():
    """Test parallel execution support"""
    print("\n🔍 Testing parallel execution...")
    
    try:
        from workflow_engine import WorkflowStep, StepType, WorkflowDefinition
        
        # Test parallel steps
        step1 = WorkflowStep(
            id="parallel_step1",
            name="Parallel Step 1",
            type=StepType.TASK,
            description="First parallel step"
        )
        
        step2 = WorkflowStep(
            id="parallel_step2",
            name="Parallel Step 2",
            type=StepType.TASK,
            description="Second parallel step",
            parallel_steps=["parallel_step1"]
        )
        
        step3 = WorkflowStep(
            id="parallel_step3",
            name="Parallel Step 3",
            type=StepType.TASK,
            description="Third parallel step",
            parallel_steps=["parallel_step1", "parallel_step2"]
        )
        
        print("✅ Parallel steps created")
        
        # Test workflow with parallel execution
        workflow = WorkflowDefinition(
            id="parallel_workflow",
            name="Parallel Test Workflow",
            version="1.0.0",
            description="Workflow with parallel execution",
            steps={
                "parallel_step1": step1,
                "parallel_step2": step2,
                "parallel_step3": step3
            },
            step_order=["parallel_step1", "parallel_step2", "parallel_step3"],
            parallel_execution=True,
            max_concurrent_steps=3
        )
        
        print("✅ Parallel workflow definition created")
        
        return True
    except Exception as e:
        print(f"❌ Parallel execution test failed: {e}")
        return False

def test_workflow_visualization():
    """Test workflow visualization"""
    print("\n🔍 Testing workflow visualization...")
    
    try:
        from workflow_engine import WorkflowExecutionEngine, WorkflowDefinition, WorkflowStep, StepType
        
        engine = WorkflowExecutionEngine()
        
        # Create simple workflow for visualization
        step1 = WorkflowStep(
            id="step1",
            name="First Step",
            type=StepType.TASK,
            description="First step"
        )
        
        step2 = WorkflowStep(
            id="step2",
            name="Second Step",
            type=StepType.TASK,
            description="Second step",
            depends_on=["step1"]
        )
        
        workflow = WorkflowDefinition(
            id="viz_workflow",
            name="Visualization Test Workflow",
            version="1.0.0",
            steps={"step1": step1, "step2": step2},
            step_order=["step1", "step2"]
        )
        
        # Generate Mermaid diagram
        diagram = engine.generate_workflow_diagram(workflow)
        
        # Verify diagram content
        assert "graph TD" in diagram
        assert "step1" in diagram
        assert "step2" in diagram
        assert "step1 --> step2" in diagram
        assert "classDef" in diagram
        
        print("✅ Mermaid diagram generated successfully")
        print(f"   Diagram has {len(diagram.split(chr(10)))} lines")
        
        return True
    except Exception as e:
        print(f"❌ Workflow visualization test failed: {e}")
        return False

def test_workflow_templates():
    """Test workflow template system"""
    print("\n🔍 Testing workflow templates...")

    try:
        from workflow_engine import WorkflowTemplateManager, WorkflowExecutionEngine

        # Test template manager
        manager = WorkflowTemplateManager()

        # Test built-in templates
        templates = manager.list_templates()
        assert len(templates) > 0
        print(f"✅ Found {len(templates)} built-in templates")

        # Test template categories
        categories = manager.template_categories
        assert len(categories) > 0
        print(f"✅ Found {len(categories)} template categories")

        # Test template search
        fullstack_templates = manager.search_templates("fullstack")
        assert len(fullstack_templates) > 0
        print(f"✅ Found {len(fullstack_templates)} fullstack templates")

        # Test specific templates exist
        required_templates = [
            'greenfield-fullstack',
            'greenfield-service',
            'greenfield-ui',
            'brownfield-fullstack',
            'brownfield-service',
            'brownfield-ui'
        ]

        for template_id in required_templates:
            template = manager.get_template(template_id)
            assert template is not None, f"Template {template_id} not found"

        print(f"✅ All {len(required_templates)} required templates exist")

        # Test engine template integration
        engine = WorkflowExecutionEngine()
        engine_templates = engine.get_available_templates()
        assert len(engine_templates) == len(templates)
        print("✅ Engine template integration working")

        # Test template recommendation
        recommendations = engine.recommend_template("create a fullstack web application", "fullstack")
        assert len(recommendations) > 0
        print(f"✅ Template recommendation returned {len(recommendations)} suggestions")

        return True
    except Exception as e:
        print(f"❌ Workflow templates test failed: {e}")
        traceback.print_exc()
        return False

def test_workflow_monitoring():
    """Test workflow monitoring system"""
    print("\n🔍 Testing workflow monitoring...")

    try:
        from workflow_engine import WorkflowMonitor, ExecutionMonitor, WorkflowExecution, WorkflowDefinition

        # Test workflow monitor
        monitor = WorkflowMonitor()
        assert monitor.monitoring_enabled == True
        assert len(monitor.active_monitors) == 0
        print("✅ WorkflowMonitor initialized")

        # Test execution monitor
        exec_monitor = ExecutionMonitor(
            execution_id="test_exec",
            workflow_id="test_workflow",
            workflow_name="Test Workflow",
            total_steps=5
        )

        assert exec_monitor.execution_id == "test_exec"
        assert exec_monitor.total_steps == 5
        assert exec_monitor.is_active == True
        print("✅ ExecutionMonitor created")

        # Test status retrieval
        status = exec_monitor.get_current_status()
        assert "execution_id" in status
        assert "status" in status
        assert "timing" in status
        assert "performance" in status
        print("✅ Monitor status retrieval working")

        # Test progress calculation
        from workflow_engine import WorkflowExecutionEngine
        engine = WorkflowExecutionEngine()

        # Create mock execution
        execution = WorkflowExecution(
            id="test_exec",
            workflow_id="test_workflow",
            status="running",
            started_at=1234567890,
            project_id="test_project"
        )

        progress = engine.get_workflow_progress(execution)
        print(f"   Progress keys: {list(progress.keys())}")
        assert "overall_progress" in progress
        assert "step_details" in progress
        if "estimated_completion" not in progress:
            print(f"   Missing estimated_completion, progress: {progress}")
        assert "estimated_completion" in progress
        print("✅ Progress calculation working")

        return True
    except Exception as e:
        print(f"❌ Workflow monitoring test failed: {e}")
        traceback.print_exc()
        return False

def test_real_time_status_updates():
    """Test real-time status updates"""
    print("\n🔍 Testing real-time status updates...")

    try:
        from workflow_engine import WorkflowExecutionEngine, WorkflowMonitor

        engine = WorkflowExecutionEngine()

        # Test monitoring integration
        assert hasattr(engine, 'monitor')
        assert isinstance(engine.monitor, WorkflowMonitor)
        print("✅ Engine has monitoring integration")

        # Test subscription system
        callback_called = False

        def test_callback(update_data):
            nonlocal callback_called
            callback_called = True

        engine.subscribe_to_monitoring_updates(test_callback)
        print("✅ Monitoring subscription system working")

        # Test execution statistics
        stats = engine.get_execution_statistics()
        assert "total_executions" in stats
        assert "active_executions" in stats
        assert "active_monitors" in stats
        print("✅ Execution statistics available")

        return True
    except Exception as e:
        print(f"❌ Real-time status updates test failed: {e}")
        return False

def test_time_estimation():
    """Test time estimation for completion"""
    print("\n🔍 Testing time estimation...")

    try:
        from workflow_engine import WorkflowExecutionEngine, WorkflowExecution, StepStatus
        import time

        engine = WorkflowExecutionEngine()

        # Create mock execution with some completed steps
        execution = WorkflowExecution(
            id="test_exec",
            workflow_id="test_workflow",
            status="running",
            started_at=time.time() - 300,  # Started 5 minutes ago
            project_id="test_project",
            step_status={
                "step1": StepStatus.COMPLETED,
                "step2": StepStatus.COMPLETED,
                "step3": StepStatus.RUNNING,
                "step4": StepStatus.PENDING,
                "step5": StepStatus.PENDING
            }
        )

        # Test time estimation
        estimated_time = engine._estimate_completion_time(execution)
        assert estimated_time is not None
        assert estimated_time >= 0
        print(f"✅ Time estimation working: {estimated_time:.1f} seconds remaining")

        return True
    except Exception as e:
        print(f"❌ Time estimation test failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🚀 Starting BMAD Workflow Engine Feature Verification")
    print("=" * 60)

    tests = [
        ("Core Imports", test_imports),
        ("Conditional Logic", test_conditional_logic),
        ("Optional Tasks", test_optional_tasks),
        ("Dynamic Agent Assignment", test_dynamic_agent_assignment),
        ("Parallel Execution", test_parallel_execution),
        ("Workflow Visualization", test_workflow_visualization),
        ("Workflow Templates", test_workflow_templates),
        ("Workflow Monitoring", test_workflow_monitoring),
        ("Real-time Status Updates", test_real_time_status_updates),
        ("Time Estimation", test_time_estimation)
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1

    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 ALL BMAD WORKFLOW FEATURES VERIFIED SUCCESSFULLY!")
        print("✅ Step 5 is 1000% COMPLETE!")
        return True
    else:
        print(f"❌ {failed} tests failed. Step 5 needs attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
