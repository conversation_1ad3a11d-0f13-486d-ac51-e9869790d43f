#!/bin/bash

# TaoForge Main Deployment Script
# Unified deployment script for development, staging, and production environments

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-development}"
FORCE_REBUILD="${FORCE_REBUILD:-false}"
SKIP_HEALTH_CHECK="${SKIP_HEALTH_CHECK:-false}"

# Logging functions
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}" >&2; }
log_warning() { echo -e "${YELLOW}⚠️ $1${NC}"; }
log_info() { echo -e "${BLUE}ℹ️ $1${NC}"; }

log_info "Starting TaoForge deployment..."
log_info "Environment: $ENVIRONMENT"
log_info "Project Root: $PROJECT_ROOT"

# Validate environment
validate_environment() {
    log_info "Validating deployment environment..."
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        return 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available"
        return 1
    fi
    
    # Check required files
    local required_files=(
        "docker-compose.yml"
        ".env.example"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$PROJECT_ROOT/$file" ]]; then
            log_error "Required file missing: $file"
            return 1
        fi
    done
    
    log_success "Environment validation passed"
    return 0
}

# Setup environment configuration
setup_environment() {
    log_info "Setting up environment configuration..."
    
    cd "$PROJECT_ROOT"
    
    # Create .env file if it doesn't exist
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log_info "Creating .env file from .env.example..."
            cp .env.example .env
            log_warning "Please edit .env file with your configuration"
        else
            log_error ".env.example file not found"
            return 1
        fi
    fi
    
    # Create necessary directories
    local directories=(
        "projects"
        "logs"
        "backups"
        "monitoring/grafana/dashboards"
        "nginx/ssl"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    log_success "Environment setup completed"
    return 0
}

# Get Docker Compose command
get_docker_compose_cmd() {
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
    elif docker compose version &> /dev/null; then
        echo "docker compose"
    else
        log_error "Docker Compose not found"
        return 1
    fi
}

# Deploy services
deploy_services() {
    log_info "Deploying TaoForge services..."
    
    cd "$PROJECT_ROOT"
    
    local compose_cmd
    compose_cmd=$(get_docker_compose_cmd)
    
    # Select compose file based on environment
    local compose_file="docker-compose.yml"
    case "$ENVIRONMENT" in
        "production")
            compose_file="docker-compose.prod.yml"
            ;;
        "test")
            compose_file="docker-compose.test.yml"
            ;;
        "development"|*)
            compose_file="docker-compose.yml"
            ;;
    esac
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose file not found: $compose_file"
        return 1
    fi
    
    log_info "Using compose file: $compose_file"
    
    # Stop existing services
    log_info "Stopping existing services..."
    $compose_cmd -f "$compose_file" down --remove-orphans || true
    
    # Build and start services
    local build_flag=""
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        build_flag="--build"
        log_info "Force rebuilding images..."
    fi
    
    log_info "Starting services..."
    if $compose_cmd -f "$compose_file" up -d $build_flag; then
        log_success "Services started successfully"
        return 0
    else
        log_error "Failed to start services"
        return 1
    fi
}

# Health check
health_check() {
    if [[ "$SKIP_HEALTH_CHECK" == "true" ]]; then
        log_info "Skipping health check (SKIP_HEALTH_CHECK=true)"
        return 0
    fi
    
    log_info "Performing health checks..."
    
    # Wait for services to start
    log_info "Waiting for services to initialize..."
    sleep 30
    
    # Check if health check script exists
    if [[ -f "$SCRIPT_DIR/health_check.sh" ]]; then
        if bash "$SCRIPT_DIR/health_check.sh"; then
            log_success "Health checks passed"
            return 0
        else
            log_error "Health checks failed"
            return 1
        fi
    else
        log_warning "Health check script not found, skipping detailed checks"
        
        # Basic service check
        local compose_cmd
        compose_cmd=$(get_docker_compose_cmd)
        
        local compose_file="docker-compose.yml"
        case "$ENVIRONMENT" in
            "production") compose_file="docker-compose.prod.yml" ;;
            "test") compose_file="docker-compose.test.yml" ;;
        esac
        
        if $compose_cmd -f "$compose_file" ps | grep -q "Up"; then
            log_success "Services are running"
            return 0
        else
            log_error "No services are running"
            return 1
        fi
    fi
}

# Show access points
show_access_points() {
    log_info "TaoForge Service Access Points:"
    
    local base_url="http://localhost"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        base_url="https://your-domain.com"
    fi
    
    echo "  🌐 Main API: $base_url:8000"
    echo "  📚 API Documentation: $base_url:8000/docs"
    echo "  🏛️ Archon API: $base_url:8100"
    echo "  🎨 Archon UI: $base_url:8501"
    echo "  🧠 Pheromind: $base_url:8502"
    echo "  🔧 BMAD: $base_url:8503"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "  📊 Grafana: $base_url/grafana/"
        echo "  📈 Prometheus: $base_url/prometheus/"
    fi
}

# Main deployment function
main() {
    log_info "🚀 Starting TaoForge deployment process..."
    
    # Validate environment
    if ! validate_environment; then
        log_error "Environment validation failed"
        exit 1
    fi
    
    # Setup environment
    if ! setup_environment; then
        log_error "Environment setup failed"
        exit 1
    fi
    
    # Deploy services
    if ! deploy_services; then
        log_error "Service deployment failed"
        exit 1
    fi
    
    # Health check
    if ! health_check; then
        log_error "Health check failed"
        exit 1
    fi
    
    # Success
    log_success "🎉 TaoForge deployment completed successfully!"
    show_access_points
    
    log_info "Deployment Summary:"
    log_info "  Environment: $ENVIRONMENT"
    log_info "  Status: ✅ SUCCESS"
    log_info "  Next Steps:"
    log_info "    1. Verify services at the URLs above"
    log_info "    2. Check logs: docker-compose logs -f"
    log_info "    3. Monitor health: ./scripts/health_check.sh"
}

# Handle script arguments
case "${1:-}" in
    "help"|"-h"|"--help")
        echo "TaoForge Deployment Script"
        echo ""
        echo "Usage: $0 [environment] [options]"
        echo ""
        echo "Environments:"
        echo "  development  - Development environment (default)"
        echo "  staging      - Staging environment"
        echo "  production   - Production environment"
        echo "  test         - Test environment"
        echo ""
        echo "Environment Variables:"
        echo "  FORCE_REBUILD=true     - Force rebuild Docker images"
        echo "  SKIP_HEALTH_CHECK=true - Skip health checks"
        echo ""
        echo "Examples:"
        echo "  $0                     # Deploy development environment"
        echo "  $0 production          # Deploy production environment"
        echo "  FORCE_REBUILD=true $0  # Force rebuild and deploy"
        echo ""
        exit 0
        ;;
    "development"|"staging"|"production"|"test"|"")
        # Valid environment, continue
        ;;
    *)
        log_error "Invalid environment: $1"
        log_info "Valid environments: development, staging, production, test"
        log_info "Use '$0 help' for more information"
        exit 1
        ;;
esac

# Run main function
main "$@"
