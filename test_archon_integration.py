#!/usr/bin/env python3
"""
Test script for the Archon Integration
Demonstrates dynamic agent generation, optimization, and evaluation capabilities
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from archon_integration import (
    ArchonIntegration,
    ArchonIntegrationExecutor,
    AgentSpecification,
    AgentMetrics,
    AgentEvolutionPlan,
    AgentTeamComposition,
    AgentType,
    OptimizationStrategy,
    AgentLifecycleStage
)

async def test_agent_team_generation():
    """Test dynamic agent team generation"""
    print("🤖 Testing Agent Team Generation")
    print("=" * 60)
    
    # Create test cases
    test_cases = [
        {
            "name": "E-commerce Platform",
            "prompt": "Build a scalable e-commerce platform with payment processing",
            "context": {
                "project_id": "ecommerce_001",
                "project_type": "web_application",
                "technology_stack": ["React", "Node.js", "PostgreSQL"],
                "complexity": "high",
                "team_size": 5
            }
        },
        {
            "name": "Data Analytics Dashboard",
            "prompt": "Create a real-time data analytics dashboard for business intelligence",
            "context": {
                "project_id": "analytics_001",
                "project_type": "data_platform",
                "technology_stack": ["Python", "React", "MongoDB"],
                "complexity": "medium",
                "team_size": 4
            }
        },
        {
            "name": "Mobile Banking App",
            "prompt": "Develop a secure mobile banking application with biometric authentication",
            "context": {
                "project_id": "banking_001",
                "project_type": "mobile_application",
                "technology_stack": ["React Native", "Node.js", "PostgreSQL"],
                "complexity": "high",
                "team_size": 6
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {test_case['name'].upper()}")
        print(f"{'='*60}")
        print(f"Project Type: {test_case['context']['project_type']}")
        print(f"Technology Stack: {', '.join(test_case['context']['technology_stack'])}")
        print(f"Complexity: {test_case['context']['complexity']}")
        print(f"Team Size: {test_case['context']['team_size']}")
        
        try:
            async with ArchonIntegration() as archon:
                # Generate agent team
                team_composition = await archon.generate_agent_team(
                    test_case["prompt"],
                    test_case["context"],
                    test_case["context"]["team_size"]
                )
                
                print(f"\n✅ Team Generation Complete!")
                print(f"   Team ID: {team_composition.team_id}")
                print(f"   Project ID: {team_composition.project_id}")
                print(f"   Agent Count: {len(team_composition.agents)}")
                print(f"   Coordination Strategy: {team_composition.coordination_strategy}")
                
                # Display agent details
                print(f"\n👥 Generated Agents:")
                for j, agent in enumerate(team_composition.agents, 1):
                    print(f"   {j}. {agent.name} ({agent.agent_type.value})")
                    print(f"      Description: {agent.description}")
                    print(f"      Capabilities: {', '.join(agent.capabilities[:3])}...")
                    print(f"      Tools: {', '.join(agent.tools[:2])}...")
                    
        except Exception as e:
            print(f"❌ Test case failed: {e}")
            import traceback
            traceback.print_exc()

async def test_agent_optimization():
    """Test agent optimization capabilities"""
    print("\n🔧 Testing Agent Optimization")
    print("=" * 60)
    
    try:
        async with ArchonIntegration() as archon:
            # Create a sample agent for optimization
            sample_agent = AgentSpecification(
                agent_id="test_agent_001",
                agent_type=AgentType.DEVELOPER,
                name="Test Developer Agent",
                description="Sample developer agent for optimization testing",
                capabilities=["code_generation", "debugging"],
                tools=["development_tools"],
                performance_targets={"code_quality": 0.8, "efficiency": 0.7}
            )
            
            # Register agent
            archon.active_agents[sample_agent.agent_id] = sample_agent
            
            # Create sample performance data
            performance_data = {
                "current_performance": 0.65,
                "efficiency_score": 0.6,
                "accuracy_score": 0.7,
                "error_rate": 0.15,
                "execution_time": 2.5
            }
            
            print(f"🔍 Optimizing agent: {sample_agent.name}")
            print(f"   Current Performance: {performance_data['current_performance']}")
            print(f"   Efficiency Score: {performance_data['efficiency_score']}")
            print(f"   Accuracy Score: {performance_data['accuracy_score']}")
            
            # Test different optimization strategies
            strategies = [
                OptimizationStrategy.PERFORMANCE,
                OptimizationStrategy.EFFICIENCY,
                OptimizationStrategy.ACCURACY
            ]
            
            for strategy in strategies:
                print(f"\n🎯 Testing {strategy.value} optimization...")
                
                try:
                    evolution_plan = await archon.optimize_agent(
                        sample_agent.agent_id,
                        strategy,
                        performance_data
                    )
                    
                    print(f"   ✅ Evolution plan created!")
                    print(f"   Current Version: {evolution_plan.current_version}")
                    print(f"   Target Version: {evolution_plan.target_version}")
                    print(f"   Improvement Areas: {', '.join(evolution_plan.improvement_areas)}")
                    print(f"   Implementation Steps: {len(evolution_plan.implementation_steps)} steps")
                    
                except Exception as e:
                    print(f"   ❌ Optimization failed: {e}")
                    
    except Exception as e:
        print(f"❌ Agent optimization test failed: {e}")

async def test_agent_evaluation():
    """Test agent performance evaluation"""
    print("\n📊 Testing Agent Evaluation")
    print("=" * 60)
    
    try:
        async with ArchonIntegration() as archon:
            # Create sample agents for evaluation
            test_agents = [
                {
                    "agent_id": "eval_agent_001",
                    "name": "High Performance Agent",
                    "performance_data": {
                        "task_completion_rate": 0.95,
                        "accuracy": 0.92,
                        "efficiency": 0.88,
                        "collaboration_score": 0.85
                    }
                },
                {
                    "agent_id": "eval_agent_002", 
                    "name": "Average Performance Agent",
                    "performance_data": {
                        "task_completion_rate": 0.75,
                        "accuracy": 0.78,
                        "efficiency": 0.72,
                        "collaboration_score": 0.70
                    }
                },
                {
                    "agent_id": "eval_agent_003",
                    "name": "Low Performance Agent",
                    "performance_data": {
                        "task_completion_rate": 0.55,
                        "accuracy": 0.60,
                        "efficiency": 0.58,
                        "collaboration_score": 0.50
                    }
                }
            ]
            
            for agent_data in test_agents:
                print(f"\n🔍 Evaluating: {agent_data['name']}")
                
                evaluation_context = {
                    "evaluation_type": "performance_assessment",
                    "metrics_source": "simulation",
                    "evaluation_period": "last_24_hours",
                    **agent_data["performance_data"]
                }
                
                try:
                    metrics = await archon.evaluate_agent_performance(
                        agent_data["agent_id"],
                        evaluation_context
                    )
                    
                    print(f"   ✅ Evaluation complete!")
                    print(f"   Overall Score: {metrics.overall_score:.2f}")
                    print(f"   Performance: {metrics.performance_score:.2f}")
                    print(f"   Accuracy: {metrics.accuracy_score:.2f}")
                    print(f"   Efficiency: {metrics.efficiency_score:.2f}")
                    print(f"   Collaboration: {metrics.collaboration_score:.2f}")
                    print(f"   Success Rate: {metrics.success_rate:.2f}")
                    
                    # Check if optimization was triggered
                    if metrics.overall_score < 0.7:
                        print(f"   ⚠️  Performance below threshold - optimization triggered")
                    else:
                        print(f"   ✅ Performance within acceptable range")
                        
                except Exception as e:
                    print(f"   ❌ Evaluation failed: {e}")
                    
    except Exception as e:
        print(f"❌ Agent evaluation test failed: {e}")

async def test_lifecycle_monitoring():
    """Test agent lifecycle monitoring"""
    print("\n🔄 Testing Agent Lifecycle Monitoring")
    print("=" * 60)
    
    try:
        async with ArchonIntegration() as archon:
            # Create sample agents in different lifecycle stages
            test_agents = [
                "lifecycle_agent_001",
                "lifecycle_agent_002", 
                "lifecycle_agent_003"
            ]
            
            for agent_id in test_agents:
                print(f"\n🔍 Monitoring lifecycle for: {agent_id}")
                
                try:
                    lifecycle_data = await archon.monitor_agent_lifecycle(agent_id)
                    
                    print(f"   ✅ Lifecycle monitoring complete!")
                    print(f"   Current Stage: {lifecycle_data.get('current_stage', 'unknown')}")
                    print(f"   Health Status: {lifecycle_data.get('lifecycle_health', 'unknown')}")
                    
                    if lifecycle_data.get('next_stage'):
                        print(f"   🔄 Stage Transition: {lifecycle_data['current_stage']} → {lifecycle_data['next_stage']}")
                    
                    monitoring_data = lifecycle_data.get('monitoring_data', {})
                    if monitoring_data.get('alerts'):
                        print(f"   ⚠️  Alerts: {len(monitoring_data['alerts'])} active")
                        for alert in monitoring_data['alerts'][:2]:
                            print(f"      - {alert}")
                            
                except Exception as e:
                    print(f"   ❌ Lifecycle monitoring failed: {e}")
                    
    except Exception as e:
        print(f"❌ Lifecycle monitoring test failed: {e}")

async def test_orchestrator_integration():
    """Test Archon integration with orchestrator"""
    print("\n🔗 Testing Orchestrator Integration")
    print("=" * 60)
    
    try:
        executor = ArchonIntegrationExecutor()
        
        # Test agent generation execution
        project_context = {
            "project_id": "integration_test_001",
            "project_type": "web_application",
            "technology_stack": ["React", "Node.js", "MongoDB"],
            "requirements": ["scalable", "secure", "maintainable"],
            "team_size": 4
        }
        
        result = await executor.execute_agent_generation(
            project_id="integration_test_001",
            project_prompt="Build a modern web application with real-time features",
            project_context=project_context
        )
        
        print(f"✅ Orchestrator integration test complete!")
        print(f"   Success: {result['success']}")
        if result['success']:
            print(f"   Team ID: {result['team_id']}")
            print(f"   Agent Count: {result['agent_count']}")
            print(f"   Coordination Strategy: {result['coordination_strategy']}")
            print(f"   Generated Agents:")
            for agent in result['agents'][:3]:  # Show first 3 agents
                print(f"      - {agent['name']} ({agent['agent_type']})")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Orchestrator integration test failed: {e}")

def print_usage():
    """Print usage information"""
    print("""
🤖 Archon Integration Test Suite

Usage:
    python test_archon_integration.py [command]

Commands:
    test        Run comprehensive Archon tests (default)
    generation  Test agent team generation only
    optimization Test agent optimization only
    evaluation  Test agent evaluation only
    lifecycle   Test lifecycle monitoring only
    integration Test orchestrator integration
    help        Show this help message

Examples:
    python test_archon_integration.py test
    python test_archon_integration.py generation
    python test_archon_integration.py optimization

The Archon Integration will:
1. 🤖 Generate dynamic agent teams based on project requirements
2. 🔧 Optimize agents for performance, accuracy, and efficiency
3. 📊 Evaluate agent performance with comprehensive metrics
4. 🔄 Monitor agent lifecycle from conception to retirement
5. 🧠 Evolve agents based on performance and requirements
6. 🔗 Integrate seamlessly with Aetherforge orchestrator

Archon capabilities include:
- Dynamic agent generation and composition
- Multi-strategy optimization (performance, accuracy, efficiency)
- Comprehensive performance evaluation and metrics
- Intelligent lifecycle management and monitoring
- Adaptive team evolution and coordination
- Predictive optimization and proactive management
""")

async def main():
    """Main entry point"""
    command = sys.argv[1] if len(sys.argv) > 1 else "test"
    
    if command == "help":
        print_usage()
    elif command == "generation":
        await test_agent_team_generation()
    elif command == "optimization":
        await test_agent_optimization()
    elif command == "evaluation":
        await test_agent_evaluation()
    elif command == "lifecycle":
        await test_lifecycle_monitoring()
    elif command == "integration":
        await test_orchestrator_integration()
    elif command == "test":
        await test_agent_team_generation()
        await test_agent_optimization()
        await test_agent_evaluation()
        await test_lifecycle_monitoring()
        await test_orchestrator_integration()
    else:
        print(f"Unknown command: {command}")
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
