#!/bin/bash

# Aetherforge Health Check Script
# Comprehensive health monitoring for all system components

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
HEALTH_LOG="/var/log/aetherforge/health.log"
ALERT_WEBHOOK="${ALERT_WEBHOOK_URL:-}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yml}"

# Health check configuration
TIMEOUT=30
RETRY_COUNT=3
CRITICAL_SERVICES=("orchestrator" "postgres" "redis")
ALL_SERVICES=("orchestrator" "archon" "mcp-crawl4ai" "pheromind" "bmad" "postgres" "redis" "nginx" "prometheus" "grafana")

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "${timestamp} [${level}] ${message}" >> "$HEALTH_LOG" 2>/dev/null || true
    
    case $level in
        "ERROR")
            echo -e "${RED}❌ ${message}${NC}" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ ${message}${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ ${message}${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️ ${message}${NC}"
            ;;
    esac
}

# Send alert
send_alert() {
    local severity=$1
    local message=$2
    
    if [[ -n "$ALERT_WEBHOOK" ]]; then
        local payload=$(cat << EOF
{
    "text": "🚨 Aetherforge Alert",
    "attachments": [
        {
            "color": "$([ "$severity" = "critical" ] && echo "danger" || echo "warning")",
            "fields": [
                {
                    "title": "Severity",
                    "value": "$severity",
                    "short": true
                },
                {
                    "title": "Host",
                    "value": "$(hostname)",
                    "short": true
                },
                {
                    "title": "Message",
                    "value": "$message",
                    "short": false
                },
                {
                    "title": "Timestamp",
                    "value": "$(date)",
                    "short": true
                }
            ]
        }
    ]
}
EOF
)
        curl -X POST -H "Content-Type: application/json" \
             -d "$payload" "$ALERT_WEBHOOK" &>/dev/null || true
    fi
}

# Check if service is running
check_service_running() {
    local service=$1
    
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps "$service" 2>/dev/null | grep -q "Up"; then
        return 0
    else
        return 1
    fi
}

# Check service health endpoint
check_health_endpoint() {
    local service=$1
    local url=$2
    local expected_status=${3:-200}
    
    local response_code
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$TIMEOUT" "$url" 2>/dev/null || echo "000")
    
    if [[ "$response_code" == "$expected_status" ]]; then
        return 0
    else
        log "ERROR" "$service health endpoint returned $response_code (expected $expected_status)"
        return 1
    fi
}

# Check database connectivity
check_database() {
    log "INFO" "Checking PostgreSQL database..."
    
    if ! check_service_running "postgres"; then
        log "ERROR" "PostgreSQL container is not running"
        return 1
    fi
    
    # Test database connection
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres pg_isready -U aetherforge &>/dev/null; then
        log "SUCCESS" "PostgreSQL is healthy"
        
        # Check database size and connections
        local db_info
        db_info=$(docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres psql -U aetherforge -d aetherforge -t -c "
            SELECT 
                pg_size_pretty(pg_database_size('aetherforge')) as db_size,
                count(*) as active_connections
            FROM pg_stat_activity 
            WHERE datname = 'aetherforge';" 2>/dev/null || echo "N/A N/A")
        
        log "INFO" "Database info: $db_info"
        return 0
    else
        log "ERROR" "PostgreSQL is not responding"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "INFO" "Checking Redis..."
    
    if ! check_service_running "redis"; then
        log "ERROR" "Redis container is not running"
        return 1
    fi
    
    # Test Redis connection
    if docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log "SUCCESS" "Redis is healthy"
        
        # Check Redis info
        local redis_info
        redis_info=$(docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T redis redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r' || echo "N/A")
        log "INFO" "Redis memory usage: $redis_info"
        return 0
    else
        log "ERROR" "Redis is not responding"
        return 1
    fi
}

# Check orchestrator service
check_orchestrator() {
    log "INFO" "Checking Orchestrator service..."
    
    if ! check_service_running "orchestrator"; then
        log "ERROR" "Orchestrator container is not running"
        return 1
    fi
    
    # Check health endpoint
    if check_health_endpoint "orchestrator" "http://localhost:8000/health"; then
        log "SUCCESS" "Orchestrator is healthy"
        
        # Get additional metrics
        local metrics
        metrics=$(curl -s --max-time 10 "http://localhost:8000/metrics" 2>/dev/null | grep -E "(active_projects|total_requests)" | head -2 || echo "Metrics unavailable")
        log "INFO" "Orchestrator metrics: $metrics"
        return 0
    else
        log "ERROR" "Orchestrator health check failed"
        return 1
    fi
}

# Check component services
check_component_service() {
    local service=$1
    local port=$2
    local health_path=${3:-"/health"}
    
    log "INFO" "Checking $service service..."
    
    if ! check_service_running "$service"; then
        log "ERROR" "$service container is not running"
        return 1
    fi
    
    # Check if port is accessible
    if nc -z localhost "$port" 2>/dev/null; then
        log "SUCCESS" "$service port $port is accessible"
        
        # Try health endpoint if available
        if check_health_endpoint "$service" "http://localhost:$port$health_path" 2>/dev/null; then
            log "SUCCESS" "$service health endpoint is responding"
        else
            log "WARNING" "$service health endpoint not available or not responding"
        fi
        return 0
    else
        log "ERROR" "$service port $port is not accessible"
        return 1
    fi
}

# Check system resources
check_system_resources() {
    log "INFO" "Checking system resources..."
    
    # Check disk space
    local disk_usage
    disk_usage=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt 90 ]]; then
        log "ERROR" "Disk usage is critical: ${disk_usage}%"
        send_alert "critical" "Disk usage is at ${disk_usage}%"
        return 1
    elif [[ $disk_usage -gt 80 ]]; then
        log "WARNING" "Disk usage is high: ${disk_usage}%"
        send_alert "warning" "Disk usage is at ${disk_usage}%"
    else
        log "SUCCESS" "Disk usage is normal: ${disk_usage}%"
    fi
    
    # Check memory usage
    local memory_usage
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [[ $memory_usage -gt 90 ]]; then
        log "ERROR" "Memory usage is critical: ${memory_usage}%"
        send_alert "critical" "Memory usage is at ${memory_usage}%"
        return 1
    elif [[ $memory_usage -gt 80 ]]; then
        log "WARNING" "Memory usage is high: ${memory_usage}%"
    else
        log "SUCCESS" "Memory usage is normal: ${memory_usage}%"
    fi
    
    # Check load average
    local load_avg
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores
    cpu_cores=$(nproc)
    
    if (( $(echo "$load_avg > $cpu_cores * 2" | bc -l) )); then
        log "WARNING" "Load average is high: $load_avg (cores: $cpu_cores)"
    else
        log "SUCCESS" "Load average is normal: $load_avg (cores: $cpu_cores)"
    fi
    
    return 0
}

# Check Docker health
check_docker_health() {
    log "INFO" "Checking Docker health..."
    
    # Check Docker daemon
    if ! docker info &>/dev/null; then
        log "ERROR" "Docker daemon is not running"
        return 1
    fi
    
    # Check Docker Compose
    if ! docker-compose --version &>/dev/null; then
        log "ERROR" "Docker Compose is not available"
        return 1
    fi
    
    # Check container health
    local unhealthy_containers
    unhealthy_containers=$(docker ps --filter "health=unhealthy" --format "table {{.Names}}" | tail -n +2)
    
    if [[ -n "$unhealthy_containers" ]]; then
        log "ERROR" "Unhealthy containers found: $unhealthy_containers"
        return 1
    fi
    
    log "SUCCESS" "Docker health check passed"
    return 0
}

# Generate health report
generate_health_report() {
    local overall_status="healthy"
    local failed_checks=()
    
    echo "========================================"
    echo "Aetherforge Health Check Report"
    echo "========================================"
    echo "Timestamp: $(date)"
    echo "Host: $(hostname)"
    echo "Environment: ${AETHERFORGE_ENV:-development}"
    echo ""
    
    # Check critical services
    echo "Critical Services:"
    for service in "${CRITICAL_SERVICES[@]}"; do
        case $service in
            "orchestrator")
                if check_orchestrator; then
                    echo "  ✅ $service: Healthy"
                else
                    echo "  ❌ $service: Failed"
                    failed_checks+=("$service")
                    overall_status="unhealthy"
                fi
                ;;
            "postgres")
                if check_database; then
                    echo "  ✅ $service: Healthy"
                else
                    echo "  ❌ $service: Failed"
                    failed_checks+=("$service")
                    overall_status="unhealthy"
                fi
                ;;
            "redis")
                if check_redis; then
                    echo "  ✅ $service: Healthy"
                else
                    echo "  ❌ $service: Failed"
                    failed_checks+=("$service")
                    overall_status="unhealthy"
                fi
                ;;
        esac
    done
    
    echo ""
    echo "Component Services:"
    
    # Check component services
    local component_services=(
        "archon:8100"
        "mcp-crawl4ai:8051"
        "pheromind:8502"
        "bmad:8503"
        "nginx:80"
        "prometheus:9090"
        "grafana:3001"
    )
    
    for service_port in "${component_services[@]}"; do
        local service=$(echo "$service_port" | cut -d: -f1)
        local port=$(echo "$service_port" | cut -d: -f2)
        
        if check_component_service "$service" "$port"; then
            echo "  ✅ $service: Healthy"
        else
            echo "  ❌ $service: Failed"
            failed_checks+=("$service")
            if [[ "$overall_status" == "healthy" ]]; then
                overall_status="degraded"
            fi
        fi
    done
    
    echo ""
    echo "System Resources:"
    if check_system_resources; then
        echo "  ✅ System resources: Normal"
    else
        echo "  ⚠️ System resources: Issues detected"
        if [[ "$overall_status" == "healthy" ]]; then
            overall_status="degraded"
        fi
    fi
    
    echo ""
    echo "Docker Health:"
    if check_docker_health; then
        echo "  ✅ Docker: Healthy"
    else
        echo "  ❌ Docker: Issues detected"
        failed_checks+=("docker")
        overall_status="unhealthy"
    fi
    
    echo ""
    echo "========================================"
    echo "Overall Status: $overall_status"
    
    if [[ ${#failed_checks[@]} -gt 0 ]]; then
        echo "Failed Checks: ${failed_checks[*]}"
        
        # Send alert for critical failures
        if [[ "$overall_status" == "unhealthy" ]]; then
            send_alert "critical" "Health check failed for critical services: ${failed_checks[*]}"
        fi
    fi
    
    echo "========================================"
    
    # Return appropriate exit code
    case $overall_status in
        "healthy")
            return 0
            ;;
        "degraded")
            return 1
            ;;
        "unhealthy")
            return 2
            ;;
    esac
}

# Main function
main() {
    local command=${1:-"check"}
    
    # Create log directory
    mkdir -p "$(dirname "$HEALTH_LOG")"
    
    case $command in
        "check"|"")
            generate_health_report
            ;;
        "monitor")
            log "INFO" "Starting continuous health monitoring..."
            while true; do
                generate_health_report > /dev/null
                sleep 60
            done
            ;;
        "critical")
            log "INFO" "Checking only critical services..."
            for service in "${CRITICAL_SERVICES[@]}"; do
                case $service in
                    "orchestrator") check_orchestrator ;;
                    "postgres") check_database ;;
                    "redis") check_redis ;;
                esac
            done
            ;;
        *)
            echo "Usage: $0 [check|monitor|critical]"
            exit 1
            ;;
    esac
}

# Make script executable
chmod +x "$0"

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
