"""
API Resilience Layer for Aetherforge
Provides comprehensive fallback, retry, and quota management for all API calls
"""

import asyncio
import time
import logging
import json
import os
from typing import Dict, Any, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import aiohttp
from datetime import datetime, timedelta

try:
    from .api_manager import APIProvider, APIManager, APIError, QuotaExceededError, RateLimitError
except ImportError:
    from api_manager import APIProvider, APIManager, APIError, QuotaExceededError, RateLimitError

logger = logging.getLogger(__name__)

class NotificationManager:
    """Manages user notifications for API resilience events"""

    def __init__(self):
        self.notifications: List[Dict[str, Any]] = []
        self.max_notifications = 50

    def add_notification(self,
                        notification_type: str,
                        message: str,
                        severity: str = "info",
                        provider: Optional[APIProvider] = None):
        """Add a notification"""
        notification = {
            "timestamp": datetime.now().isoformat(),
            "type": notification_type,
            "message": message,
            "severity": severity,  # info, warning, error
            "provider": provider.value if provider else None
        }

        self.notifications.append(notification)

        # Keep only recent notifications
        if len(self.notifications) > self.max_notifications:
            self.notifications = self.notifications[-self.max_notifications:]

        # Log based on severity
        if severity == "error":
            logger.error(f"[{notification_type}] {message}")
        elif severity == "warning":
            logger.warning(f"[{notification_type}] {message}")
        else:
            logger.info(f"[{notification_type}] {message}")

    def get_recent_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent notifications"""
        return self.notifications[-limit:]

    def get_notifications_by_type(self, notification_type: str) -> List[Dict[str, Any]]:
        """Get notifications by type"""
        return [n for n in self.notifications if n["type"] == notification_type]

    def clear_notifications(self):
        """Clear all notifications"""
        self.notifications.clear()

class FallbackStrategy(Enum):
    """Fallback strategies for API failures"""
    PROVIDER_FALLBACK = "provider_fallback"  # Try different providers
    MODEL_FALLBACK = "model_fallback"        # Try different models
    DEGRADED_SERVICE = "degraded_service"    # Provide limited functionality
    CACHE_FALLBACK = "cache_fallback"        # Use cached responses

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retry_on_quota: bool = False
    retry_on_rate_limit: bool = True

@dataclass
class FallbackConfig:
    """Configuration for fallback behavior"""
    enable_provider_fallback: bool = True
    enable_model_fallback: bool = True
    enable_degraded_service: bool = True
    enable_cache_fallback: bool = True
    fallback_providers: List[APIProvider] = field(default_factory=lambda: [
        APIProvider.OPENAI, APIProvider.ANTHROPIC, APIProvider.LOCAL
    ])
    fallback_models: Dict[APIProvider, List[str]] = field(default_factory=lambda: {
        APIProvider.OPENAI: ["gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
        APIProvider.ANTHROPIC: ["claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
        APIProvider.LOCAL: ["llama2", "codellama", "mistral"]
    })

@dataclass
class QuotaUsage:
    """Track API quota usage"""
    provider: APIProvider
    requests_made: int = 0
    tokens_used: int = 0
    last_reset: datetime = field(default_factory=datetime.now)
    daily_limit: Optional[int] = None
    monthly_limit: Optional[int] = None
    cost_estimate: float = 0.0

@dataclass
class APICallResult:
    """Result of an API call with metadata"""
    success: bool
    response: Optional[str] = None
    provider_used: Optional[APIProvider] = None
    model_used: Optional[str] = None
    fallback_used: bool = False
    retries_attempted: int = 0
    total_time: float = 0.0
    error: Optional[str] = None
    quota_remaining: Optional[int] = None

class QuotaManager:
    """Manages API quota tracking and warnings"""
    
    def __init__(self, storage_path: Optional[str] = None):
        self.storage_path = Path(storage_path or os.path.expanduser("~/.aetherforge/quota.json"))
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self.usage_data: Dict[str, QuotaUsage] = {}
        self._load_usage_data()
    
    def _load_usage_data(self):
        """Load quota usage data from storage"""
        if self.storage_path.exists():
            try:
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                    for provider_name, usage_dict in data.items():
                        provider = APIProvider(provider_name)
                        self.usage_data[provider_name] = QuotaUsage(
                            provider=provider,
                            requests_made=usage_dict.get('requests_made', 0),
                            tokens_used=usage_dict.get('tokens_used', 0),
                            last_reset=datetime.fromisoformat(usage_dict.get('last_reset', datetime.now().isoformat())),
                            daily_limit=usage_dict.get('daily_limit'),
                            monthly_limit=usage_dict.get('monthly_limit'),
                            cost_estimate=usage_dict.get('cost_estimate', 0.0)
                        )
            except Exception as e:
                logger.warning(f"Failed to load quota data: {e}")
    
    def _save_usage_data(self):
        """Save quota usage data to storage"""
        try:
            data = {}
            for provider_name, usage in self.usage_data.items():
                data[provider_name] = {
                    'requests_made': usage.requests_made,
                    'tokens_used': usage.tokens_used,
                    'last_reset': usage.last_reset.isoformat(),
                    'daily_limit': usage.daily_limit,
                    'monthly_limit': usage.monthly_limit,
                    'cost_estimate': usage.cost_estimate
                }
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save quota data: {e}")
    
    def record_usage(self, provider: APIProvider, tokens: int = 0, cost: float = 0.0):
        """Record API usage"""
        provider_name = provider.value
        if provider_name not in self.usage_data:
            self.usage_data[provider_name] = QuotaUsage(provider=provider)
        
        usage = self.usage_data[provider_name]
        usage.requests_made += 1
        usage.tokens_used += tokens
        usage.cost_estimate += cost
        
        # Reset daily counters if needed
        if datetime.now() - usage.last_reset > timedelta(days=1):
            usage.requests_made = 1
            usage.tokens_used = tokens
            usage.last_reset = datetime.now()
        
        self._save_usage_data()
    
    def get_usage_stats(self, provider: APIProvider) -> Dict[str, Any]:
        """Get usage statistics for a provider"""
        provider_name = provider.value
        if provider_name not in self.usage_data:
            return {"requests": 0, "tokens": 0, "cost": 0.0, "warnings": []}
        
        usage = self.usage_data[provider_name]
        warnings = []
        
        # Check for quota warnings
        if usage.daily_limit and usage.requests_made > usage.daily_limit * 0.8:
            warnings.append(f"Approaching daily request limit ({usage.requests_made}/{usage.daily_limit})")
        
        if usage.monthly_limit and usage.requests_made > usage.monthly_limit * 0.8:
            warnings.append(f"Approaching monthly request limit")
        
        if usage.cost_estimate > 50.0:  # $50 threshold
            warnings.append(f"High API costs: ${usage.cost_estimate:.2f}")
        
        return {
            "requests": usage.requests_made,
            "tokens": usage.tokens_used,
            "cost": usage.cost_estimate,
            "warnings": warnings,
            "last_reset": usage.last_reset.isoformat()
        }
    
    def estimate_remaining_quota(self, provider: APIProvider) -> Optional[int]:
        """Estimate remaining quota for a provider"""
        provider_name = provider.value
        if provider_name not in self.usage_data:
            return None
        
        usage = self.usage_data[provider_name]
        if usage.daily_limit:
            return max(0, usage.daily_limit - usage.requests_made)
        return None

class APIResilienceLayer:
    """Main resilience layer that wraps all API calls"""
    
    def __init__(self,
                 api_manager: Optional[APIManager] = None,
                 retry_config: Optional[RetryConfig] = None,
                 fallback_config: Optional[FallbackConfig] = None):
        self.api_manager = api_manager or APIManager()
        self.retry_config = retry_config or RetryConfig()
        self.fallback_config = fallback_config or FallbackConfig()
        self.quota_manager = QuotaManager()
        self.notification_manager = NotificationManager()
        self.response_cache: Dict[str, Tuple[str, datetime]] = {}
        self.cache_ttl = timedelta(hours=1)  # Cache responses for 1 hour
    
    async def generate_text_resilient(self, 
                                    messages: List[Dict[str, str]], 
                                    preferred_provider: Optional[APIProvider] = None,
                                    **kwargs) -> APICallResult:
        """Generate text with full resilience features"""
        start_time = time.time()
        
        # Check cache first if enabled
        if self.fallback_config.enable_cache_fallback:
            cache_key = self._get_cache_key(messages, kwargs)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                self.notification_manager.add_notification(
                    "cache_hit",
                    "Using cached response to avoid API call",
                    "info"
                )
                return APICallResult(
                    success=True,
                    response=cached_response,
                    provider_used=None,
                    fallback_used=True,
                    total_time=time.time() - start_time
                )
        
        # Determine providers to try
        providers_to_try = self._get_provider_order(preferred_provider)
        
        last_error = None
        total_retries = 0
        
        for provider in providers_to_try:
            # Check quota before attempting
            quota_stats = self.quota_manager.get_usage_stats(provider)
            if quota_stats["warnings"]:
                for warning in quota_stats["warnings"]:
                    self.notification_manager.add_notification(
                        "quota_warning",
                        f"{provider.value}: {warning}",
                        "warning",
                        provider
                    )
            
            # Try different models for this provider if enabled
            models_to_try = self._get_model_order(provider)
            
            for model in models_to_try:
                try:
                    result = await self._attempt_api_call(
                        provider, model, messages, **kwargs
                    )

                    # Cache successful response
                    if result.success and self.fallback_config.enable_cache_fallback:
                        cache_key = self._get_cache_key(messages, kwargs)
                        self._cache_response(cache_key, result.response)

                    # Notify about successful call with fallback info
                    if result.fallback_used:
                        self.notification_manager.add_notification(
                            "provider_fallback",
                            f"Successfully used fallback provider {result.provider_used.value}/{result.model_used}",
                            "info",
                            result.provider_used
                        )

                    result.total_time = time.time() - start_time
                    result.retries_attempted = total_retries + result.retries_attempted
                    return result

                except Exception as e:
                    last_error = e
                    total_retries += 1
                    logger.warning(f"Failed with {provider.value}/{model}: {e}")
                    continue
        
        # All providers and models failed, try degraded service
        if self.fallback_config.enable_degraded_service:
            self.notification_manager.add_notification(
                "degraded_service",
                "All API providers failed, using degraded service response",
                "warning"
            )
            degraded_response = self._get_degraded_response(messages)
            return APICallResult(
                success=True,
                response=degraded_response,
                fallback_used=True,
                total_time=time.time() - start_time,
                retries_attempted=total_retries
            )
        
        # Complete failure
        return APICallResult(
            success=False,
            error=str(last_error) if last_error else "All API providers failed",
            total_time=time.time() - start_time,
            retries_attempted=total_retries
        )

    async def _attempt_api_call(self,
                               provider: APIProvider,
                               model: str,
                               messages: List[Dict[str, str]],
                               **kwargs) -> APICallResult:
        """Attempt API call with retry logic"""
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                # Update model in API manager config
                if provider in self.api_manager.providers:
                    self.api_manager.providers[provider].model = model

                response = await self.api_manager.generate_text(
                    messages, preferred_provider=provider, **kwargs
                )

                # Record successful usage
                estimated_tokens = len(str(messages)) // 4  # Rough estimate
                self.quota_manager.record_usage(provider, tokens=estimated_tokens)

                return APICallResult(
                    success=True,
                    response=response,
                    provider_used=provider,
                    model_used=model,
                    fallback_used=provider != self._get_preferred_provider()
                )

            except QuotaExceededError as e:
                self.notification_manager.add_notification(
                    "quota_exceeded",
                    f"API quota exceeded for {provider.value}",
                    "error",
                    provider
                )
                if not self.retry_config.retry_on_quota:
                    raise

            except RateLimitError as e:
                self.notification_manager.add_notification(
                    "rate_limit",
                    f"Rate limit hit for {provider.value}, waiting {e.retry_after}s",
                    "warning",
                    provider
                )
                if self.retry_config.retry_on_rate_limit and attempt < self.retry_config.max_retries:
                    await asyncio.sleep(e.retry_after)
                    continue
                else:
                    raise

            except Exception as e:
                if attempt == self.retry_config.max_retries:
                    raise

                # Calculate delay with exponential backoff and jitter
                delay = min(
                    self.retry_config.base_delay * (self.retry_config.exponential_base ** attempt),
                    self.retry_config.max_delay
                )

                if self.retry_config.jitter:
                    import random
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter

                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay:.1f}s: {e}")
                await asyncio.sleep(delay)

        raise APIError(f"All retry attempts failed for {provider.value}/{model}", provider)

    def _get_provider_order(self, preferred_provider: Optional[APIProvider] = None) -> List[APIProvider]:
        """Get ordered list of providers to try"""
        providers = []

        # Add preferred provider first
        if preferred_provider and preferred_provider in self.api_manager.providers:
            providers.append(preferred_provider)

        # Add fallback providers
        if self.fallback_config.enable_provider_fallback:
            for provider in self.fallback_config.fallback_providers:
                if provider not in providers and provider in self.api_manager.providers:
                    providers.append(provider)

        return providers

    def _get_model_order(self, provider: APIProvider) -> List[str]:
        """Get ordered list of models to try for a provider"""
        models = []

        # Add current configured model first
        if provider in self.api_manager.providers:
            current_model = self.api_manager.providers[provider].model
            models.append(current_model)

        # Add fallback models
        if self.fallback_config.enable_model_fallback:
            fallback_models = self.fallback_config.fallback_models.get(provider, [])
            for model in fallback_models:
                if model not in models:
                    models.append(model)

        return models or ["gpt-3.5-turbo"]  # Default fallback

    def _get_preferred_provider(self) -> Optional[APIProvider]:
        """Get the preferred provider (first in fallback order)"""
        if self.fallback_config.fallback_providers:
            return self.fallback_config.fallback_providers[0]
        return None

    def _get_cache_key(self, messages: List[Dict[str, str]], kwargs: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        import hashlib
        content = json.dumps({"messages": messages, "kwargs": kwargs}, sort_keys=True)
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached response if still valid"""
        if cache_key in self.response_cache:
            response, timestamp = self.response_cache[cache_key]
            if datetime.now() - timestamp < self.cache_ttl:
                return response
            else:
                # Remove expired cache entry
                del self.response_cache[cache_key]
        return None

    def _cache_response(self, cache_key: str, response: str):
        """Cache a response"""
        self.response_cache[cache_key] = (response, datetime.now())

        # Clean up old cache entries (keep only last 100)
        if len(self.response_cache) > 100:
            oldest_keys = sorted(
                self.response_cache.keys(),
                key=lambda k: self.response_cache[k][1]
            )[:50]
            for key in oldest_keys:
                del self.response_cache[key]

    def _get_degraded_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate degraded service response when all APIs fail"""
        user_content = ""
        for msg in messages:
            if msg.get("role") == "user":
                user_content = msg.get("content", "")
                break

        return f"""I apologize, but I'm currently experiencing technical difficulties with my AI services.

Your request: "{user_content[:100]}{'...' if len(user_content) > 100 else ''}"

I'm unable to provide a full AI-generated response at this time due to API service issues.
Please try again in a few minutes, or contact support if the issue persists.

This is a fallback response generated when all AI providers are unavailable."""

    def get_resilience_stats(self) -> Dict[str, Any]:
        """Get comprehensive resilience statistics"""
        stats = {
            "quota_usage": {},
            "cache_stats": {
                "entries": len(self.response_cache),
                "hit_rate": "N/A"  # Would need to track hits/misses
            },
            "provider_status": self.api_manager.get_provider_status(),
            "fallback_config": {
                "provider_fallback": self.fallback_config.enable_provider_fallback,
                "model_fallback": self.fallback_config.enable_model_fallback,
                "degraded_service": self.fallback_config.enable_degraded_service,
                "cache_fallback": self.fallback_config.enable_cache_fallback
            },
            "recent_notifications": self.notification_manager.get_recent_notifications(20),
            "notification_summary": {
                "total": len(self.notification_manager.notifications),
                "errors": len([n for n in self.notification_manager.notifications if n["severity"] == "error"]),
                "warnings": len([n for n in self.notification_manager.notifications if n["severity"] == "warning"]),
                "info": len([n for n in self.notification_manager.notifications if n["severity"] == "info"])
            }
        }

        # Add quota stats for each provider
        for provider in APIProvider:
            stats["quota_usage"][provider.value] = self.quota_manager.get_usage_stats(provider)

        return stats

    def get_user_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get user-friendly notifications"""
        return self.notification_manager.get_recent_notifications(limit)

    def clear_notifications(self):
        """Clear all notifications"""
        self.notification_manager.clear_notifications()

    async def test_all_providers(self) -> Dict[str, Dict[str, Any]]:
        """Test all configured providers and return status"""
        test_messages = [{"role": "user", "content": "Hello, this is a test."}]
        results = {}

        for provider in self.api_manager.get_available_providers():
            try:
                start_time = time.time()
                result = await self._attempt_api_call(provider,
                                                    self.api_manager.providers[provider].model,
                                                    test_messages,
                                                    max_tokens=10)

                results[provider.value] = {
                    "status": "healthy",
                    "response_time": time.time() - start_time,
                    "model": self.api_manager.providers[provider].model,
                    "response_preview": result.response[:50] if result.response else None
                }

            except Exception as e:
                results[provider.value] = {
                    "status": "error",
                    "error": str(e),
                    "model": self.api_manager.providers[provider].model
                }

        return results

# Global resilience layer instance
_resilience_layer: Optional[APIResilienceLayer] = None

def get_resilience_layer() -> APIResilienceLayer:
    """Get global resilience layer instance"""
    global _resilience_layer
    if _resilience_layer is None:
        _resilience_layer = APIResilienceLayer()
    return _resilience_layer

async def generate_text_with_resilience(messages: List[Dict[str, str]], **kwargs) -> APICallResult:
    """Convenience function for resilient text generation"""
    resilience_layer = get_resilience_layer()
    return await resilience_layer.generate_text_resilient(messages, **kwargs)
