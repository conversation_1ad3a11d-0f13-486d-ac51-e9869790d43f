"""
Configuration for the Archon Integration
Defines agent generation parameters, optimization settings, and lifecycle management
"""

from dataclasses import dataclass
from typing import Dict, List, Any
from enum import Enum

class ArchonMode(Enum):
    """Archon operation modes"""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    ENTERPRISE = "enterprise"

class AgentGenerationStrategy(Enum):
    """Strategies for agent generation"""
    TEMPLATE_BASED = "template_based"
    DYNAMIC_CREATION = "dynamic_creation"
    HYBRID_APPROACH = "hybrid_approach"
    AI_OPTIMIZED = "ai_optimized"

@dataclass
class ArchonConfig:
    """Configuration for the Archon Integration"""
    
    # Archon Service Configuration
    archon_url: str = "http://localhost:8100"
    archon_timeout: int = 300
    archon_enabled: bool = True
    
    # OpenAI Configuration
    openai_api_key: str = None
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 4000
    
    # Agent Generation Configuration
    default_archon_mode: str = "standard"
    max_agents_per_team: int = 8
    min_agents_per_team: int = 2
    enable_dynamic_generation: bool = True
    enable_agent_optimization: bool = True
    
    # Lifecycle Management Configuration
    enable_lifecycle_monitoring: bool = True
    monitoring_interval_seconds: int = 1800  # 30 minutes
    optimization_threshold: float = 0.7
    evolution_threshold: float = 0.6
    retirement_threshold: float = 0.4
    
    # Performance Configuration
    enable_performance_tracking: bool = True
    performance_history_retention_days: int = 30
    enable_predictive_optimization: bool = True
    
    # Team Coordination Configuration
    enable_team_optimization: bool = True
    team_performance_threshold: float = 0.75
    coordination_strategy: str = "adaptive"
    
    @staticmethod
    def get_archon_mode_config(mode: ArchonMode) -> Dict[str, Any]:
        """Get configuration for specific Archon modes"""
        configs = {
            ArchonMode.BASIC: {
                "max_agents": 4,
                "optimization_enabled": False,
                "monitoring_interval": 3600,
                "generation_strategy": "template_based",
                "performance_tracking": False
            },
            ArchonMode.STANDARD: {
                "max_agents": 6,
                "optimization_enabled": True,
                "monitoring_interval": 1800,
                "generation_strategy": "hybrid_approach",
                "performance_tracking": True
            },
            ArchonMode.ADVANCED: {
                "max_agents": 8,
                "optimization_enabled": True,
                "monitoring_interval": 900,
                "generation_strategy": "dynamic_creation",
                "performance_tracking": True,
                "predictive_optimization": True
            },
            ArchonMode.ENTERPRISE: {
                "max_agents": 12,
                "optimization_enabled": True,
                "monitoring_interval": 600,
                "generation_strategy": "ai_optimized",
                "performance_tracking": True,
                "predictive_optimization": True,
                "advanced_analytics": True
            }
        }
        return configs.get(mode, configs[ArchonMode.STANDARD])
    
    @staticmethod
    def get_agent_type_templates() -> Dict[str, Dict[str, Any]]:
        """Get templates for different agent types"""
        return {
            "analyst": {
                "base_capabilities": [
                    "requirement_analysis",
                    "stakeholder_communication",
                    "specification_creation",
                    "research_coordination"
                ],
                "required_tools": [
                    "research_engine",
                    "documentation_tools",
                    "communication_tools"
                ],
                "performance_targets": {
                    "accuracy": 0.9,
                    "completeness": 0.85,
                    "clarity": 0.9
                },
                "optimization_focus": ["accuracy", "completeness"]
            },
            "architect": {
                "base_capabilities": [
                    "system_design",
                    "architecture_planning",
                    "technology_selection",
                    "scalability_analysis"
                ],
                "required_tools": [
                    "design_tools",
                    "modeling_tools",
                    "analysis_tools"
                ],
                "performance_targets": {
                    "scalability": 0.9,
                    "maintainability": 0.85,
                    "performance": 0.8
                },
                "optimization_focus": ["scalability", "maintainability"]
            },
            "developer": {
                "base_capabilities": [
                    "code_generation",
                    "implementation",
                    "debugging",
                    "optimization"
                ],
                "required_tools": [
                    "development_tools",
                    "version_control",
                    "testing_tools"
                ],
                "performance_targets": {
                    "code_quality": 0.9,
                    "efficiency": 0.8,
                    "maintainability": 0.85
                },
                "optimization_focus": ["code_quality", "efficiency"]
            },
            "qa": {
                "base_capabilities": [
                    "testing",
                    "validation",
                    "quality_assessment",
                    "defect_detection"
                ],
                "required_tools": [
                    "testing_frameworks",
                    "quality_tools",
                    "automation_tools"
                ],
                "performance_targets": {
                    "test_coverage": 0.95,
                    "defect_detection": 0.9,
                    "accuracy": 0.95
                },
                "optimization_focus": ["test_coverage", "defect_detection"]
            },
            "researcher": {
                "base_capabilities": [
                    "information_gathering",
                    "analysis",
                    "synthesis",
                    "recommendation"
                ],
                "required_tools": [
                    "research_engine",
                    "analysis_tools",
                    "documentation_tools"
                ],
                "performance_targets": {
                    "thoroughness": 0.9,
                    "relevance": 0.85,
                    "accuracy": 0.9
                },
                "optimization_focus": ["thoroughness", "relevance"]
            },
            "optimizer": {
                "base_capabilities": [
                    "performance_analysis",
                    "optimization_planning",
                    "efficiency_improvement",
                    "resource_management"
                ],
                "required_tools": [
                    "profiling_tools",
                    "optimization_tools",
                    "monitoring_tools"
                ],
                "performance_targets": {
                    "optimization_effectiveness": 0.9,
                    "resource_efficiency": 0.85,
                    "impact_measurement": 0.8
                },
                "optimization_focus": ["optimization_effectiveness", "resource_efficiency"]
            },
            "monitor": {
                "base_capabilities": [
                    "system_monitoring",
                    "alert_management",
                    "performance_tracking",
                    "health_assessment"
                ],
                "required_tools": [
                    "monitoring_tools",
                    "alerting_systems",
                    "analytics_tools"
                ],
                "performance_targets": {
                    "detection_accuracy": 0.95,
                    "response_time": 0.9,
                    "coverage": 0.9
                },
                "optimization_focus": ["detection_accuracy", "response_time"]
            },
            "specialist": {
                "base_capabilities": [
                    "domain_expertise",
                    "specialized_analysis",
                    "expert_consultation",
                    "knowledge_application"
                ],
                "required_tools": [
                    "specialized_tools",
                    "domain_tools",
                    "consultation_tools"
                ],
                "performance_targets": {
                    "expertise_depth": 0.95,
                    "problem_solving": 0.9,
                    "knowledge_application": 0.85
                },
                "optimization_focus": ["expertise_depth", "problem_solving"]
            }
        }
    
    @staticmethod
    def get_optimization_strategies() -> Dict[str, Dict[str, Any]]:
        """Get optimization strategy configurations"""
        return {
            "performance": {
                "focus_areas": ["execution_speed", "resource_usage", "throughput"],
                "metrics": ["response_time", "cpu_usage", "memory_usage"],
                "improvement_targets": {"speed": 0.2, "efficiency": 0.15},
                "techniques": ["caching", "parallelization", "optimization"]
            },
            "accuracy": {
                "focus_areas": ["precision", "recall", "error_reduction"],
                "metrics": ["accuracy_score", "error_rate", "validation_score"],
                "improvement_targets": {"accuracy": 0.1, "error_reduction": 0.3},
                "techniques": ["validation", "verification", "testing"]
            },
            "efficiency": {
                "focus_areas": ["resource_optimization", "cost_reduction", "automation"],
                "metrics": ["resource_usage", "cost_per_operation", "automation_level"],
                "improvement_targets": {"efficiency": 0.25, "cost_reduction": 0.2},
                "techniques": ["automation", "optimization", "streamlining"]
            },
            "adaptability": {
                "focus_areas": ["flexibility", "learning", "evolution"],
                "metrics": ["adaptation_speed", "learning_rate", "flexibility_score"],
                "improvement_targets": {"adaptability": 0.3, "learning": 0.2},
                "techniques": ["machine_learning", "adaptation", "evolution"]
            },
            "collaboration": {
                "focus_areas": ["communication", "coordination", "teamwork"],
                "metrics": ["communication_effectiveness", "coordination_score", "team_performance"],
                "improvement_targets": {"collaboration": 0.2, "coordination": 0.15},
                "techniques": ["communication_enhancement", "coordination_improvement", "team_building"]
            },
            "specialization": {
                "focus_areas": ["domain_expertise", "skill_depth", "knowledge"],
                "metrics": ["expertise_level", "skill_depth", "knowledge_breadth"],
                "improvement_targets": {"specialization": 0.25, "expertise": 0.2},
                "techniques": ["training", "specialization", "knowledge_enhancement"]
            }
        }
    
    @staticmethod
    def get_lifecycle_stage_configs() -> Dict[str, Dict[str, Any]]:
        """Get configuration for different lifecycle stages"""
        return {
            "conception": {
                "duration_hours": 1,
                "required_inputs": ["requirements", "context"],
                "outputs": ["agent_specification"],
                "success_criteria": ["specification_completeness"]
            },
            "generation": {
                "duration_hours": 2,
                "required_inputs": ["agent_specification", "templates"],
                "outputs": ["agent_implementation"],
                "success_criteria": ["implementation_quality", "functionality"]
            },
            "optimization": {
                "duration_hours": 4,
                "required_inputs": ["performance_data", "optimization_strategy"],
                "outputs": ["optimized_agent"],
                "success_criteria": ["performance_improvement", "stability"]
            },
            "evaluation": {
                "duration_hours": 1,
                "required_inputs": ["agent_implementation", "test_cases"],
                "outputs": ["evaluation_results"],
                "success_criteria": ["test_pass_rate", "performance_metrics"]
            },
            "deployment": {
                "duration_hours": 1,
                "required_inputs": ["validated_agent", "deployment_config"],
                "outputs": ["deployed_agent"],
                "success_criteria": ["deployment_success", "operational_readiness"]
            },
            "monitoring": {
                "duration_hours": 24,  # Continuous
                "required_inputs": ["deployed_agent", "monitoring_config"],
                "outputs": ["monitoring_data"],
                "success_criteria": ["health_status", "performance_stability"]
            },
            "evolution": {
                "duration_hours": 8,
                "required_inputs": ["monitoring_data", "evolution_triggers"],
                "outputs": ["evolved_agent"],
                "success_criteria": ["improvement_achievement", "stability_maintenance"]
            },
            "retirement": {
                "duration_hours": 2,
                "required_inputs": ["retirement_decision", "migration_plan"],
                "outputs": ["retired_agent", "migration_complete"],
                "success_criteria": ["graceful_shutdown", "data_preservation"]
            }
        }
    
    @staticmethod
    def get_fallback_configurations() -> Dict[str, Any]:
        """Get fallback configurations when Archon service is unavailable"""
        return {
            "team_generation": {
                "default_team_size": 4,
                "agent_types": ["analyst", "architect", "developer", "qa"],
                "coordination_strategy": "bmad_methodology",
                "fallback_mode": True
            },
            "optimization": {
                "enabled": False,
                "fallback_improvements": {
                    "performance": 0.1,
                    "efficiency": 0.05
                }
            },
            "monitoring": {
                "basic_monitoring": True,
                "advanced_features": False,
                "monitoring_interval": 7200  # 2 hours
            }
        }
