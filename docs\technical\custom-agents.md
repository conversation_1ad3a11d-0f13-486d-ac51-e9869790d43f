# Custom Agents Development Guide

This guide provides comprehensive instructions for creating custom AI agents in Aetherforge, from basic implementations to advanced specialized agents.

## 🎯 Agent Architecture

### Agent Lifecycle

```mermaid
graph LR
    A[Initialize] --> B[Validate Input]
    B --> C[Execute Task]
    C --> D[Generate Output]
    D --> E[Update Context]
    E --> F[Cleanup]
    
    B --> G[Validation Failed]
    C --> H[Execution Failed]
    G --> I[Error Handling]
    H --> I
    I --> F
```

### Agent Types and Roles

| Agent Type | Purpose | Example Use Cases |
|------------|---------|-------------------|
| **Analyst** | Requirements analysis, research | Business analysis, market research |
| **Architect** | System design, technology selection | Architecture design, tech stack selection |
| **Developer** | Code implementation | Frontend, backend, full-stack development |
| **QA** | Testing, quality assurance | Test generation, code review |
| **Specialist** | Domain-specific expertise | Security, performance, accessibility |
| **Custom** | User-defined functionality | Industry-specific, proprietary tools |

## 🛠️ Creating Your First Custom Agent

### Step 1: Define Agent Specification

```python
# agents/specifications/my_agent_spec.py
from dataclasses import dataclass
from typing import List, Dict, Any
from enum import Enum

class AgentComplexity(Enum):
    SIMPLE = "simple"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

@dataclass
class CustomAgentSpec:
    """Specification for custom agent creation"""
    name: str
    description: str
    agent_type: str
    complexity: AgentComplexity
    required_capabilities: List[str]
    optional_capabilities: List[str]
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    dependencies: List[str]
    tools: List[str]
    performance_targets: Dict[str, float]
```

### Step 2: Implement Agent Class

```python
# agents/custom/my_custom_agent.py
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from extensions.agents.base_agent import BaseAgent, AgentType, AgentCapability
from agents.specifications.my_agent_spec import CustomAgentSpec

logger = logging.getLogger(__name__)

class MyCustomAgent(BaseAgent):
    """Example custom agent implementation"""
    
    def __init__(self, spec: CustomAgentSpec):
        super().__init__(
            agent_id=f"custom_{spec.name.lower().replace(' ', '_')}",
            name=spec.name,
            agent_type=AgentType.CUSTOM
        )
        
        self.spec = spec
        self.performance_metrics = {}
        self.execution_history = []
        
        # Initialize capabilities from spec
        self._initialize_capabilities()
    
    def _initialize_capabilities(self):
        """Initialize agent capabilities from specification"""
        for capability_name in self.spec.required_capabilities:
            capability = AgentCapability(
                name=capability_name,
                description=f"Required capability: {capability_name}",
                required_tools=self.spec.tools
            )
            self.capabilities.append(capability)
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task with comprehensive error handling and logging"""
        start_time = datetime.now()
        task_id = task.get("id", f"task_{start_time.timestamp()}")
        
        logger.info(f"Agent {self.name} starting task {task_id}")
        
        try:
            # Validate input
            if not await self.validate_input(task):
                raise ValueError("Task validation failed")
            
            # Pre-execution setup
            await self._pre_execution_setup(task)
            
            # Execute main task logic
            result = await self._execute_main_logic(task)
            
            # Post-execution processing
            result = await self._post_execution_processing(result, task)
            
            # Record successful execution
            execution_time = (datetime.now() - start_time).total_seconds()
            self._record_execution(task_id, "success", execution_time)
            
            logger.info(f"Agent {self.name} completed task {task_id} in {execution_time:.2f}s")
            
            return {
                "status": "success",
                "task_id": task_id,
                "agent_id": self.agent_id,
                "execution_time": execution_time,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self._record_execution(task_id, "failed", execution_time, str(e))
            
            logger.error(f"Agent {self.name} failed task {task_id}: {e}")
            
            return {
                "status": "failed",
                "task_id": task_id,
                "agent_id": self.agent_id,
                "execution_time": execution_time,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_main_logic(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Main task execution logic - override in subclasses"""
        task_type = task.get("type", "default")
        
        if task_type == "analyze":
            return await self._perform_analysis(task)
        elif task_type == "generate":
            return await self._perform_generation(task)
        elif task_type == "validate":
            return await self._perform_validation(task)
        else:
            raise NotImplementedError(f"Task type '{task_type}' not implemented")
    
    async def _perform_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform analysis task"""
        data = task.get("data", {})
        analysis_type = task.get("analysis_type", "general")
        
        # Implement your analysis logic here
        result = {
            "analysis_type": analysis_type,
            "findings": [],
            "recommendations": [],
            "confidence": 0.0
        }
        
        # Example analysis logic
        if analysis_type == "code_quality":
            result = await self._analyze_code_quality(data)
        elif analysis_type == "performance":
            result = await self._analyze_performance(data)
        
        return result
    
    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input against agent specification"""
        try:
            # Check required fields from input schema
            required_fields = self.spec.input_schema.get("required", [])
            for field in required_fields:
                if field not in input_data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Validate field types
            properties = self.spec.input_schema.get("properties", {})
            for field, value in input_data.items():
                if field in properties:
                    expected_type = properties[field].get("type")
                    if not self._validate_field_type(value, expected_type):
                        logger.error(f"Invalid type for field {field}: expected {expected_type}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Input validation error: {e}")
            return False
    
    def _validate_field_type(self, value: Any, expected_type: str) -> bool:
        """Validate field type"""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True  # Unknown type, assume valid
    
    async def _pre_execution_setup(self, task: Dict[str, Any]) -> None:
        """Setup before task execution"""
        # Initialize tools, load models, setup connections, etc.
        pass
    
    async def _post_execution_processing(self, result: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
        """Process results after execution"""
        # Add metadata, format output, trigger notifications, etc.
        result["agent_metadata"] = {
            "agent_id": self.agent_id,
            "agent_name": self.name,
            "capabilities_used": [cap.name for cap in self.capabilities],
            "performance_metrics": self.performance_metrics
        }
        
        return result
    
    def _record_execution(self, task_id: str, status: str, execution_time: float, error: str = None):
        """Record execution metrics"""
        execution_record = {
            "task_id": task_id,
            "status": status,
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        if error:
            execution_record["error"] = error
        
        self.execution_history.append(execution_record)
        
        # Update performance metrics
        self.performance_metrics["total_executions"] = len(self.execution_history)
        self.performance_metrics["success_rate"] = len([r for r in self.execution_history if r["status"] == "success"]) / len(self.execution_history)
        self.performance_metrics["avg_execution_time"] = sum(r["execution_time"] for r in self.execution_history) / len(self.execution_history)
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        return {
            "agent_id": self.agent_id,
            "agent_name": self.name,
            "metrics": self.performance_metrics,
            "execution_history": self.execution_history[-10:],  # Last 10 executions
            "capabilities": [cap.name for cap in self.capabilities]
        }
```

### Step 3: Register Your Agent

```python
# agents/registry.py
from typing import Dict, Type
from extensions.agents.base_agent import BaseAgent
from agents.custom.my_custom_agent import MyCustomAgent

class AgentRegistry:
    """Registry for all available agents"""
    
    _agents: Dict[str, Type[BaseAgent]] = {}
    
    @classmethod
    def register_agent(cls, agent_class: Type[BaseAgent], agent_id: str = None):
        """Register a new agent class"""
        if agent_id is None:
            agent_id = agent_class.__name__.lower()
        
        cls._agents[agent_id] = agent_class
    
    @classmethod
    def get_agent(cls, agent_id: str) -> Type[BaseAgent]:
        """Get agent class by ID"""
        return cls._agents.get(agent_id)
    
    @classmethod
    def list_agents(cls) -> List[str]:
        """List all registered agent IDs"""
        return list(cls._agents.keys())

# Register your custom agent
AgentRegistry.register_agent(MyCustomAgent, "my_custom_agent")
```

## 🔧 Advanced Agent Features

### Tool Integration

```python
class ToolIntegratedAgent(BaseAgent):
    """Agent with integrated tools"""
    
    def __init__(self):
        super().__init__("tool_agent", "Tool Integrated Agent", AgentType.SPECIALIST)
        self.tools = {
            "code_analyzer": CodeAnalyzerTool(),
            "file_generator": FileGeneratorTool(),
            "api_client": APIClientTool()
        }
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task using available tools"""
        tool_name = task.get("tool")
        if tool_name not in self.tools:
            raise ValueError(f"Tool '{tool_name}' not available")
        
        tool = self.tools[tool_name]
        return await tool.execute(task.get("tool_params", {}))
```

### Context Management

```python
class ContextAwareAgent(BaseAgent):
    """Agent with advanced context management"""
    
    def __init__(self):
        super().__init__("context_agent", "Context Aware Agent", AgentType.SPECIALIST)
        self.context_store = {}
        self.context_history = []
    
    async def update_context(self, key: str, value: Any, persistent: bool = False):
        """Update agent context"""
        self.context_store[key] = value
        
        if persistent:
            # Save to persistent storage
            await self._save_context_to_storage(key, value)
        
        # Track context changes
        self.context_history.append({
            "key": key,
            "timestamp": datetime.now().isoformat(),
            "persistent": persistent
        })
    
    async def get_context(self, key: str, default: Any = None) -> Any:
        """Get context value"""
        return self.context_store.get(key, default)
```

## 📊 Agent Testing

### Unit Testing

```python
# tests/test_custom_agent.py
import pytest
import asyncio
from agents.custom.my_custom_agent import MyCustomAgent
from agents.specifications.my_agent_spec import CustomAgentSpec, AgentComplexity

@pytest.fixture
def agent_spec():
    return CustomAgentSpec(
        name="Test Agent",
        description="Agent for testing",
        agent_type="custom",
        complexity=AgentComplexity.SIMPLE,
        required_capabilities=["analysis"],
        optional_capabilities=[],
        input_schema={
            "required": ["data"],
            "properties": {
                "data": {"type": "object"},
                "type": {"type": "string"}
            }
        },
        output_schema={},
        dependencies=[],
        tools=[],
        performance_targets={"accuracy": 0.9}
    )

@pytest.fixture
def custom_agent(agent_spec):
    return MyCustomAgent(agent_spec)

@pytest.mark.asyncio
async def test_agent_initialization(custom_agent):
    """Test agent initialization"""
    assert custom_agent.name == "Test Agent"
    assert custom_agent.agent_type.value == "custom"
    assert len(custom_agent.capabilities) > 0

@pytest.mark.asyncio
async def test_task_execution(custom_agent):
    """Test task execution"""
    task = {
        "id": "test_task",
        "type": "analyze",
        "data": {"sample": "data"}
    }
    
    result = await custom_agent.execute_task(task)
    
    assert result["status"] == "success"
    assert "result" in result
    assert result["agent_id"] == custom_agent.agent_id

@pytest.mark.asyncio
async def test_input_validation(custom_agent):
    """Test input validation"""
    valid_input = {"data": {"test": "value"}, "type": "analyze"}
    invalid_input = {"type": "analyze"}  # Missing required 'data' field
    
    assert await custom_agent.validate_input(valid_input) == True
    assert await custom_agent.validate_input(invalid_input) == False
```

### Integration Testing

```python
# tests/test_agent_integration.py
import pytest
from orchestrator import Orchestrator
from agents.registry import AgentRegistry

@pytest.mark.asyncio
async def test_agent_orchestrator_integration():
    """Test agent integration with orchestrator"""
    orchestrator = Orchestrator()
    
    # Register custom agent
    agent_id = "my_custom_agent"
    agent_class = AgentRegistry.get_agent(agent_id)
    
    # Create project with custom agent
    project_config = {
        "name": "Test Project",
        "agents": [agent_id],
        "workflow": "custom_workflow"
    }
    
    project = await orchestrator.create_project(project_config)
    assert project.status == "created"
    assert agent_id in project.assigned_agents
```

## 📚 Best Practices

### 1. Error Handling

```python
async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Task execution logic
        result = await self._execute_logic(task)
        return {"status": "success", "result": result}
    except ValidationError as e:
        return {"status": "validation_error", "error": str(e)}
    except TimeoutError as e:
        return {"status": "timeout", "error": str(e)}
    except Exception as e:
        logger.exception(f"Unexpected error in {self.name}")
        return {"status": "error", "error": str(e)}
```

### 2. Performance Monitoring

```python
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        start_time = time.time()
        try:
            result = await func(self, *args, **kwargs)
            execution_time = time.time() - start_time
            self._record_performance_metric(func.__name__, execution_time, "success")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_performance_metric(func.__name__, execution_time, "error")
            raise
    return wrapper
```

### 3. Resource Management

```python
class ResourceManagedAgent(BaseAgent):
    async def __aenter__(self):
        # Initialize resources (connections, models, etc.)
        await self._initialize_resources()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Cleanup resources
        await self._cleanup_resources()
    
    async def _initialize_resources(self):
        # Resource initialization logic
        pass
    
    async def _cleanup_resources(self):
        # Resource cleanup logic
        pass
```

## 🔗 Next Steps

- **[Plugin Development](plugin-development.md)** - Create agent plugins
- **[Integration Guide](integration-guide.md)** - Integrate with external systems
- **[Component Adapters](component-adapters.md)** - Service integration patterns
- **[API Extensions](api-extensions.md)** - Extend the REST API

## 📖 Resources

- [Agent Examples Repository](https://github.com/aetherforge/agent-examples)
- [Community Agents](https://github.com/aetherforge/community-agents)
- [Agent Development Best Practices](https://docs.aetherforge.dev/best-practices/agents)
