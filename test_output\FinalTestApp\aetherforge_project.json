{"project_id": "proj_1750437990", "name": "FinalTestApp", "type": "fullstack", "status": "failed", "created_at": "2025-06-20T11:46:30.835161", "completed_at": null, "generator": "Aetherforge Standalone", "version": "1.0.0", "files_generated": ["docs/requirements.md", "docs/api_documentation.md", "src/App.tsx", "src/index.tsx", "src/components/Header.tsx", "src/pages/Home.tsx", "src/App.css", "server/index.js", ".giti<PERSON>re", ".env.example", "Dockerfile", "docker-compose.yml", "package.json", "tsconfig.json"], "phases_completed": 5, "total_phases": 6}