#!/usr/bin/env python3
"""
Example demonstrating comprehensive API resilience integration in Aetherforge
Shows how to use fallback mechanisms, retry logic, and quota management
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from api_resilience import (
    APIResilienceLayer, RetryConfig, FallbackConfig, 
    APIProvider, get_resilience_layer
)
from api_manager import APIManager

# Configure logging to see resilience events
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def basic_resilience_example():
    """Basic example of using the resilience layer"""
    print("🔧 Basic Resilience Example")
    print("=" * 50)
    
    # Get the global resilience layer
    resilience_layer = get_resilience_layer()
    
    # Simple API call with automatic resilience
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Explain API resilience in one sentence."}
    ]
    
    result = await resilience_layer.generate_text_resilient(messages, max_tokens=100)
    
    if result.success:
        print(f"✅ Success!")
        print(f"Response: {result.response}")
        print(f"Provider: {result.provider_used.value if result.provider_used else 'Cache'}")
        print(f"Fallback used: {result.fallback_used}")
        print(f"Retries: {result.retries_attempted}")
        print(f"Time: {result.total_time:.2f}s")
    else:
        print(f"❌ Failed: {result.error}")

async def custom_configuration_example():
    """Example with custom retry and fallback configuration"""
    print("\n🎛️  Custom Configuration Example")
    print("=" * 50)
    
    # Custom retry configuration - more aggressive retries
    retry_config = RetryConfig(
        max_retries=5,
        base_delay=0.5,
        max_delay=30.0,
        retry_on_quota=True,  # Retry even on quota errors
        retry_on_rate_limit=True
    )
    
    # Custom fallback configuration
    fallback_config = FallbackConfig(
        enable_provider_fallback=True,
        enable_model_fallback=True,
        enable_degraded_service=True,
        enable_cache_fallback=True,
        fallback_providers=[APIProvider.OPENAI, APIProvider.ANTHROPIC, APIProvider.LOCAL],
        fallback_models={
            APIProvider.OPENAI: ["gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
            APIProvider.ANTHROPIC: ["claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
            APIProvider.LOCAL: ["llama2", "codellama"]
        }
    )
    
    # Create resilience layer with custom config
    api_manager = APIManager()
    custom_resilience = APIResilienceLayer(
        api_manager=api_manager,
        retry_config=retry_config,
        fallback_config=fallback_config
    )
    
    messages = [
        {"role": "user", "content": "What are the benefits of API fallback mechanisms?"}
    ]
    
    result = await custom_resilience.generate_text_resilient(messages, max_tokens=150)
    
    if result.success:
        print(f"✅ Custom config success!")
        print(f"Response: {result.response[:100]}...")
        print(f"Configuration worked with {result.retries_attempted} retries")
    else:
        print(f"❌ Failed even with custom config: {result.error}")

async def quota_monitoring_example():
    """Example of quota monitoring and warnings"""
    print("\n💰 Quota Monitoring Example")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    
    # Make several API calls to generate usage data
    for i in range(3):
        messages = [{"role": "user", "content": f"Test message {i+1}"}]
        result = await resilience_layer.generate_text_resilient(messages, max_tokens=20)
        
        if result.success:
            print(f"Call {i+1}: ✅ Success")
        else:
            print(f"Call {i+1}: ❌ Failed")
    
    # Check quota usage
    stats = resilience_layer.get_resilience_stats()
    print("\nQuota Usage Summary:")
    for provider, usage in stats["quota_usage"].items():
        if usage["requests"] > 0:
            print(f"  {provider}:")
            print(f"    Requests: {usage['requests']}")
            print(f"    Tokens: {usage['tokens']}")
            print(f"    Estimated Cost: ${usage['cost']:.4f}")
            
            if usage["warnings"]:
                for warning in usage["warnings"]:
                    print(f"    ⚠️  {warning}")

async def notification_monitoring_example():
    """Example of monitoring resilience notifications"""
    print("\n🔔 Notification Monitoring Example")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    
    # Make some API calls that might generate notifications
    test_cases = [
        "What is machine learning?",
        "Explain quantum computing briefly.",
        "What are the benefits of cloud computing?"
    ]
    
    for i, prompt in enumerate(test_cases):
        messages = [{"role": "user", "content": prompt}]
        result = await resilience_layer.generate_text_resilient(messages, max_tokens=50)
        print(f"Test {i+1}: {'✅' if result.success else '❌'}")
    
    # Check notifications
    notifications = resilience_layer.get_user_notifications(10)
    
    if notifications:
        print(f"\nRecent Notifications ({len(notifications)}):")
        for notification in notifications[-5:]:  # Show last 5
            severity_emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(notification["severity"], "📝")
            provider_info = f" ({notification['provider']})" if notification['provider'] else ""
            print(f"  {severity_emoji} [{notification['type']}]{provider_info}")
            print(f"     {notification['message']}")
    else:
        print("\nNo recent notifications.")

async def provider_health_check_example():
    """Example of checking provider health"""
    print("\n🏥 Provider Health Check Example")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    
    # Test all configured providers
    health_results = await resilience_layer.test_all_providers()
    
    print("Provider Health Status:")
    for provider, result in health_results.items():
        if result["status"] == "healthy":
            print(f"  ✅ {provider.upper()}")
            print(f"     Model: {result['model']}")
            print(f"     Response Time: {result['response_time']:.2f}s")
            if result.get('response_preview'):
                print(f"     Preview: {result['response_preview'][:50]}...")
        else:
            print(f"  ❌ {provider.upper()}")
            print(f"     Error: {result['error']}")
            print(f"     Model: {result['model']}")

async def cache_demonstration_example():
    """Example demonstrating cache functionality"""
    print("\n💾 Cache Demonstration Example")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    
    # Same message to demonstrate caching
    messages = [{"role": "user", "content": "What is the capital of France?"}]
    
    print("First call (should hit API):")
    result1 = await resilience_layer.generate_text_resilient(messages, max_tokens=30)
    print(f"  Time: {result1.total_time:.2f}s")
    print(f"  Provider: {result1.provider_used.value if result1.provider_used else 'Cache'}")
    print(f"  Fallback: {result1.fallback_used}")
    
    print("\nSecond call (should use cache):")
    result2 = await resilience_layer.generate_text_resilient(messages, max_tokens=30)
    print(f"  Time: {result2.total_time:.2f}s")
    print(f"  Provider: {result2.provider_used.value if result2.provider_used else 'Cache'}")
    print(f"  Fallback: {result2.fallback_used}")
    
    if result2.total_time < result1.total_time and result2.provider_used is None:
        print("  ✅ Cache hit detected!")
    else:
        print("  ℹ️  Cache may not have been used (could be expected)")

async def comprehensive_stats_example():
    """Example showing comprehensive resilience statistics"""
    print("\n📊 Comprehensive Statistics Example")
    print("=" * 50)
    
    resilience_layer = get_resilience_layer()
    stats = resilience_layer.get_resilience_stats()
    
    print("🔧 Provider Status:")
    for provider, status in stats["provider_status"].items():
        configured = "✅" if status["configured"] else "❌"
        available = "✅" if status["can_make_request"] else "⏳"
        print(f"  {provider}: {configured} Configured | {available} Available")
        print(f"    Model: {status['model']}")
        print(f"    Rate Limit Remaining: {status['rate_limit_remaining']}")
    
    print(f"\n💾 Cache: {stats['cache_stats']['entries']} entries")
    
    print("\n🔄 Fallback Configuration:")
    config = stats["fallback_config"]
    print(f"  Provider Fallback: {'✅' if config['provider_fallback'] else '❌'}")
    print(f"  Model Fallback: {'✅' if config['model_fallback'] else '❌'}")
    print(f"  Degraded Service: {'✅' if config['degraded_service'] else '❌'}")
    print(f"  Cache Fallback: {'✅' if config['cache_fallback'] else '❌'}")
    
    print(f"\n🔔 Notifications Summary:")
    summary = stats["notification_summary"]
    print(f"  Total: {summary['total']}")
    print(f"  Errors: {summary['errors']} | Warnings: {summary['warnings']} | Info: {summary['info']}")

async def main():
    """Run all examples"""
    print("🚀 Aetherforge API Resilience Integration Examples")
    print("=" * 60)
    
    try:
        await basic_resilience_example()
        await custom_configuration_example()
        await quota_monitoring_example()
        await notification_monitoring_example()
        await provider_health_check_example()
        await cache_demonstration_example()
        await comprehensive_stats_example()
        
        print("\n✅ All examples completed successfully!")
        print("\nNext steps:")
        print("1. Configure your API keys using the API manager")
        print("2. Customize retry and fallback configurations for your needs")
        print("3. Monitor quota usage and notifications in production")
        print("4. Use the resilience CLI tool for ongoing monitoring")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        print("Make sure you have API keys configured and the resilience system set up.")

if __name__ == "__main__":
    asyncio.run(main())
