#!/usr/bin/env node
/**
 * Test script for Aetherforge VS Code Extension
 * Validates all functionality including project creation, agent interaction, and workflow visualization
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 AETHERFORGE VS CODE EXTENSION VALIDATION');
console.log('============================================================');

/**
 * Test extension.js file structure and functionality
 */
function testExtensionStructure() {
    console.log('\n📁 Testing Extension Structure...');
    
    const extensionPath = path.join('vscode-extension', 'src', 'extension.js');
    
    if (!fs.existsSync(extensionPath)) {
        console.log('❌ extension.js file not found');
        return false;
    }
    
    const extensionContent = fs.readFileSync(extensionPath, 'utf8');
    
    // Test for required functions
    const requiredFunctions = [
        'activate',
        'deactivate',
        'showProjectCreationPanel',
        'showQuickCreateDialog',
        'showAgentInteractionPanel',
        'showWorkflowVisualizationPanel',
        'handleProjectCreation',
        'sendMessageToAgent',
        'executeAgentTask',
        'updateWorkflowVisualizations',
        'getProjectCreationHTML',
        'getAgentInteractionHTML',
        'getWorkflowVisualizationHTML'
    ];
    
    let missingFunctions = [];
    requiredFunctions.forEach(func => {
        if (!extensionContent.includes(`function ${func}`) && !extensionContent.includes(`${func}(`)) {
            missingFunctions.push(func);
        }
    });
    
    if (missingFunctions.length > 0) {
        console.log(`❌ Missing functions: ${missingFunctions.join(', ')}`);
        return false;
    }
    
    console.log('✅ All required functions present');
    
    // Test for required commands
    const requiredCommands = [
        'aetherforge.createProject',
        'aetherforge.quickCreate',
        'aetherforge.showAgentPanel',
        'aetherforge.showWorkflow',
        'aetherforge.viewProjectStatus',
        'aetherforge.showPheromoneTrail'
    ];
    
    let missingCommands = [];
    requiredCommands.forEach(cmd => {
        if (!extensionContent.includes(`'${cmd}'`) && !extensionContent.includes(`"${cmd}"`)) {
            missingCommands.push(cmd);
        }
    });
    
    if (missingCommands.length > 0) {
        console.log(`❌ Missing commands: ${missingCommands.join(', ')}`);
        return false;
    }
    
    console.log('✅ All required commands present');
    
    // Test for WebSocket integration
    if (!extensionContent.includes('WebSocket') || !extensionContent.includes('connectToPheromoneWebSocket')) {
        console.log('❌ WebSocket integration missing');
        return false;
    }
    
    console.log('✅ WebSocket integration present');
    
    // Test for HTML generation
    const htmlFunctions = [
        'getProjectCreationHTML',
        'getAgentInteractionHTML',
        'getWorkflowVisualizationHTML'
    ];
    
    htmlFunctions.forEach(func => {
        if (!extensionContent.includes(func)) {
            console.log(`❌ Missing HTML function: ${func}`);
            return false;
        }
    });
    
    console.log('✅ All HTML generation functions present');
    
    return true;
}

/**
 * Test package.json configuration
 */
function testPackageJson() {
    console.log('\n📦 Testing package.json Configuration...');
    
    const packagePath = path.join('vscode-extension', 'package.json');
    
    if (!fs.existsSync(packagePath)) {
        console.log('❌ package.json not found');
        return false;
    }
    
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Test commands
    const commands = packageContent.contributes?.commands || [];
    const requiredCommands = [
        'aetherforge.createProject',
        'aetherforge.quickCreate',
        'aetherforge.showAgentPanel',
        'aetherforge.showWorkflow'
    ];
    
    const commandIds = commands.map(cmd => cmd.command);
    const missingCommands = requiredCommands.filter(cmd => !commandIds.includes(cmd));
    
    if (missingCommands.length > 0) {
        console.log(`❌ Missing commands in package.json: ${missingCommands.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${commands.length} commands configured`);
    
    // Test configuration properties
    const config = packageContent.contributes?.configuration?.properties || {};
    const requiredConfigs = [
        'aetherforge.orchestratorUrl',
        'aetherforge.autoOpenProjects',
        'aetherforge.defaultProjectType',
        'aetherforge.enableRealTimeUpdates'
    ];
    
    const missingConfigs = requiredConfigs.filter(cfg => !config[cfg]);
    
    if (missingConfigs.length > 0) {
        console.log(`❌ Missing configuration properties: ${missingConfigs.join(', ')}`);
        return false;
    }
    
    console.log(`✅ All ${Object.keys(config).length} configuration properties present`);
    
    // Test dependencies
    const dependencies = packageContent.dependencies || {};
    const requiredDeps = ['axios', 'ws'];
    
    const missingDeps = requiredDeps.filter(dep => !dependencies[dep]);
    
    if (missingDeps.length > 0) {
        console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
        return false;
    }
    
    console.log('✅ All required dependencies present');
    
    // Test keybindings
    const keybindings = packageContent.contributes?.keybindings || [];
    if (keybindings.length === 0) {
        console.log('⚠️  No keybindings configured');
    } else {
        console.log(`✅ ${keybindings.length} keybindings configured`);
    }
    
    return true;
}

/**
 * Test HTML content generation
 */
function testHTMLGeneration() {
    console.log('\n🌐 Testing HTML Generation...');
    
    const extensionPath = path.join('vscode-extension', 'src', 'extension.js');
    const extensionContent = fs.readFileSync(extensionPath, 'utf8');
    
    // Test for HTML structure
    const htmlTests = [
        { name: 'Project Creation Form', pattern: /textarea.*id="prompt"/ },
        { name: 'Project Type Selection', pattern: /select.*id="projectType"/ },
        { name: 'Agent Chat Interface', pattern: /class="chat-messages"/ },
        { name: 'Workflow Visualization', pattern: /class="workflow-diagram"/ },
        { name: 'CSS Styling', pattern: /var\(--vscode-/ },
        { name: 'JavaScript Interaction', pattern: /vscode\.postMessage/ }
    ];
    
    let passedTests = 0;
    htmlTests.forEach(test => {
        if (test.pattern.test(extensionContent)) {
            console.log(`✅ ${test.name} - Present`);
            passedTests++;
        } else {
            console.log(`❌ ${test.name} - Missing`);
        }
    });
    
    console.log(`\n📊 HTML Generation: ${passedTests}/${htmlTests.length} tests passed`);
    
    return passedTests === htmlTests.length;
}

/**
 * Test API integration points
 */
function testAPIIntegration() {
    console.log('\n🔌 Testing API Integration...');
    
    const extensionPath = path.join('vscode-extension', 'src', 'extension.js');
    const extensionContent = fs.readFileSync(extensionPath, 'utf8');
    
    const apiTests = [
        { name: 'Project Creation API', pattern: /axios\.post.*\/projects/ },
        { name: 'Agent List API', pattern: /axios\.get.*\/agents/ },
        { name: 'Workflow API', pattern: /axios\.get.*\/workflows/ },
        { name: 'Health Check API', pattern: /axios\.get.*\/health/ },
        { name: 'WebSocket Connection', pattern: /new WebSocket/ },
        { name: 'Error Handling', pattern: /catch.*error/ }
    ];
    
    let passedTests = 0;
    apiTests.forEach(test => {
        if (test.pattern.test(extensionContent)) {
            console.log(`✅ ${test.name} - Implemented`);
            passedTests++;
        } else {
            console.log(`❌ ${test.name} - Missing`);
        }
    });
    
    console.log(`\n📊 API Integration: ${passedTests}/${apiTests.length} tests passed`);
    
    return passedTests === apiTests.length;
}

/**
 * Main test execution
 */
async function runTests() {
    console.log('🚀 Starting VS Code Extension Validation...\n');
    
    const tests = [
        { name: 'Extension Structure', test: testExtensionStructure },
        { name: 'Package Configuration', test: testPackageJson },
        { name: 'HTML Generation', test: testHTMLGeneration },
        { name: 'API Integration', test: testAPIIntegration }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            const result = test.test();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ ${test.name} failed with error: ${error.message}`);
        }
    }
    
    console.log('\n============================================================');
    console.log('🎯 VALIDATION SUMMARY');
    console.log('============================================================');
    console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`📊 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL TESTS PASSED!');
        console.log('✅ VS Code Extension is fully implemented and ready for use');
        console.log('\n📋 Extension Features:');
        console.log('   • Project Creation with comprehensive UI');
        console.log('   • Agent Interaction and Chat Interface');
        console.log('   • Workflow Visualization with real-time updates');
        console.log('   • System Status and Monitoring');
        console.log('   • Pheromone Trail Visualization');
        console.log('   • WebSocket Integration for real-time updates');
        console.log('   • Comprehensive Configuration Options');
        console.log('   • Keyboard Shortcuts and Menu Integration');
        
        return true;
    } else {
        console.log('\n⚠️  Some tests failed. Please review the issues above.');
        return false;
    }
}

// Run the tests
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runTests };
