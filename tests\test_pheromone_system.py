"""
Comprehensive test suite for the Pheromone System
Tests all aspects of pheromone-based agent coordination
"""

import pytest
import asyncio
import json
import tempfile
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# Import the pheromone system components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from pheromone_system import PheromoneSystem, Pheromone, PheromoneTrail
from pheromone_bus import EnhancedPheromonebus


class TestPheromone:
    """Test the basic Pheromone class"""
    
    def test_pheromone_creation(self):
        """Test creating a pheromone with all properties"""
        pheromone = Pheromone(
            signal="test_signal",
            payload={"key": "value"},
            project_id="test_project",
            agent_id="test_agent",
            intensity=0.8,
            decay_rate=0.1
        )
        
        assert pheromone.signal == "test_signal"
        assert pheromone.payload == {"key": "value"}
        assert pheromone.project_id == "test_project"
        assert pheromone.agent_id == "test_agent"
        assert pheromone.intensity == 0.8
        assert pheromone.decay_rate == 0.1
        assert isinstance(pheromone.timestamp, datetime)
        assert isinstance(pheromone.id, str)
    
    def test_pheromone_serialization(self):
        """Test pheromone serialization to dict"""
        pheromone = Pheromone(
            signal="test_signal",
            payload={"data": [1, 2, 3]},
            project_id="test_project"
        )
        
        data = pheromone.to_dict()
        
        assert data["signal"] == "test_signal"
        assert data["payload"] == {"data": [1, 2, 3]}
        assert data["project_id"] == "test_project"
        assert "timestamp" in data
        assert "id" in data
    
    def test_pheromone_from_dict(self):
        """Test creating pheromone from dictionary"""
        data = {
            "signal": "test_signal",
            "payload": {"key": "value"},
            "project_id": "test_project",
            "agent_id": "test_agent",
            "intensity": 0.7,
            "decay_rate": 0.05,
            "timestamp": "2023-01-01T12:00:00",
            "id": "test_id"
        }
        
        pheromone = Pheromone.from_dict(data)
        
        assert pheromone.signal == "test_signal"
        assert pheromone.payload == {"key": "value"}
        assert pheromone.project_id == "test_project"
        assert pheromone.agent_id == "test_agent"
        assert pheromone.intensity == 0.7
        assert pheromone.decay_rate == 0.05
        assert pheromone.id == "test_id"
    
    def test_pheromone_decay(self):
        """Test pheromone intensity decay over time"""
        pheromone = Pheromone(
            signal="test_signal",
            payload={},
            project_id="test_project",
            intensity=1.0,
            decay_rate=0.1
        )
        
        # Simulate time passage
        original_intensity = pheromone.intensity
        
        # Mock timestamp to be 10 seconds ago
        past_time = datetime.now() - timedelta(seconds=10)
        pheromone.timestamp = past_time
        
        current_intensity = pheromone.get_current_intensity()
        
        # Intensity should have decayed
        assert current_intensity < original_intensity
        assert current_intensity > 0  # Should not be completely gone yet
    
    def test_pheromone_expiry(self):
        """Test pheromone expiry logic"""
        pheromone = Pheromone(
            signal="test_signal",
            payload={},
            project_id="test_project",
            intensity=1.0,
            decay_rate=0.5  # High decay rate
        )
        
        # Mock timestamp to be far in the past
        past_time = datetime.now() - timedelta(hours=1)
        pheromone.timestamp = past_time
        
        assert pheromone.is_expired()
        assert pheromone.get_current_intensity() <= 0.01  # Nearly zero


class TestPheromoneTrail:
    """Test the PheromoneTrail class"""
    
    def test_trail_creation(self):
        """Test creating a pheromone trail"""
        trail = PheromoneTrail(project_id="test_project")
        
        assert trail.project_id == "test_project"
        assert len(trail.pheromones) == 0
        assert isinstance(trail.created_at, datetime)
    
    def test_add_pheromone_to_trail(self):
        """Test adding pheromones to a trail"""
        trail = PheromoneTrail(project_id="test_project")
        
        pheromone1 = Pheromone("signal1", {}, "test_project")
        pheromone2 = Pheromone("signal2", {}, "test_project")
        
        trail.add_pheromone(pheromone1)
        trail.add_pheromone(pheromone2)
        
        assert len(trail.pheromones) == 2
        assert trail.pheromones[0] == pheromone1
        assert trail.pheromones[1] == pheromone2
    
    def test_get_pheromones_by_signal(self):
        """Test filtering pheromones by signal"""
        trail = PheromoneTrail(project_id="test_project")
        
        pheromone1 = Pheromone("important", {}, "test_project")
        pheromone2 = Pheromone("other", {}, "test_project")
        pheromone3 = Pheromone("important", {}, "test_project")
        
        trail.add_pheromone(pheromone1)
        trail.add_pheromone(pheromone2)
        trail.add_pheromone(pheromone3)
        
        important_pheromones = trail.get_pheromones_by_signal("important")
        
        assert len(important_pheromones) == 2
        assert all(p.signal == "important" for p in important_pheromones)
    
    def test_get_recent_pheromones(self):
        """Test getting recent pheromones"""
        trail = PheromoneTrail(project_id="test_project")
        
        # Create pheromones with different timestamps
        old_pheromone = Pheromone("old", {}, "test_project")
        old_pheromone.timestamp = datetime.now() - timedelta(hours=2)
        
        recent_pheromone = Pheromone("recent", {}, "test_project")
        recent_pheromone.timestamp = datetime.now() - timedelta(minutes=5)
        
        trail.add_pheromone(old_pheromone)
        trail.add_pheromone(recent_pheromone)
        
        # Get pheromones from last hour
        recent = trail.get_recent_pheromones(timedelta(hours=1))
        
        assert len(recent) == 1
        assert recent[0].signal == "recent"
    
    def test_cleanup_expired_pheromones(self):
        """Test cleaning up expired pheromones"""
        trail = PheromoneTrail(project_id="test_project")
        
        # Create expired pheromone
        expired_pheromone = Pheromone("expired", {}, "test_project", decay_rate=1.0)
        expired_pheromone.timestamp = datetime.now() - timedelta(hours=1)
        
        # Create active pheromone
        active_pheromone = Pheromone("active", {}, "test_project", decay_rate=0.01)
        
        trail.add_pheromone(expired_pheromone)
        trail.add_pheromone(active_pheromone)
        
        assert len(trail.pheromones) == 2
        
        trail.cleanup_expired()
        
        assert len(trail.pheromones) == 1
        assert trail.pheromones[0].signal == "active"


class TestPheromoneSystem:
    """Test the main PheromoneSystem class"""
    
    @pytest.fixture
    def temp_file(self):
        """Create temporary file for testing"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        temp_file.close()
        yield temp_file.name
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
    
    def test_system_initialization(self, temp_file):
        """Test pheromone system initialization"""
        system = PheromoneSystem(storage_file=temp_file)
        
        assert system.storage_file == temp_file
        assert len(system.trails) == 0
        assert system.is_running is False
    
    def test_drop_pheromone(self, temp_file):
        """Test dropping a pheromone"""
        system = PheromoneSystem(storage_file=temp_file)
        
        pheromone_id = system.drop_pheromone(
            signal="test_signal",
            payload={"key": "value"},
            project_id="test_project",
            agent_id="test_agent"
        )
        
        assert pheromone_id is not None
        assert "test_project" in system.trails
        assert len(system.trails["test_project"].pheromones) == 1
        
        pheromone = system.trails["test_project"].pheromones[0]
        assert pheromone.signal == "test_signal"
        assert pheromone.payload == {"key": "value"}
        assert pheromone.agent_id == "test_agent"
    
    def test_get_pheromones_by_project(self, temp_file):
        """Test getting pheromones for a specific project"""
        system = PheromoneSystem(storage_file=temp_file)
        
        # Add pheromones for different projects
        system.drop_pheromone("signal1", {}, "project1")
        system.drop_pheromone("signal2", {}, "project2")
        system.drop_pheromone("signal3", {}, "project1")
        
        project1_pheromones = system.get_pheromones(project_id="project1")
        
        assert len(project1_pheromones) == 2
        assert all(p.project_id == "project1" for p in project1_pheromones)
    
    def test_get_pheromones_by_signal(self, temp_file):
        """Test getting pheromones by signal type"""
        system = PheromoneSystem(storage_file=temp_file)
        
        system.drop_pheromone("important", {"data": 1}, "project1")
        system.drop_pheromone("other", {"data": 2}, "project1")
        system.drop_pheromone("important", {"data": 3}, "project2")
        
        important_pheromones = system.get_pheromones(signal="important")
        
        assert len(important_pheromones) == 2
        assert all(p.signal == "important" for p in important_pheromones)
    
    def test_persistence(self, temp_file):
        """Test pheromone persistence to file"""
        # Create system and add pheromones
        system1 = PheromoneSystem(storage_file=temp_file)
        system1.drop_pheromone("test_signal", {"data": "test"}, "test_project")
        system1.save_to_file()
        
        # Create new system and load from file
        system2 = PheromoneSystem(storage_file=temp_file)
        system2.load_from_file()
        
        assert "test_project" in system2.trails
        assert len(system2.trails["test_project"].pheromones) == 1
        
        pheromone = system2.trails["test_project"].pheromones[0]
        assert pheromone.signal == "test_signal"
        assert pheromone.payload == {"data": "test"}

    def test_statistics(self, temp_file):
        """Test getting system statistics"""
        system = PheromoneSystem(storage_file=temp_file)

        # Add pheromones for multiple projects
        system.drop_pheromone("signal1", {}, "project1")
        system.drop_pheromone("signal2", {}, "project1")
        system.drop_pheromone("signal3", {}, "project2")

        stats = system.get_statistics()

        assert stats["total_pheromones"] == 3
        assert stats["active_projects"] == 2
        assert stats["unique_signals"] == 3
        assert "project1" in stats["projects"]
        assert "project2" in stats["projects"]

    def test_cleanup_expired(self, temp_file):
        """Test cleaning up expired pheromones"""
        system = PheromoneSystem(storage_file=temp_file)

        # Add expired pheromone
        system.drop_pheromone("expired", {}, "project1")
        expired_trail = system.trails["project1"]
        expired_trail.pheromones[0].timestamp = datetime.now() - timedelta(hours=2)
        expired_trail.pheromones[0].decay_rate = 1.0

        # Add active pheromone
        system.drop_pheromone("active", {}, "project1")

        assert len(system.trails["project1"].pheromones) == 2

        system.cleanup_expired()

        assert len(system.trails["project1"].pheromones) == 1
        assert system.trails["project1"].pheromones[0].signal == "active"


class TestEnhancedPheromonebus:
    """Test the enhanced pheromone bus with async operations"""

    @pytest.fixture
    def temp_file(self):
        """Create temporary file for testing"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        temp_file.close()
        yield temp_file.name
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

    @pytest.mark.asyncio
    async def test_bus_initialization(self, temp_file):
        """Test enhanced pheromone bus initialization"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        assert bus.storage_file == temp_file
        assert bus.cleanup_task is None
        assert bus.subscribers == {}

    @pytest.mark.asyncio
    async def test_drop_pheromone_async(self, temp_file):
        """Test dropping pheromones asynchronously"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        pheromone_id = await bus.drop_pheromone(
            signal="async_signal",
            payload={"async": True},
            project_id="async_project"
        )

        assert pheromone_id is not None

        # Verify pheromone was stored
        pheromones = bus.get_pheromones(project_id="async_project")
        assert len(pheromones) == 1
        assert pheromones[0].signal == "async_signal"
        assert pheromones[0].payload["async"] is True

    @pytest.mark.asyncio
    async def test_subscribe_to_signals(self, temp_file):
        """Test subscribing to pheromone signals"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        received_signals = []

        async def signal_handler(pheromone):
            received_signals.append(pheromone)

        # Subscribe to specific signal
        bus.subscribe("test_signal", signal_handler)

        # Drop pheromone that matches subscription
        await bus.drop_pheromone("test_signal", {"data": "test"}, "project1")

        # Drop pheromone that doesn't match
        await bus.drop_pheromone("other_signal", {"data": "other"}, "project1")

        # Allow async processing
        await asyncio.sleep(0.1)

        assert len(received_signals) == 1
        assert received_signals[0].signal == "test_signal"

    @pytest.mark.asyncio
    async def test_cleanup_task(self, temp_file):
        """Test automatic cleanup task"""
        bus = EnhancedPheromonebus(storage_file=temp_file, cleanup_interval=0.1)

        # Start cleanup task
        await bus.start_cleanup_task()

        # Add expired pheromone
        await bus.drop_pheromone("expired", {}, "project1")

        # Manually expire the pheromone
        trail = bus.trails["project1"]
        trail.pheromones[0].timestamp = datetime.now() - timedelta(hours=1)
        trail.pheromones[0].decay_rate = 1.0

        # Add active pheromone
        await bus.drop_pheromone("active", {}, "project1")

        assert len(bus.trails["project1"].pheromones) == 2

        # Wait for cleanup to run
        await asyncio.sleep(0.2)

        # Expired pheromone should be removed
        assert len(bus.trails["project1"].pheromones) == 1
        assert bus.trails["project1"].pheromones[0].signal == "active"

        # Cleanup
        await bus.shutdown()

    @pytest.mark.asyncio
    async def test_project_trails(self, temp_file):
        """Test getting project-specific trails"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        # Add pheromones for different projects
        await bus.drop_pheromone("signal1", {"data": 1}, "project1")
        await bus.drop_pheromone("signal2", {"data": 2}, "project1")
        await bus.drop_pheromone("signal3", {"data": 3}, "project2")

        project1_trails = bus.get_project_trails("project1")
        project2_trails = bus.get_project_trails("project2")

        assert len(project1_trails) == 2
        assert len(project2_trails) == 1
        assert all(p.project_id == "project1" for p in project1_trails)
        assert all(p.project_id == "project2" for p in project2_trails)

    @pytest.mark.asyncio
    async def test_signal_strength_analysis(self, temp_file):
        """Test analyzing signal strength patterns"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        # Add multiple pheromones with different intensities
        await bus.drop_pheromone("strong_signal", {}, "project1", intensity=0.9)
        await bus.drop_pheromone("strong_signal", {}, "project1", intensity=0.8)
        await bus.drop_pheromone("weak_signal", {}, "project1", intensity=0.2)

        analysis = bus.analyze_signal_strength("project1")

        assert "strong_signal" in analysis
        assert "weak_signal" in analysis
        assert analysis["strong_signal"]["count"] == 2
        assert analysis["strong_signal"]["avg_intensity"] > 0.8
        assert analysis["weak_signal"]["count"] == 1
        assert analysis["weak_signal"]["avg_intensity"] == 0.2

    @pytest.mark.asyncio
    async def test_error_handling(self, temp_file):
        """Test error handling in pheromone operations"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        # Test with invalid signal type
        with pytest.raises(ValueError):
            await bus.drop_pheromone("", {}, "project1")

        # Test with invalid project ID
        with pytest.raises(ValueError):
            await bus.drop_pheromone("signal", {}, "")

        # Test with invalid intensity
        with pytest.raises(ValueError):
            await bus.drop_pheromone("signal", {}, "project1", intensity=2.0)

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, temp_file):
        """Test concurrent pheromone operations"""
        bus = EnhancedPheromonebus(storage_file=temp_file)

        # Create multiple concurrent pheromone drops
        tasks = []
        for i in range(10):
            task = bus.drop_pheromone(f"signal_{i}", {"index": i}, "concurrent_project")
            tasks.append(task)

        # Wait for all operations to complete
        results = await asyncio.gather(*tasks)

        # Verify all pheromones were created
        assert len(results) == 10
        assert all(result is not None for result in results)

        # Verify all pheromones are stored
        pheromones = bus.get_pheromones(project_id="concurrent_project")
        assert len(pheromones) == 10

        # Verify unique signals
        signals = {p.signal for p in pheromones}
        assert len(signals) == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
