"""
Test utilities and shared fixtures for TaoForge test suite
Provides common testing infrastructure and helper functions
"""

import pytest
import tempfile
import shutil
import json
import os
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import uuid


class TestDataFactory:
    """Factory for creating test data objects"""
    
    @staticmethod
    def create_project_metadata(
        name="TestProject",
        project_type="fullstack",
        status="in_progress",
        **kwargs
    ):
        """Create project metadata for testing"""
        metadata = {
            "id": str(uuid.uuid4()),
            "name": name,
            "type": project_type,
            "status": status,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "phases": [
                {"name": "requirements_analysis", "status": "completed", "progress": 100},
                {"name": "architecture_design", "status": "completed", "progress": 100},
                {"name": "development", "status": "in_progress", "progress": 60},
                {"name": "quality_assurance", "status": "pending", "progress": 0}
            ],
            "agents": [
                {"role": "analyst", "status": "completed"},
                {"role": "architect", "status": "completed"},
                {"role": "developer", "status": "active"},
                {"role": "qa", "status": "waiting"}
            ],
            "tech_stack": {
                "frontend": "React",
                "backend": "Node.js",
                "database": "PostgreSQL",
                "deployment": "Docker"
            }
        }
        metadata.update(kwargs)
        return metadata
    
    @staticmethod
    def create_pheromone_data(
        signal="test_signal",
        project_id="test_project",
        agent_id="test_agent",
        **kwargs
    ):
        """Create pheromone data for testing"""
        pheromone = {
            "id": str(uuid.uuid4()),
            "signal": signal,
            "payload": kwargs.get("payload", {"test": True}),
            "project_id": project_id,
            "agent_id": agent_id,
            "intensity": kwargs.get("intensity", 1.0),
            "decay_rate": kwargs.get("decay_rate", 0.1),
            "timestamp": datetime.now().isoformat()
        }
        return pheromone
    
    @staticmethod
    def create_agent_context(
        prompt="Test prompt",
        project_path="/tmp/test",
        project_type="fullstack",
        **kwargs
    ):
        """Create agent execution context for testing"""
        context = {
            "prompt": prompt,
            "project_path": project_path,
            "project_type": project_type,
            "project_id": kwargs.get("project_id", "test_project"),
            "phase": kwargs.get("phase", "development"),
            "agent_role": kwargs.get("agent_role", "developer"),
            "workflow": kwargs.get("workflow", "greenfield-fullstack"),
            "tech_stack": kwargs.get("tech_stack", {
                "frontend": "React",
                "backend": "Node.js",
                "database": "PostgreSQL"
            })
        }
        context.update(kwargs)
        return context
    
    @staticmethod
    def create_workflow_config(
        name="test_workflow",
        project_types=None,
        **kwargs
    ):
        """Create workflow configuration for testing"""
        if project_types is None:
            project_types = ["fullstack", "api", "frontend"]
        
        workflow = {
            "name": name,
            "description": kwargs.get("description", "Test workflow"),
            "project_types": project_types,
            "phases": [
                {
                    "name": "requirements_analysis",
                    "description": "Analyze requirements",
                    "agent_role": "analyst",
                    "inputs": ["prompt"],
                    "outputs": ["requirements.md", "user_stories.md"],
                    "estimated_duration": 300
                },
                {
                    "name": "architecture_design",
                    "description": "Design system architecture",
                    "agent_role": "architect",
                    "inputs": ["requirements.md"],
                    "outputs": ["architecture.md", "api_design.md"],
                    "estimated_duration": 600
                },
                {
                    "name": "development",
                    "description": "Implement the system",
                    "agent_role": "developer",
                    "inputs": ["architecture.md"],
                    "outputs": ["source_code", "configuration"],
                    "estimated_duration": 1800
                },
                {
                    "name": "quality_assurance",
                    "description": "Test and validate",
                    "agent_role": "qa",
                    "inputs": ["source_code"],
                    "outputs": ["test_results", "quality_report"],
                    "estimated_duration": 900
                }
            ]
        }
        workflow.update(kwargs)
        return workflow


class MockAPIResponses:
    """Mock API responses for testing"""
    
    @staticmethod
    def successful_project_creation():
        """Mock successful project creation response"""
        return {
            "status": "success",
            "project_id": "proj_" + str(uuid.uuid4())[:8],
            "project_slug": "test-project",
            "message": "Project created successfully",
            "estimated_completion": "2023-01-01T15:00:00Z"
        }
    
    @staticmethod
    def agent_execution_success(agent_role="developer"):
        """Mock successful agent execution response"""
        return {
            "success": True,
            "agent_role": agent_role,
            "outputs": [
                f"{agent_role}_output_1.md",
                f"{agent_role}_output_2.js",
                f"{agent_role}_config.json"
            ],
            "summary": f"{agent_role.title()} phase completed successfully",
            "duration": 120.5,
            "files_created": 15 if agent_role == "developer" else 3
        }
    
    @staticmethod
    def system_health_check():
        """Mock system health check response"""
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "orchestrator": {"status": "healthy", "uptime": 3600},
                "pheromone_system": {"status": "healthy", "active_projects": 5},
                "workflow_engine": {"status": "healthy", "active_workflows": 3},
                "agent_executors": {"status": "healthy", "available_agents": 4}
            }
        }
    
    @staticmethod
    def pheromone_statistics():
        """Mock pheromone system statistics"""
        return {
            "total_pheromones": 1250,
            "active_projects": 8,
            "unique_signals": 25,
            "projects": {
                "project_1": {"pheromone_count": 150, "last_activity": "2023-01-01T12:00:00Z"},
                "project_2": {"pheromone_count": 200, "last_activity": "2023-01-01T11:30:00Z"},
                "project_3": {"pheromone_count": 100, "last_activity": "2023-01-01T11:00:00Z"}
            }
        }


class TestEnvironmentManager:
    """Manages test environment setup and cleanup"""
    
    def __init__(self):
        self.temp_dirs = []
        self.temp_files = []
        self.mock_patches = []
    
    def create_temp_directory(self, prefix="taoforge_test_"):
        """Create a temporary directory for testing"""
        temp_dir = tempfile.mkdtemp(prefix=prefix)
        self.temp_dirs.append(temp_dir)
        return temp_dir
    
    def create_temp_file(self, suffix=".json", content=None):
        """Create a temporary file for testing"""
        temp_file = tempfile.NamedTemporaryFile(
            mode='w', 
            delete=False, 
            suffix=suffix
        )
        
        if content:
            if isinstance(content, dict):
                json.dump(content, temp_file, indent=2)
            else:
                temp_file.write(str(content))
        
        temp_file.close()
        self.temp_files.append(temp_file.name)
        return temp_file.name
    
    def create_project_structure(self, base_path, project_name="TestProject"):
        """Create a complete project directory structure"""
        project_path = Path(base_path) / project_name
        
        # Create directory structure
        directories = [
            "src",
            "docs", 
            "tests",
            "config",
            "scripts",
            "client/src/components",
            "client/src/pages",
            "server/routes",
            "server/models",
            "server/middleware",
            "database/migrations",
            "database/seeds"
        ]
        
        for directory in directories:
            (project_path / directory).mkdir(parents=True, exist_ok=True)
        
        # Create essential files
        files = {
            "README.md": f"# {project_name}\n\nTest project for TaoForge",
            "package.json": json.dumps({
                "name": project_name.lower().replace(" ", "-"),
                "version": "1.0.0",
                "description": "Test project",
                "main": "server/index.js",
                "scripts": {
                    "start": "node server/index.js",
                    "dev": "nodemon server/index.js",
                    "test": "jest"
                }
            }, indent=2),
            ".gitignore": "node_modules/\n.env\n*.log\n",
            ".aetherforge.json": json.dumps(
                TestDataFactory.create_project_metadata(name=project_name),
                indent=2
            ),
            "docs/requirements.md": "# Requirements\n\nTest requirements document",
            "docs/architecture.md": "# Architecture\n\nTest architecture document",
            "server/index.js": "console.log('Test server');",
            "client/src/App.js": "console.log('Test client');"
        }
        
        for file_path, content in files.items():
            full_path = project_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)
        
        return str(project_path)
    
    def add_mock_patch(self, patch_obj):
        """Add a mock patch to be cleaned up later"""
        self.mock_patches.append(patch_obj)
        return patch_obj
    
    def cleanup(self):
        """Clean up all temporary resources"""
        # Clean up temporary directories
        for temp_dir in self.temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        # Clean up temporary files
        for temp_file in self.temp_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        
        # Stop all mock patches
        for patch_obj in self.mock_patches:
            if hasattr(patch_obj, 'stop'):
                patch_obj.stop()
        
        # Clear lists
        self.temp_dirs.clear()
        self.temp_files.clear()
        self.mock_patches.clear()


@pytest.fixture(scope="function")
def test_env():
    """Pytest fixture for test environment management"""
    env_manager = TestEnvironmentManager()
    yield env_manager
    env_manager.cleanup()


@pytest.fixture(scope="function")
def temp_workspace(test_env):
    """Pytest fixture for temporary workspace"""
    return test_env.create_temp_directory("workspace_")


@pytest.fixture(scope="function")
def sample_project(test_env, temp_workspace):
    """Pytest fixture for sample project structure"""
    return test_env.create_project_structure(temp_workspace, "SampleProject")


@pytest.fixture(scope="function")
def mock_openai_api(test_env):
    """Pytest fixture for mocking OpenAI API calls"""
    def mock_response(*args, **kwargs):
        prompt = args[0] if args else kwargs.get('prompt', '')
        
        if 'requirements' in prompt.lower():
            return "# Requirements Analysis\n\nMock requirements document"
        elif 'architecture' in prompt.lower():
            return "# System Architecture\n\nMock architecture document"
        elif 'developer' in prompt.lower() or 'code' in prompt.lower():
            return "# Implementation\n\nMock implementation plan"
        else:
            return "Mock AI response"
    
    patch_obj = patch('src.agent_executors.call_openai_api', side_effect=mock_response)
    test_env.add_mock_patch(patch_obj)
    return patch_obj.start()


@pytest.fixture(scope="function")
def mock_file_generation(test_env):
    """Pytest fixture for mocking file generation"""
    def mock_generate_files(*args, **kwargs):
        return {
            "package.json": '{"name": "test-project", "version": "1.0.0"}',
            "README.md": "# Test Project\n\nGenerated by TaoForge",
            "src/index.js": "console.log('Hello, World!');",
            "src/App.js": "import React from 'react';\n\nfunction App() {\n  return <div>Hello</div>;\n}"
        }
    
    patch_obj = patch('src.file_generators.generate_project_files', side_effect=mock_generate_files)
    test_env.add_mock_patch(patch_obj)
    return patch_obj.start()


if __name__ == "__main__":
    # Test the utilities
    factory = TestDataFactory()
    
    # Test project metadata creation
    metadata = factory.create_project_metadata("TestApp", "api")
    print("Project metadata:", json.dumps(metadata, indent=2))
    
    # Test pheromone data creation
    pheromone = factory.create_pheromone_data("test_signal", "proj_123")
    print("Pheromone data:", json.dumps(pheromone, indent=2))
    
    # Test environment manager
    env = TestEnvironmentManager()
    temp_dir = env.create_temp_directory()
    project_path = env.create_project_structure(temp_dir, "TestProject")
    print("Created project at:", project_path)
    env.cleanup()
    print("Cleanup completed")
