/**
 * Aetherforge VS Code Extension - Main Extension File
 * Comprehensive commands for project creation, agent interaction, and workflow visualization
 */

const vscode = require('vscode');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const WebSocket = require('ws');

// Extension state
let extensionContext;
let orchestratorUrl = 'http://localhost:8000';
let webviewPanels = new Map();
let activeProjects = new Map();
let agentConnections = new Map();
let workflowVisualizations = new Map();

/**
 * Extension activation function
 */
function activate(context) {
    console.log('🔮 Aetherforge VS Code Extension is now active!');
    extensionContext = context;
    
    // Load configuration
    loadConfiguration();
    
    // Register all commands
    registerCommands(context);
    
    // Initialize status bar
    initializeStatusBar(context);
    
    // Setup file watchers
    setupFileWatchers(context);
    
    // Initialize real-time connections
    initializeRealTimeConnections();
    
    console.log('✅ Aetherforge extension fully initialized');
}

/**
 * Register all VS Code commands
 */
function registerCommands(context) {
    // Project Creation Commands
    const createProjectCommand = vscode.commands.registerCommand('aetherforge.createProject', () => {
        showProjectCreationPanel();
    });
    
    const quickCreateCommand = vscode.commands.registerCommand('aetherforge.quickCreate', () => {
        showQuickCreateDialog();
    });
    
    const createFromTemplateCommand = vscode.commands.registerCommand('aetherforge.createFromTemplate', () => {
        showTemplateSelectionPanel();
    });
    
    // Agent Interaction Commands
    const showAgentPanelCommand = vscode.commands.registerCommand('aetherforge.showAgentPanel', () => {
        showAgentInteractionPanel();
    });
    
    const chatWithAgentCommand = vscode.commands.registerCommand('aetherforge.chatWithAgent', () => {
        showAgentChatDialog();
    });
    
    const viewAgentStatusCommand = vscode.commands.registerCommand('aetherforge.viewAgentStatus', () => {
        showAgentStatusPanel();
    });
    
    // Workflow Visualization Commands
    const showWorkflowCommand = vscode.commands.registerCommand('aetherforge.showWorkflow', () => {
        showWorkflowVisualizationPanel();
    });
    
    const viewProjectStatusCommand = vscode.commands.registerCommand('aetherforge.viewProjectStatus', () => {
        showProjectStatusPanel();
    });
    
    const showPheromoneTrailCommand = vscode.commands.registerCommand('aetherforge.showPheromoneTrail', () => {
        showPheromoneTrailVisualization();
    });
    
    // System Commands
    const showSystemStatusCommand = vscode.commands.registerCommand('aetherforge.showSystemStatus', () => {
        showSystemStatusPanel();
    });
    
    const openSettingsCommand = vscode.commands.registerCommand('aetherforge.openSettings', () => {
        showSettingsPanel();
    });
    
    const refreshConnectionCommand = vscode.commands.registerCommand('aetherforge.refreshConnection', () => {
        refreshOrchestratorConnection();
    });
    
    // Register all commands
    context.subscriptions.push(
        createProjectCommand,
        quickCreateCommand,
        createFromTemplateCommand,
        showAgentPanelCommand,
        chatWithAgentCommand,
        viewAgentStatusCommand,
        showWorkflowCommand,
        viewProjectStatusCommand,
        showPheromoneTrailCommand,
        showSystemStatusCommand,
        openSettingsCommand,
        refreshConnectionCommand
    );
}

/**
 * Load extension configuration
 */
function loadConfiguration() {
    const config = vscode.workspace.getConfiguration('aetherforge');
    orchestratorUrl = config.get('orchestratorUrl', 'http://localhost:8000');
    
    // Validate orchestrator URL
    if (!orchestratorUrl.startsWith('http')) {
        orchestratorUrl = 'http://' + orchestratorUrl;
    }
    
    console.log(`📡 Orchestrator URL: ${orchestratorUrl}`);
}

/**
 * Initialize status bar items
 */
function initializeStatusBar(context) {
    // Main status bar item
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = '$(rocket) Aetherforge';
    statusBarItem.tooltip = 'Aetherforge - Autonomous AI Software Creation';
    statusBarItem.command = 'aetherforge.showSystemStatus';
    statusBarItem.show();
    
    // Connection status item
    const connectionStatusItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 99);
    connectionStatusItem.text = '$(sync~spin) Connecting...';
    connectionStatusItem.tooltip = 'Orchestrator Connection Status';
    connectionStatusItem.command = 'aetherforge.refreshConnection';
    connectionStatusItem.show();
    
    context.subscriptions.push(statusBarItem, connectionStatusItem);
    
    // Update connection status
    updateConnectionStatus(connectionStatusItem);
}

/**
 * Update connection status in status bar
 */
async function updateConnectionStatus(statusItem) {
    try {
        const response = await axios.get(`${orchestratorUrl}/health`, { timeout: 5000 });
        if (response.status === 200) {
            statusItem.text = '$(check) Connected';
            statusItem.tooltip = 'Connected to Aetherforge Orchestrator';
            statusItem.color = undefined;
        }
    } catch (error) {
        statusItem.text = '$(error) Disconnected';
        statusItem.tooltip = `Cannot connect to orchestrator: ${error.message}`;
        statusItem.color = new vscode.ThemeColor('errorForeground');
    }
    
    // Update every 30 seconds
    setTimeout(() => updateConnectionStatus(statusItem), 30000);
}

/**
 * Setup file system watchers
 */
function setupFileWatchers(context) {
    // Watch for .aetherforge.json files (project metadata)
    const projectWatcher = vscode.workspace.createFileSystemWatcher('**/.aetherforge.json');
    
    projectWatcher.onDidCreate(async (uri) => {
        const projectDir = path.dirname(uri.fsPath);
        const projectName = path.basename(projectDir);
        
        try {
            const metadata = JSON.parse(fs.readFileSync(uri.fsPath, 'utf8'));
            activeProjects.set(metadata.project_id, {
                name: projectName,
                path: projectDir,
                metadata: metadata,
                status: metadata.status || 'unknown'
            });
            
            vscode.window.showInformationMessage(
                `✨ New Aetherforge project detected: ${projectName}`,
                'Open Project', 'View Status'
            ).then(selection => {
                if (selection === 'Open Project') {
                    vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectDir), true);
                } else if (selection === 'View Status') {
                    showProjectStatusPanel(metadata.project_id);
                }
            });
        } catch (error) {
            console.error('Error reading project metadata:', error);
        }
    });
    
    projectWatcher.onDidChange(async (uri) => {
        try {
            const metadata = JSON.parse(fs.readFileSync(uri.fsPath, 'utf8'));
            if (activeProjects.has(metadata.project_id)) {
                const project = activeProjects.get(metadata.project_id);
                project.metadata = metadata;
                project.status = metadata.status || 'unknown';
                
                // Update any open project status panels
                updateProjectStatusPanels(metadata.project_id);
            }
        } catch (error) {
            console.error('Error updating project metadata:', error);
        }
    });
    
    context.subscriptions.push(projectWatcher);
}

/**
 * Initialize real-time connections
 */
function initializeRealTimeConnections() {
    // Connect to pheromone bus WebSocket
    connectToPheromoneWebSocket();
    
    // Connect to agent status WebSocket
    connectToAgentWebSocket();
    
    // Connect to workflow status WebSocket
    connectToWorkflowWebSocket();
}

/**
 * Connect to pheromone bus WebSocket for real-time updates
 */
function connectToPheromoneWebSocket() {
    try {
        const wsUrl = orchestratorUrl.replace('http', 'ws') + '/ws/pheromones';
        const ws = new WebSocket(wsUrl);

        ws.on('open', () => {
            console.log('📡 Connected to pheromone bus WebSocket');
        });

        ws.on('message', (data) => {
            try {
                const pheromone = JSON.parse(data);
                handlePheromoneUpdate(pheromone);
            } catch (error) {
                console.error('Error parsing pheromone data:', error);
            }
        });

        ws.on('error', (error) => {
            console.error('Pheromone WebSocket error:', error);
        });

        ws.on('close', () => {
            console.log('📡 Pheromone WebSocket disconnected, attempting reconnect...');
            setTimeout(connectToPheromoneWebSocket, 5000);
        });

    } catch (error) {
        console.error('Failed to connect to pheromone WebSocket:', error);
        setTimeout(connectToPheromoneWebSocket, 10000);
    }
}

/**
 * Connect to agent status WebSocket for real-time updates
 */
function connectToAgentWebSocket() {
    try {
        const wsUrl = orchestratorUrl.replace('http', 'ws') + '/ws/agents';
        const ws = new WebSocket(wsUrl);

        ws.on('open', () => {
            console.log('📡 Connected to agent status WebSocket');
        });

        ws.on('message', (data) => {
            try {
                const agentUpdate = JSON.parse(data);
                handleAgentUpdate(agentUpdate);
            } catch (error) {
                console.error('Error parsing agent data:', error);
            }
        });

        ws.on('error', (error) => {
            console.error('Agent WebSocket error:', error);
        });

        ws.on('close', () => {
            console.log('📡 Agent WebSocket disconnected, attempting reconnect...');
            setTimeout(connectToAgentWebSocket, 5000);
        });

        agentConnections.set('main', ws);

    } catch (error) {
        console.error('Failed to connect to agent WebSocket:', error);
        setTimeout(connectToAgentWebSocket, 10000);
    }
}

/**
 * Connect to workflow status WebSocket for real-time updates
 */
function connectToWorkflowWebSocket() {
    try {
        const wsUrl = orchestratorUrl.replace('http', 'ws') + '/ws/workflows';
        const ws = new WebSocket(wsUrl);

        ws.on('open', () => {
            console.log('📡 Connected to workflow status WebSocket');
        });

        ws.on('message', (data) => {
            try {
                const workflowUpdate = JSON.parse(data);
                handleWorkflowUpdate(workflowUpdate);
            } catch (error) {
                console.error('Error parsing workflow data:', error);
            }
        });

        ws.on('error', (error) => {
            console.error('Workflow WebSocket error:', error);
        });

        ws.on('close', () => {
            console.log('📡 Workflow WebSocket disconnected, attempting reconnect...');
            setTimeout(connectToWorkflowWebSocket, 5000);
        });

        workflowVisualizations.set('main', ws);

    } catch (error) {
        console.error('Failed to connect to workflow WebSocket:', error);
        setTimeout(connectToWorkflowWebSocket, 10000);
    }
}

/**
 * Handle agent updates from WebSocket
 */
function handleAgentUpdate(agentUpdate) {
    // Update agent panels
    const agentPanel = webviewPanels.get('agentPanel');
    if (agentPanel) {
        agentPanel.webview.postMessage({
            command: 'agentUpdate',
            data: agentUpdate
        });
    }

    const agentStatusPanel = webviewPanels.get('agentStatus');
    if (agentStatusPanel) {
        agentStatusPanel.webview.postMessage({
            command: 'agentStatusUpdate',
            data: agentUpdate
        });
    }
}

/**
 * Handle workflow updates from WebSocket
 */
function handleWorkflowUpdate(workflowUpdate) {
    // Update workflow visualization panels
    const workflowPanel = webviewPanels.get('workflowVisualization');
    if (workflowPanel) {
        workflowPanel.webview.postMessage({
            command: 'workflowUpdate',
            data: workflowUpdate
        });
    }

    // Update project status if related
    if (workflowUpdate.project_id) {
        updateProjectStatusPanels(workflowUpdate.project_id);
    }
}

/**
 * Handle pheromone updates from WebSocket
 */
function handlePheromoneUpdate(pheromone) {
    // Update workflow visualizations
    updateWorkflowVisualizations(pheromone);
    
    // Update project status
    if (pheromone.project_id && activeProjects.has(pheromone.project_id)) {
        updateProjectStatus(pheromone.project_id, pheromone);
    }
    
    // Show notifications for important events
    if (pheromone.type === 'project_completed') {
        vscode.window.showInformationMessage(
            `🎉 Project "${pheromone.data.project_name}" completed successfully!`,
            'Open Project'
        ).then(selection => {
            if (selection === 'Open Project') {
                const project = activeProjects.get(pheromone.project_id);
                if (project) {
                    vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(project.path), true);
                }
            }
        });
    } else if (pheromone.type === 'project_failed') {
        vscode.window.showErrorMessage(
            `❌ Project "${pheromone.data.project_name}" failed: ${pheromone.data.error}`,
            'View Details'
        ).then(selection => {
            if (selection === 'View Details') {
                showProjectStatusPanel(pheromone.project_id);
            }
        });
    }
}

/**
 * Extension deactivation function
 */
function deactivate() {
    console.log('🔮 Aetherforge extension deactivated');
    
    // Close all WebSocket connections
    agentConnections.forEach(ws => ws.close());
    
    // Close all webview panels
    webviewPanels.forEach(panel => panel.dispose());
}

/**
 * Show project creation panel with comprehensive options
 */
function showProjectCreationPanel() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeProjectCreation',
        'Aetherforge - Create Project',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [vscode.Uri.file(path.join(extensionContext.extensionPath, 'media'))]
        }
    );

    panel.webview.html = getProjectCreationHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'createProject':
                await handleProjectCreation(message.data, panel.webview);
                break;
            case 'validateProjectName':
                await validateProjectName(message.projectName, panel.webview);
                break;
            case 'getProjectTypes':
                await sendProjectTypes(panel.webview);
                break;
            case 'getWorkflows':
                await sendWorkflows(panel.webview);
                break;
            case 'previewProject':
                await previewProjectStructure(message.data, panel.webview);
                break;
        }
    });

    webviewPanels.set('projectCreation', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('projectCreation');
    });
}

/**
 * Show quick create dialog for simple project creation
 */
async function showQuickCreateDialog() {
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe your project in detail',
        placeHolder: 'e.g., Create a todo list app with user authentication and real-time updates',
        ignoreFocusOut: true
    });

    if (!prompt) return;

    const projectType = await vscode.window.showQuickPick([
        { label: 'Fullstack Application', value: 'fullstack', description: 'Complete web application with frontend and backend' },
        { label: 'Frontend Only', value: 'frontend', description: 'Frontend-only application (React, Vue, Angular)' },
        { label: 'Backend API', value: 'backend', description: 'Backend API service (Express, FastAPI, Django)' },
        { label: 'Mobile App', value: 'mobile', description: 'Mobile application (React Native, Flutter)' },
        { label: 'Desktop App', value: 'desktop', description: 'Desktop application (Electron, Tauri)' },
        { label: 'API Service', value: 'api', description: 'REST API service' },
        { label: 'Game', value: 'game', description: 'Game development project' }
    ], {
        placeHolder: 'Select project type',
        ignoreFocusOut: true
    });

    if (!projectType) return;

    // Show progress notification
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: 'Creating project...',
        cancellable: false
    }, async (progress) => {
        try {
            progress.report({ increment: 10, message: 'Sending request to orchestrator...' });

            const response = await axios.post(`${orchestratorUrl}/projects`, {
                prompt: prompt,
                project_type: projectType.value
            }, { timeout: 300000 });

            progress.report({ increment: 90, message: 'Project created successfully!' });

            vscode.window.showInformationMessage(
                `✅ Project "${response.data.project_slug}" created successfully!`,
                'Open Project', 'View Status'
            ).then(selection => {
                if (selection === 'Open Project') {
                    const projectPath = response.data.project_path || path.join('projects', response.data.project_slug);
                    vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath), true);
                } else if (selection === 'View Status') {
                    showProjectStatusPanel(response.data.project_id);
                }
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create project: ${error.message}`);
        }
    });
}

/**
 * Handle project creation from webview
 */
async function handleProjectCreation(projectData, webview) {
    try {
        webview.postMessage({
            command: 'updateStatus',
            message: 'Creating project...',
            type: 'info'
        });

        const response = await axios.post(`${orchestratorUrl}/projects`, {
            prompt: projectData.prompt,
            project_name: projectData.projectName,
            project_type: projectData.projectType,
            workflow: projectData.workflow,
            requirements: projectData.requirements,
            priority: projectData.priority || 'normal',
            agent_behavior: projectData.agentBehavior || 'balanced',
            enable_parallel_execution: projectData.enableParallel || true,
            enable_code_review: projectData.enableCodeReview || true,
            enable_testing: projectData.enableTesting || true,
            target_platforms: projectData.targetPlatforms || [],
            programming_languages: projectData.programmingLanguages || [],
            frameworks: projectData.frameworks || [],
            databases: projectData.databases || [],
            deployment_targets: projectData.deploymentTargets || [],
            code_quality_level: projectData.codeQualityLevel || 'production',
            documentation_level: projectData.documentationLevel || 'comprehensive',
            test_coverage_target: projectData.testCoverageTarget || 0.8,
            enable_ai_optimization: projectData.enableAiOptimization || true,
            enable_security_scan: projectData.enableSecurityScan || true,
            enable_performance_optimization: projectData.enablePerformanceOptimization || true
        }, { timeout: 300000 });

        webview.postMessage({
            command: 'projectCreated',
            data: response.data
        });

        // Track the project
        activeProjects.set(response.data.project_id, {
            name: response.data.project_slug,
            path: response.data.project_path,
            metadata: response.data,
            status: 'creating'
        });

        vscode.window.showInformationMessage(
            `🚀 Project "${response.data.project_slug}" creation started!`,
            'View Progress', 'Open When Ready'
        ).then(selection => {
            if (selection === 'View Progress') {
                showProjectStatusPanel(response.data.project_id);
            } else if (selection === 'Open When Ready') {
                // Set up auto-open when project is complete
                setupAutoOpenProject(response.data.project_id, response.data.project_path);
            }
        });

    } catch (error) {
        webview.postMessage({
            command: 'updateStatus',
            message: `Error: ${error.message}`,
            type: 'error'
        });

        vscode.window.showErrorMessage(`Failed to create project: ${error.message}`);
    }
}

/**
 * Show agent interaction panel
 */
function showAgentInteractionPanel() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeAgentPanel',
        'Aetherforge - Agent Interaction',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getAgentInteractionHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'getAgents':
                await sendAgentList(panel.webview);
                break;
            case 'sendMessage':
                await sendMessageToAgent(message.agentId, message.message, panel.webview);
                break;
            case 'getAgentHistory':
                await sendAgentHistory(message.agentId, panel.webview);
                break;
            case 'executeAgentTask':
                await executeAgentTask(message.agentId, message.task, panel.webview);
                break;
            case 'getAgentCapabilities':
                await sendAgentCapabilities(message.agentId, panel.webview);
                break;
        }
    });

    webviewPanels.set('agentPanel', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('agentPanel');
    });
}

/**
 * Show agent chat dialog
 */
async function showAgentChatDialog() {
    // Get available agents
    try {
        const response = await axios.get(`${orchestratorUrl}/agents`);
        const agents = response.data.agents || [];

        if (agents.length === 0) {
            vscode.window.showInformationMessage('No agents are currently available');
            return;
        }

        const selectedAgent = await vscode.window.showQuickPick(
            agents.map(agent => ({
                label: agent.name || agent.id,
                description: agent.role || 'Agent',
                detail: agent.status || 'Available',
                value: agent.id
            })),
            {
                placeHolder: 'Select an agent to chat with',
                ignoreFocusOut: true
            }
        );

        if (!selectedAgent) return;

        const message = await vscode.window.showInputBox({
            prompt: `Send a message to ${selectedAgent.label}`,
            placeHolder: 'Type your message here...',
            ignoreFocusOut: true
        });

        if (!message) return;

        // Send message to agent
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `Sending message to ${selectedAgent.label}...`,
            cancellable: false
        }, async (progress) => {
            try {
                const response = await axios.post(`${orchestratorUrl}/agents/${selectedAgent.value}/message`, {
                    message: message,
                    sender: 'vscode-extension'
                });

                if (response.data.reply) {
                    vscode.window.showInformationMessage(
                        `${selectedAgent.label}: ${response.data.reply}`,
                        'Continue Chat'
                    ).then(selection => {
                        if (selection === 'Continue Chat') {
                            showAgentInteractionPanel();
                        }
                    });
                } else {
                    vscode.window.showInformationMessage('Message sent successfully');
                }

            } catch (error) {
                vscode.window.showErrorMessage(`Failed to send message: ${error.message}`);
            }
        });

    } catch (error) {
        vscode.window.showErrorMessage(`Failed to get agents: ${error.message}`);
    }
}

/**
 * Show agent status panel
 */
function showAgentStatusPanel() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeAgentStatus',
        'Aetherforge - Agent Status',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getAgentStatusHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'refreshAgentStatus':
                await refreshAgentStatus(panel.webview);
                break;
            case 'restartAgent':
                await restartAgent(message.agentId, panel.webview);
                break;
            case 'pauseAgent':
                await pauseAgent(message.agentId, panel.webview);
                break;
            case 'resumeAgent':
                await resumeAgent(message.agentId, panel.webview);
                break;
        }
    });

    webviewPanels.set('agentStatus', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('agentStatus');
    });

    // Initial load
    refreshAgentStatus(panel.webview);
}

/**
 * Send message to specific agent
 */
async function sendMessageToAgent(agentId, message, webview) {
    try {
        const response = await axios.post(`${orchestratorUrl}/agents/${agentId}/message`, {
            message: message,
            sender: 'vscode-extension',
            timestamp: new Date().toISOString()
        });

        webview.postMessage({
            command: 'messageResponse',
            data: {
                agentId: agentId,
                response: response.data,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        webview.postMessage({
            command: 'messageError',
            data: {
                agentId: agentId,
                error: error.message
            }
        });
    }
}

/**
 * Execute task with specific agent
 */
async function executeAgentTask(agentId, task, webview) {
    try {
        const response = await axios.post(`${orchestratorUrl}/agents/${agentId}/execute`, {
            task: task,
            requester: 'vscode-extension',
            timestamp: new Date().toISOString()
        });

        webview.postMessage({
            command: 'taskResponse',
            data: {
                agentId: agentId,
                taskId: response.data.task_id,
                status: response.data.status,
                result: response.data.result
            }
        });

    } catch (error) {
        webview.postMessage({
            command: 'taskError',
            data: {
                agentId: agentId,
                error: error.message
            }
        });
    }
}

/**
 * Show workflow visualization panel
 */
function showWorkflowVisualizationPanel() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeWorkflow',
        'Aetherforge - Workflow Visualization',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getWorkflowVisualizationHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'getWorkflows':
                await sendWorkflowList(panel.webview);
                break;
            case 'getWorkflowDetails':
                await sendWorkflowDetails(message.workflowId, panel.webview);
                break;
            case 'getActiveExecutions':
                await sendActiveExecutions(panel.webview);
                break;
            case 'pauseWorkflow':
                await pauseWorkflow(message.executionId, panel.webview);
                break;
            case 'resumeWorkflow':
                await resumeWorkflow(message.executionId, panel.webview);
                break;
            case 'cancelWorkflow':
                await cancelWorkflow(message.executionId, panel.webview);
                break;
        }
    });

    webviewPanels.set('workflowVisualization', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('workflowVisualization');
    });

    // Initial load
    sendWorkflowList(panel.webview);
    sendActiveExecutions(panel.webview);
}

/**
 * Show project status panel
 */
function showProjectStatusPanel(projectId = null) {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeProjectStatus',
        'Aetherforge - Project Status',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getProjectStatusHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'getProjects':
                await sendProjectList(panel.webview);
                break;
            case 'getProjectDetails':
                await sendProjectDetails(message.projectId, panel.webview);
                break;
            case 'refreshProject':
                await refreshProjectStatus(message.projectId, panel.webview);
                break;
            case 'openProject':
                await openProject(message.projectId);
                break;
            case 'deleteProject':
                await deleteProject(message.projectId, panel.webview);
                break;
        }
    });

    webviewPanels.set('projectStatus', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('projectStatus');
    });

    // Initial load
    sendProjectList(panel.webview);
    if (projectId) {
        sendProjectDetails(projectId, panel.webview);
    }
}

/**
 * Show pheromone trail visualization
 */
function showPheromoneTrailVisualization() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgePheromoneTrail',
        'Aetherforge - Pheromone Trail',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getPheromoneTrailHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'getPheromones':
                await sendPheromoneData(panel.webview, message.filters);
                break;
            case 'clearPheromones':
                await clearPheromones(panel.webview);
                break;
            case 'exportPheromones':
                await exportPheromones(panel.webview);
                break;
        }
    });

    webviewPanels.set('pheromoneTrail', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('pheromoneTrail');
    });

    // Initial load
    sendPheromoneData(panel.webview);
}

/**
 * Show system status panel
 */
function showSystemStatusPanel() {
    const panel = vscode.window.createWebviewPanel(
        'aetherforgeSystemStatus',
        'Aetherforge - System Status',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );

    panel.webview.html = getSystemStatusHTML();

    // Handle messages from webview
    panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
            case 'getSystemStatus':
                await sendSystemStatus(panel.webview);
                break;
            case 'restartService':
                await restartService(message.serviceName, panel.webview);
                break;
            case 'getServiceLogs':
                await sendServiceLogs(message.serviceName, panel.webview);
                break;
        }
    });

    webviewPanels.set('systemStatus', panel);

    panel.onDidDispose(() => {
        webviewPanels.delete('systemStatus');
    });

    // Initial load and periodic refresh
    sendSystemStatus(panel.webview);
    const refreshInterval = setInterval(() => {
        if (!panel.webview) {
            clearInterval(refreshInterval);
            return;
        }
        sendSystemStatus(panel.webview);
    }, 10000); // Refresh every 10 seconds
}

/**
 * Update workflow visualizations with new pheromone data
 */
function updateWorkflowVisualizations(pheromone) {
    const workflowPanel = webviewPanels.get('workflowVisualization');
    if (workflowPanel) {
        workflowPanel.webview.postMessage({
            command: 'pheromoneUpdate',
            data: pheromone
        });
    }

    const pheromonePanel = webviewPanels.get('pheromoneTrail');
    if (pheromonePanel) {
        pheromonePanel.webview.postMessage({
            command: 'newPheromone',
            data: pheromone
        });
    }
}

/**
 * Update project status panels
 */
function updateProjectStatusPanels(projectId) {
    const projectPanel = webviewPanels.get('projectStatus');
    if (projectPanel) {
        refreshProjectStatus(projectId, projectPanel.webview);
    }
}

/**
 * Generate HTML for project creation panel
 */
function getProjectCreationHTML() {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge - Create Project</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--vscode-textSeparator-foreground);
            padding-bottom: 20px;
        }
        h1 {
            color: var(--vscode-textLink-foreground);
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--vscode-input-foreground);
        }
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .checkbox-item input[type="checkbox"] {
            width: auto;
        }
        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        .button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        .status.info {
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            color: var(--vscode-inputValidation-infoForeground);
        }
        .status.error {
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            color: var(--vscode-inputValidation-errorForeground);
        }
        .status.success {
            background-color: var(--vscode-inputValidation-warningBackground);
            border: 1px solid var(--vscode-inputValidation-warningBorder);
            color: var(--vscode-inputValidation-warningForeground);
        }
        .advanced-options {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .advanced-toggle {
            cursor: pointer;
            user-select: none;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .advanced-content {
            display: none;
        }
        .advanced-content.show {
            display: block;
        }
        .preview-section {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            background-color: var(--vscode-panel-background);
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: var(--vscode-progressBar-background);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: var(--vscode-progressBar-foreground);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 Create New Project</h1>
            <p>Describe your project and let Aetherforge's AI agents build it for you</p>
        </div>

        <form id="projectForm">
            <div class="form-group">
                <label for="prompt">Project Description *</label>
                <textarea id="prompt" placeholder="Describe your project in detail. Include features, technologies, user requirements, and any specific needs..." required></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="projectName">Project Name</label>
                    <input type="text" id="projectName" placeholder="MyAwesomeProject">
                </div>
                <div class="form-group">
                    <label for="projectType">Project Type *</label>
                    <select id="projectType" required>
                        <option value="">Select project type...</option>
                        <option value="fullstack">Fullstack Application</option>
                        <option value="frontend">Frontend Only</option>
                        <option value="backend">Backend API</option>
                        <option value="mobile">Mobile App</option>
                        <option value="desktop">Desktop App</option>
                        <option value="api">API Service</option>
                        <option value="game">Game</option>
                        <option value="library">Library</option>
                        <option value="cli">CLI Tool</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="workflow">Workflow</label>
                    <select id="workflow">
                        <option value="">Default workflow</option>
                        <option value="rapid">Rapid Development</option>
                        <option value="thorough">Thorough Development</option>
                        <option value="enterprise">Enterprise Grade</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="priority">Priority</label>
                    <select id="priority">
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                        <option value="low">Low</option>
                    </select>
                </div>
            </div>

            <div class="advanced-options">
                <div class="advanced-toggle" onclick="toggleAdvanced()">
                    ⚙️ Advanced Options <span id="advancedIcon">▼</span>
                </div>
                <div class="advanced-content" id="advancedContent">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="agentBehavior">Agent Behavior</label>
                            <select id="agentBehavior">
                                <option value="balanced">Balanced</option>
                                <option value="conservative">Conservative</option>
                                <option value="aggressive">Aggressive</option>
                                <option value="creative">Creative</option>
                                <option value="production">Production</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="codeQualityLevel">Code Quality Level</label>
                            <select id="codeQualityLevel">
                                <option value="production">Production</option>
                                <option value="development">Development</option>
                                <option value="prototype">Prototype</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Features</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableParallel" checked>
                                <label for="enableParallel">Parallel Execution</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableCodeReview" checked>
                                <label for="enableCodeReview">Code Review</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableTesting" checked>
                                <label for="enableTesting">Testing</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableAiOptimization" checked>
                                <label for="enableAiOptimization">AI Optimization</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableSecurityScan" checked>
                                <label for="enableSecurityScan">Security Scan</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enablePerformanceOptimization">
                                <label for="enablePerformanceOptimization">Performance Optimization</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="status" id="status"></div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="button" class="button secondary" onclick="previewProject()">Preview Structure</button>
                <button type="submit" class="button">Create Project</button>
            </div>
        </form>

        <div class="preview-section" id="previewSection" style="display: none;">
            <h3>Project Structure Preview</h3>
            <div id="previewContent"></div>
        </div>

        <div id="progressSection" style="display: none;">
            <h3>Project Creation Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Initializing...</div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function toggleAdvanced() {
            const content = document.getElementById('advancedContent');
            const icon = document.getElementById('advancedIcon');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                icon.textContent = '▼';
            } else {
                content.classList.add('show');
                icon.textContent = '▲';
            }
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';

            if (type !== 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function previewProject() {
            const formData = getFormData();
            vscode.postMessage({
                command: 'previewProject',
                data: formData
            });
        }

        function getFormData() {
            return {
                prompt: document.getElementById('prompt').value,
                projectName: document.getElementById('projectName').value,
                projectType: document.getElementById('projectType').value,
                workflow: document.getElementById('workflow').value,
                priority: document.getElementById('priority').value,
                agentBehavior: document.getElementById('agentBehavior').value,
                codeQualityLevel: document.getElementById('codeQualityLevel').value,
                enableParallel: document.getElementById('enableParallel').checked,
                enableCodeReview: document.getElementById('enableCodeReview').checked,
                enableTesting: document.getElementById('enableTesting').checked,
                enableAiOptimization: document.getElementById('enableAiOptimization').checked,
                enableSecurityScan: document.getElementById('enableSecurityScan').checked,
                enablePerformanceOptimization: document.getElementById('enablePerformanceOptimization').checked
            };
        }

        document.getElementById('projectForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = getFormData();

            if (!formData.prompt.trim()) {
                showStatus('Please provide a project description', 'error');
                return;
            }

            if (!formData.projectType) {
                showStatus('Please select a project type', 'error');
                return;
            }

            document.getElementById('progressSection').style.display = 'block';
            showStatus('Creating project...', 'info');

            vscode.postMessage({
                command: 'createProject',
                data: formData
            });
        });

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'updateStatus':
                    showStatus(message.message, message.type || 'info');
                    break;
                case 'projectCreated':
                    showStatus('Project created successfully!', 'success');
                    document.getElementById('progressSection').style.display = 'none';
                    break;
                case 'previewResponse':
                    document.getElementById('previewSection').style.display = 'block';
                    document.getElementById('previewContent').innerHTML = message.preview;
                    break;
                case 'updateProgress':
                    document.getElementById('progressFill').style.width = message.progress + '%';
                    document.getElementById('progressText').textContent = message.text;
                    break;
            }
        });
    </script>
</body>
</html>`;
}

/**
 * Generate HTML for agent interaction panel
 */
function getAgentInteractionHTML() {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge - Agent Interaction</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }
        .agent-list {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 15px;
            background-color: var(--vscode-panel-background);
            overflow-y: auto;
        }
        .chat-area {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-panel-background);
            display: flex;
            flex-direction: column;
        }
        .agent-item {
            padding: 10px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .agent-item:hover {
            background-color: var(--vscode-list-hoverBackground);
        }
        .agent-item.active {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
        }
        .agent-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .agent-role {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        .agent-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-top: 5px;
            display: inline-block;
        }
        .agent-status.active {
            background-color: #28a745;
            color: white;
        }
        .agent-status.busy {
            background-color: #ffc107;
            color: black;
        }
        .agent-status.offline {
            background-color: #dc3545;
            color: white;
        }
        .chat-header {
            padding: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
        }
        .chat-input {
            padding: 15px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }
        .message.user {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
        }
        .message.agent {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
        }
        .message-time {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
        }
        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .no-agent-selected {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="agent-list">
            <h3>Available Agents</h3>
            <div id="agentList">
                <div style="text-align: center; color: var(--vscode-descriptionForeground); margin-top: 20px;">
                    Loading agents...
                </div>
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-header">
                <div id="chatHeader" class="no-agent-selected">
                    Select an agent to start chatting
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="no-agent-selected">
                    Choose an agent from the list to begin interaction
                </div>
            </div>

            <div class="chat-input" id="chatInput" style="display: none;">
                <div class="input-group">
                    <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
                    <button class="button" onclick="sendMessage()">Send</button>
                    <button class="button" onclick="executeTask()">Execute Task</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let selectedAgent = null;
        let agents = [];

        function loadAgents() {
            vscode.postMessage({ command: 'getAgents' });
        }

        function selectAgent(agentId) {
            selectedAgent = agentId;
            const agent = agents.find(a => a.id === agentId);

            // Update UI
            document.querySelectorAll('.agent-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(\`[data-agent-id="\${agentId}"]\`).classList.add('active');

            // Update chat header
            document.getElementById('chatHeader').innerHTML = \`
                <div class="agent-name">\${agent.name || agent.id}</div>
                <div class="agent-role">\${agent.role || 'Agent'} - \${agent.status || 'Unknown'}</div>
            \`;

            // Show chat input
            document.getElementById('chatInput').style.display = 'block';

            // Load chat history
            vscode.postMessage({
                command: 'getAgentHistory',
                agentId: agentId
            });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || !selectedAgent) return;

            // Add message to chat
            addMessage(message, 'user');
            input.value = '';

            // Send to agent
            vscode.postMessage({
                command: 'sendMessage',
                agentId: selectedAgent,
                message: message
            });
        }

        function executeTask() {
            const input = document.getElementById('messageInput');
            const task = input.value.trim();

            if (!task || !selectedAgent) return;

            // Add task to chat
            addMessage(\`Execute: \${task}\`, 'user');
            input.value = '';

            // Send task to agent
            vscode.postMessage({
                command: 'executeAgentTask',
                agentId: selectedAgent,
                task: task
            });
        }

        function addMessage(content, sender, timestamp = null) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;

            const time = timestamp || new Date().toLocaleTimeString();
            messageDiv.innerHTML = \`
                <div>\${content}</div>
                <div class="message-time">\${time}</div>
            \`;

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'agentList':
                    agents = message.agents;
                    renderAgentList();
                    break;
                case 'messageResponse':
                    if (message.data.response && message.data.response.reply) {
                        addMessage(message.data.response.reply, 'agent', message.data.timestamp);
                    }
                    break;
                case 'taskResponse':
                    addMessage(\`Task completed: \${message.data.result || 'Success'}\`, 'agent');
                    break;
                case 'messageError':
                case 'taskError':
                    addMessage(\`Error: \${message.data.error}\`, 'agent');
                    break;
            }
        });

        function renderAgentList() {
            const listDiv = document.getElementById('agentList');

            if (agents.length === 0) {
                listDiv.innerHTML = '<div style="text-align: center; color: var(--vscode-descriptionForeground);">No agents available</div>';
                return;
            }

            listDiv.innerHTML = agents.map(agent => \`
                <div class="agent-item" data-agent-id="\${agent.id}" onclick="selectAgent('\${agent.id}')">
                    <div class="agent-name">\${agent.name || agent.id}</div>
                    <div class="agent-role">\${agent.role || 'Agent'}</div>
                    <span class="agent-status \${(agent.status || 'offline').toLowerCase()}">\${agent.status || 'Offline'}</span>
                </div>
            \`).join('');
        }

        // Initialize
        loadAgents();
    </script>
</body>
</html>`;
}

/**
 * Generate HTML for workflow visualization panel
 */
function getWorkflowVisualizationHTML() {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge - Workflow Visualization</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--vscode-textSeparator-foreground);
        }
        .workflow-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 120px);
        }
        .workflow-list {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 15px;
            background-color: var(--vscode-panel-background);
            overflow-y: auto;
        }
        .workflow-visualization {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-panel-background);
            padding: 15px;
            overflow: auto;
        }
        .workflow-item {
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .workflow-item:hover {
            background-color: var(--vscode-list-hoverBackground);
        }
        .workflow-item.active {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
        }
        .workflow-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .workflow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
            margin-top: 5px;
        }
        .workflow-status.running {
            background-color: #28a745;
            color: white;
        }
        .workflow-status.completed {
            background-color: #007bff;
            color: white;
        }
        .workflow-status.failed {
            background-color: #dc3545;
            color: white;
        }
        .workflow-status.paused {
            background-color: #ffc107;
            color: black;
        }
        .workflow-diagram {
            min-height: 400px;
            border: 1px dashed var(--vscode-input-border);
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--vscode-descriptionForeground);
        }
        .step-node {
            display: inline-block;
            padding: 10px 15px;
            border: 2px solid var(--vscode-input-border);
            border-radius: 8px;
            margin: 10px;
            background-color: var(--vscode-input-background);
            position: relative;
            min-width: 120px;
            text-align: center;
        }
        .step-node.completed {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .step-node.running {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            animation: pulse 2s infinite;
        }
        .step-node.failed {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .step-node.pending {
            border-color: var(--vscode-input-border);
            opacity: 0.6;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
        }
        .step-arrow {
            display: inline-block;
            margin: 0 10px;
            color: var(--vscode-descriptionForeground);
        }
        .workflow-controls {
            margin-top: 15px;
            text-align: center;
        }
        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .button.danger {
            background-color: #dc3545;
            color: white;
        }
        .button.warning {
            background-color: #ffc107;
            color: black;
        }
        .execution-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-editor-background);
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .detail-label {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Workflow Visualization</h1>
            <div>
                <button class="button" onclick="refreshWorkflows()">Refresh</button>
                <button class="button" onclick="showActiveExecutions()">Active Executions</button>
            </div>
        </div>

        <div class="workflow-grid">
            <div class="workflow-list">
                <h3>Workflows & Executions</h3>
                <div id="workflowList">
                    <div style="text-align: center; color: var(--vscode-descriptionForeground); margin-top: 20px;">
                        Loading workflows...
                    </div>
                </div>
            </div>

            <div class="workflow-visualization">
                <div id="workflowDiagram" class="workflow-diagram">
                    Select a workflow to view its visualization
                </div>

                <div class="workflow-controls" id="workflowControls" style="display: none;">
                    <button class="button warning" onclick="pauseWorkflow()">Pause</button>
                    <button class="button" onclick="resumeWorkflow()">Resume</button>
                    <button class="button danger" onclick="cancelWorkflow()">Cancel</button>
                </div>

                <div class="execution-details" id="executionDetails" style="display: none;">
                    <h4>Execution Details</h4>
                    <div id="detailsContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let selectedExecution = null;
        let workflows = [];
        let executions = [];

        function refreshWorkflows() {
            vscode.postMessage({ command: 'getWorkflows' });
            vscode.postMessage({ command: 'getActiveExecutions' });
        }

        function showActiveExecutions() {
            vscode.postMessage({ command: 'getActiveExecutions' });
        }

        function selectExecution(executionId) {
            selectedExecution = executionId;

            // Update UI
            document.querySelectorAll('.workflow-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(\`[data-execution-id="\${executionId}"]\`).classList.add('active');

            // Get execution details
            vscode.postMessage({
                command: 'getWorkflowDetails',
                workflowId: executionId
            });
        }

        function pauseWorkflow() {
            if (selectedExecution) {
                vscode.postMessage({
                    command: 'pauseWorkflow',
                    executionId: selectedExecution
                });
            }
        }

        function resumeWorkflow() {
            if (selectedExecution) {
                vscode.postMessage({
                    command: 'resumeWorkflow',
                    executionId: selectedExecution
                });
            }
        }

        function cancelWorkflow() {
            if (selectedExecution && confirm('Are you sure you want to cancel this workflow?')) {
                vscode.postMessage({
                    command: 'cancelWorkflow',
                    executionId: selectedExecution
                });
            }
        }

        function renderWorkflowList() {
            const listDiv = document.getElementById('workflowList');

            if (executions.length === 0) {
                listDiv.innerHTML = '<div style="text-align: center; color: var(--vscode-descriptionForeground);">No active workflows</div>';
                return;
            }

            listDiv.innerHTML = executions.map(execution => \`
                <div class="workflow-item" data-execution-id="\${execution.id}" onclick="selectExecution('\${execution.id}')">
                    <div class="workflow-name">\${execution.workflow_name || execution.workflow_id}</div>
                    <div style="font-size: 12px; color: var(--vscode-descriptionForeground);">
                        Project: \${execution.project_id}
                    </div>
                    <span class="workflow-status \${execution.status.toLowerCase()}">\${execution.status}</span>
                </div>
            \`).join('');
        }

        function renderWorkflowDiagram(workflowData) {
            const diagramDiv = document.getElementById('workflowDiagram');
            const controlsDiv = document.getElementById('workflowControls');
            const detailsDiv = document.getElementById('executionDetails');

            if (!workflowData || !workflowData.steps) {
                diagramDiv.innerHTML = 'No workflow data available';
                controlsDiv.style.display = 'none';
                detailsDiv.style.display = 'none';
                return;
            }

            // Render workflow steps
            const stepsHtml = workflowData.steps.map((step, index) => {
                const statusClass = step.status || 'pending';
                const arrow = index < workflowData.steps.length - 1 ? '<span class="step-arrow">→</span>' : '';

                return \`
                    <div class="step-node \${statusClass}">
                        <div style="font-weight: bold;">\${step.name}</div>
                        <div style="font-size: 11px; margin-top: 5px;">\${step.type || 'Task'}</div>
                    </div>\${arrow}
                \`;
            }).join('');

            diagramDiv.innerHTML = \`
                <div style="text-align: left;">
                    <h4>\${workflowData.name || 'Workflow'}</h4>
                    <div style="margin: 20px 0; text-align: center;">
                        \${stepsHtml}
                    </div>
                </div>
            \`;

            // Show controls if workflow is active
            if (workflowData.status === 'running') {
                controlsDiv.style.display = 'block';
            } else {
                controlsDiv.style.display = 'none';
            }

            // Show execution details
            detailsDiv.style.display = 'block';
            document.getElementById('detailsContent').innerHTML = \`
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span>\${workflowData.status || 'Unknown'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Started:</span>
                    <span>\${workflowData.started_at || 'Unknown'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Progress:</span>
                    <span>\${workflowData.completed_steps || 0}/\${workflowData.total_steps || 0} steps</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Duration:</span>
                    <span>\${workflowData.duration || 'Calculating...'}</span>
                </div>
            \`;
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'workflowList':
                    workflows = message.workflows || [];
                    break;
                case 'activeExecutions':
                    executions = message.executions || [];
                    renderWorkflowList();
                    break;
                case 'workflowDetails':
                    renderWorkflowDiagram(message.workflow);
                    break;
                case 'pheromoneUpdate':
                    // Update workflow visualization with real-time data
                    if (selectedExecution && message.data.project_id) {
                        // Refresh the current workflow if it matches
                        vscode.postMessage({
                            command: 'getWorkflowDetails',
                            workflowId: selectedExecution
                        });
                    }
                    break;
            }
        });

        // Initialize
        refreshWorkflows();
    </script>
</body>
</html>`;
}

// Utility functions for API communication
async function sendAgentList(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/agents`);
        webview.postMessage({
            command: 'agentList',
            agents: response.data.agents || []
        });
    } catch (error) {
        console.error('Failed to get agent list:', error);
        webview.postMessage({
            command: 'agentList',
            agents: []
        });
    }
}

async function sendWorkflowList(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/workflows`);
        webview.postMessage({
            command: 'workflowList',
            workflows: response.data.workflows || []
        });
    } catch (error) {
        console.error('Failed to get workflow list:', error);
    }
}

async function sendActiveExecutions(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/executions/active`);
        webview.postMessage({
            command: 'activeExecutions',
            executions: response.data.executions || []
        });
    } catch (error) {
        console.error('Failed to get active executions:', error);
        webview.postMessage({
            command: 'activeExecutions',
            executions: []
        });
    }
}

async function refreshOrchestratorConnection() {
    try {
        const response = await axios.get(`${orchestratorUrl}/health`, { timeout: 5000 });
        if (response.status === 200) {
            vscode.window.showInformationMessage('✅ Connected to Aetherforge Orchestrator');
        }
    } catch (error) {
        vscode.window.showErrorMessage(`❌ Cannot connect to orchestrator: ${error.message}`);
    }
}

// Additional HTML generators (simplified for space)
function getAgentStatusHTML() {
    return `<!DOCTYPE html><html><head><title>Agent Status</title></head><body><h1>Agent Status Panel</h1><div id="agentStatus">Loading...</div></body></html>`;
}

function getProjectStatusHTML() {
    return `<!DOCTYPE html><html><head><title>Project Status</title></head><body><h1>Project Status Panel</h1><div id="projectStatus">Loading...</div></body></html>`;
}

function getPheromoneTrailHTML() {
    return `<!DOCTYPE html><html><head><title>Pheromone Trail</title></head><body><h1>Pheromone Trail Visualization</h1><div id="pheromoneTrail">Loading...</div></body></html>`;
}

function getSystemStatusHTML() {
    return `<!DOCTYPE html><html><head><title>System Status</title></head><body><h1>System Status Panel</h1><div id="systemStatus">Loading...</div></body></html>`;
}

/**
 * Show template selection panel
 */
function showTemplateSelectionPanel() {
    vscode.window.showQuickPick([
        { label: 'React + Express Fullstack', value: 'react-express', description: 'Complete fullstack application with React frontend and Express backend' },
        { label: 'Vue + FastAPI', value: 'vue-fastapi', description: 'Vue.js frontend with Python FastAPI backend' },
        { label: 'Next.js Application', value: 'nextjs', description: 'Full-stack React application with Next.js' },
        { label: 'Django Web App', value: 'django', description: 'Python Django web application' },
        { label: 'Flutter Mobile App', value: 'flutter', description: 'Cross-platform mobile application with Flutter' },
        { label: 'Electron Desktop App', value: 'electron', description: 'Cross-platform desktop application with Electron' },
        { label: 'REST API Service', value: 'rest-api', description: 'RESTful API service with authentication' },
        { label: 'Microservices Architecture', value: 'microservices', description: 'Microservices-based application architecture' }
    ], {
        placeHolder: 'Select a project template',
        ignoreFocusOut: true
    }).then(async (template) => {
        if (template) {
            const projectName = await vscode.window.showInputBox({
                prompt: 'Enter project name',
                placeHolder: 'my-awesome-project',
                ignoreFocusOut: true
            });

            if (projectName) {
                // Create project from template
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `Creating project from ${template.label} template...`,
                    cancellable: false
                }, async (progress) => {
                    try {
                        const response = await axios.post(`${orchestratorUrl}/projects/template`, {
                            template: template.value,
                            project_name: projectName
                        });

                        vscode.window.showInformationMessage(
                            `✅ Project "${projectName}" created from template!`,
                            'Open Project'
                        ).then(selection => {
                            if (selection === 'Open Project') {
                                const projectPath = response.data.project_path || path.join('projects', projectName);
                                vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath), true);
                            }
                        });
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to create project from template: ${error.message}`);
                    }
                });
            }
        }
    });
}

/**
 * Show settings panel
 */
function showSettingsPanel() {
    vscode.commands.executeCommand('workbench.action.openSettings', 'aetherforge');
}

/**
 * Validate project name
 */
async function validateProjectName(projectName, webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/projects/validate-name`, {
            params: { name: projectName }
        });

        webview.postMessage({
            command: 'nameValidation',
            valid: response.data.valid,
            message: response.data.message
        });
    } catch (error) {
        webview.postMessage({
            command: 'nameValidation',
            valid: false,
            message: 'Error validating project name'
        });
    }
}

/**
 * Send project types to webview
 */
async function sendProjectTypes(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/project-types`);
        webview.postMessage({
            command: 'projectTypes',
            types: response.data.types || []
        });
    } catch (error) {
        webview.postMessage({
            command: 'projectTypes',
            types: [
                { id: 'fullstack', name: 'Fullstack Application', description: 'Complete web application' },
                { id: 'frontend', name: 'Frontend Only', description: 'Frontend-only application' },
                { id: 'backend', name: 'Backend API', description: 'Backend API service' },
                { id: 'mobile', name: 'Mobile App', description: 'Mobile application' },
                { id: 'desktop', name: 'Desktop App', description: 'Desktop application' }
            ]
        });
    }
}

/**
 * Send workflows to webview
 */
async function sendWorkflows(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/workflows`);
        webview.postMessage({
            command: 'workflows',
            workflows: response.data.workflows || []
        });
    } catch (error) {
        webview.postMessage({
            command: 'workflows',
            workflows: [
                { id: 'default', name: 'Default Workflow', description: 'Standard development workflow' },
                { id: 'rapid', name: 'Rapid Development', description: 'Fast prototyping workflow' },
                { id: 'thorough', name: 'Thorough Development', description: 'Comprehensive development with testing' },
                { id: 'enterprise', name: 'Enterprise Grade', description: 'Enterprise-level development workflow' }
            ]
        });
    }
}

/**
 * Preview project structure
 */
async function previewProjectStructure(projectData, webview) {
    try {
        const response = await axios.post(`${orchestratorUrl}/projects/preview`, {
            project_type: projectData.projectType,
            prompt: projectData.prompt,
            options: projectData
        });

        webview.postMessage({
            command: 'previewResponse',
            preview: response.data.preview || 'Preview not available'
        });
    } catch (error) {
        webview.postMessage({
            command: 'previewResponse',
            preview: `<div style="color: red;">Error generating preview: ${error.message}</div>`
        });
    }
}

/**
 * Setup auto-open project when complete
 */
function setupAutoOpenProject(projectId, projectPath) {
    // Store the project for auto-opening
    const autoOpenProjects = extensionContext.globalState.get('autoOpenProjects', new Map());
    autoOpenProjects.set(projectId, projectPath);
    extensionContext.globalState.update('autoOpenProjects', autoOpenProjects);

    // The project will be auto-opened when we receive the completion pheromone
}

/**
 * Update project status
 */
function updateProjectStatus(projectId, pheromone) {
    const project = activeProjects.get(projectId);
    if (project) {
        // Update project status based on pheromone type
        switch (pheromone.type) {
            case 'project_started':
                project.status = 'in_progress';
                break;
            case 'project_completed':
                project.status = 'completed';
                // Check if this project should be auto-opened
                const autoOpenProjects = extensionContext.globalState.get('autoOpenProjects', new Map());
                if (autoOpenProjects.has(projectId)) {
                    const projectPath = autoOpenProjects.get(projectId);
                    vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath), true);
                    autoOpenProjects.delete(projectId);
                    extensionContext.globalState.update('autoOpenProjects', autoOpenProjects);
                }
                break;
            case 'project_failed':
                project.status = 'failed';
                break;
            case 'project_phase_completed':
                project.currentPhase = pheromone.data.phase;
                project.progress = pheromone.data.progress;
                break;
        }

        // Update any open project status panels
        updateProjectStatusPanels(projectId);
    }
}

// Additional utility functions for API communication
async function sendSystemStatus(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/system/status`);
        webview.postMessage({
            command: 'systemStatus',
            status: response.data
        });
    } catch (error) {
        webview.postMessage({
            command: 'systemStatus',
            status: { error: error.message }
        });
    }
}

async function sendPheromoneData(webview, filters = {}) {
    try {
        const response = await axios.get(`${orchestratorUrl}/pheromones`, { params: filters });
        webview.postMessage({
            command: 'pheromoneData',
            pheromones: response.data.pheromones || []
        });
    } catch (error) {
        webview.postMessage({
            command: 'pheromoneData',
            pheromones: []
        });
    }
}

async function refreshProjectStatus(projectId, webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/projects/${projectId}/status`);
        webview.postMessage({
            command: 'projectStatus',
            project: response.data
        });
    } catch (error) {
        webview.postMessage({
            command: 'projectStatus',
            project: { error: error.message }
        });
    }
}

async function sendProjectList(webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/projects`);
        webview.postMessage({
            command: 'projectList',
            projects: response.data.projects || []
        });
    } catch (error) {
        webview.postMessage({
            command: 'projectList',
            projects: Array.from(activeProjects.values())
        });
    }
}

async function sendProjectDetails(projectId, webview) {
    try {
        const response = await axios.get(`${orchestratorUrl}/projects/${projectId}`);
        webview.postMessage({
            command: 'projectDetails',
            project: response.data
        });
    } catch (error) {
        const project = activeProjects.get(projectId);
        webview.postMessage({
            command: 'projectDetails',
            project: project || { error: error.message }
        });
    }
}

module.exports = {
    activate,
    deactivate
};
