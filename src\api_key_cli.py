#!/usr/bin/env python3
"""
Aetherforge API Key Management CLI
Provides command-line interface for managing API keys securely.
"""

import asyncio
import sys
import os
from pathlib import Path
import argparse
import getpass
from typing import Optional
import json

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from api_manager import APIManager, APIProvider, APIKeyValidator

class APIKeyCLI:
    """Command-line interface for API key management"""
    
    def __init__(self):
        self.api_manager = APIManager()
        self.validator = APIKeyValidator()
    
    async def set_key(self, provider: str, api_key: Optional[str] = None, validate: bool = True):
        """Set an API key for a provider"""
        try:
            provider_enum = APIProvider(provider.lower())
        except ValueError:
            print(f"❌ Invalid provider: {provider}")
            print(f"Valid providers: {', '.join([p.value for p in APIProvider])}")
            return False
        
        # Get API key if not provided
        if not api_key:
            api_key = getpass.getpass(f"Enter API key for {provider}: ")
        
        if not api_key.strip():
            print("❌ API key cannot be empty")
            return False
        
        print(f"🔄 Setting API key for {provider}...")
        
        # Set the key
        result = await self.api_manager.set_api_key(provider_enum, api_key, validate)
        
        if result["success"]:
            print(f"✅ {result['message']}")
            if validate and "validation" in result:
                validation = result["validation"]
                if validation.get("model_access"):
                    print(f"📋 Available models: {', '.join(validation['model_access'])}")
            return True
        else:
            print(f"❌ {result['message']}")
            return False
    
    async def test_key(self, provider: str):
        """Test an API key"""
        try:
            provider_enum = APIProvider(provider.lower())
        except ValueError:
            print(f"❌ Invalid provider: {provider}")
            return False
        
        api_key = self.api_manager.get_api_key(provider_enum)
        if not api_key:
            print(f"❌ No API key found for {provider}")
            return False
        
        print(f"🔄 Testing API key for {provider}...")
        
        result = await self.validator.validate_key(provider_enum, api_key)
        
        if result["valid"]:
            print(f"✅ API key is valid for {provider}")
            if result.get("model_access"):
                print(f"📋 Available models: {', '.join(result['model_access'])}")
        else:
            print(f"❌ API key validation failed: {result['message']}")
        
        return result["valid"]
    
    def list_keys(self):
        """List all configured API keys"""
        providers = self.api_manager.list_configured_providers()
        
        print("\n🔑 API Key Status:")
        print("-" * 50)
        
        for provider_info in providers:
            provider = provider_info["provider"]
            has_key = provider_info["has_key"]
            is_active = provider_info["is_active"]
            model = provider_info["model"]
            
            status = "✅ Active" if is_active else ("🔑 Configured" if has_key else "❌ Not configured")
            print(f"{provider.upper():12} | {status:15} | Model: {model}")
        
        print("-" * 50)
    
    def remove_key(self, provider: str):
        """Remove an API key"""
        try:
            provider_enum = APIProvider(provider.lower())
        except ValueError:
            print(f"❌ Invalid provider: {provider}")
            return False
        
        # Confirm removal
        confirm = input(f"Are you sure you want to remove the API key for {provider}? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ Operation cancelled")
            return False
        
        success = self.api_manager.remove_api_key(provider_enum)
        
        if success:
            print(f"✅ API key removed for {provider}")
        else:
            print(f"❌ Failed to remove API key for {provider}")
        
        return success
    
    async def setup_wizard(self):
        """Interactive setup wizard for first-time configuration"""
        print("🔮 Welcome to Aetherforge API Key Setup Wizard!")
        print("=" * 50)
        print("This wizard will help you configure API keys for AI providers.")
        print()
        
        # Check for existing keys
        providers = self.api_manager.list_configured_providers()
        configured_providers = [p for p in providers if p["has_key"]]
        
        if configured_providers:
            print("📋 Currently configured providers:")
            for provider in configured_providers:
                print(f"  ✅ {provider['provider'].upper()}")
            print()
        
        # OpenAI setup
        if not any(p["provider"] == "openai" and p["has_key"] for p in providers):
            print("🤖 OpenAI Configuration")
            print("-" * 20)
            print("OpenAI provides GPT models (recommended for best results)")
            
            setup_openai = input("Would you like to configure OpenAI? (Y/n): ").lower()
            if setup_openai != 'n':
                print("💡 Get your API key from: https://platform.openai.com/api-keys")
                await self.set_key("openai")
                print()
        
        # Anthropic setup
        if not any(p["provider"] == "anthropic" and p["has_key"] for p in providers):
            print("🧠 Anthropic Configuration")
            print("-" * 20)
            print("Anthropic provides Claude models (alternative to OpenAI)")
            
            setup_anthropic = input("Would you like to configure Anthropic? (y/N): ").lower()
            if setup_anthropic == 'y':
                print("💡 Get your API key from: https://console.anthropic.com/")
                await self.set_key("anthropic")
                print()
        
        # Local setup
        if not any(p["provider"] == "local" and p["has_key"] for p in providers):
            print("🏠 Local Model Configuration")
            print("-" * 25)
            print("Configure local models (Ollama) for offline usage")
            
            setup_local = input("Would you like to configure local models? (y/N): ").lower()
            if setup_local == 'y':
                print("💡 Make sure Ollama is running on http://localhost:11434")
                # Test local endpoint
                result = await self.validator.validate_local_endpoint("http://localhost:11434")
                if result["valid"]:
                    # Set a dummy key for local
                    await self.api_manager.set_api_key(APIProvider.LOCAL, "local", validate=False)
                    print("✅ Local models configured successfully")
                else:
                    print(f"❌ Local endpoint not accessible: {result['message']}")
                print()
        
        print("🎉 Setup complete!")
        print("Use 'aetherforge-keys list' to view your configuration")
        print("Use 'aetherforge-keys test <provider>' to test your keys")

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Aetherforge API Key Management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  aetherforge-keys setup                    # Run setup wizard
  aetherforge-keys set openai              # Set OpenAI API key
  aetherforge-keys set anthropic sk-ant... # Set Anthropic key directly
  aetherforge-keys list                     # List all configured keys
  aetherforge-keys test openai             # Test OpenAI key
  aetherforge-keys remove anthropic        # Remove Anthropic key
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Setup wizard
    subparsers.add_parser('setup', help='Run interactive setup wizard')
    
    # Set key
    set_parser = subparsers.add_parser('set', help='Set API key for a provider')
    set_parser.add_argument('provider', help='Provider name (openai, anthropic, local)')
    set_parser.add_argument('--key', help='API key (will prompt if not provided)')
    set_parser.add_argument('--no-validate', action='store_true', help='Skip key validation')
    
    # Test key
    test_parser = subparsers.add_parser('test', help='Test API key for a provider')
    test_parser.add_argument('provider', help='Provider name')
    
    # List keys
    subparsers.add_parser('list', help='List all configured API keys')
    
    # Remove key
    remove_parser = subparsers.add_parser('remove', help='Remove API key for a provider')
    remove_parser.add_argument('provider', help='Provider name')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = APIKeyCLI()
    
    try:
        if args.command == 'setup':
            asyncio.run(cli.setup_wizard())
        elif args.command == 'set':
            validate = not args.no_validate
            asyncio.run(cli.set_key(args.provider, args.key, validate))
        elif args.command == 'test':
            asyncio.run(cli.test_key(args.provider))
        elif args.command == 'list':
            cli.list_keys()
        elif args.command == 'remove':
            cli.remove_key(args.provider)
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def create_cli_entry_point():
    """Create a CLI entry point script"""
    entry_script = """#!/usr/bin/env python3
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from api_key_cli import main

if __name__ == "__main__":
    main()
"""

    script_path = Path("aetherforge-keys")
    script_path.write_text(entry_script)
    script_path.chmod(0o755)  # Make executable
    print(f"Created CLI entry point: {script_path}")

if __name__ == "__main__":
    main()
