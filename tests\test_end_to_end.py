"""
End-to-End Tests for TaoForge
Tests complete workflows from user input to project delivery
"""

import pytest
import asyncio
import json
import tempfile
import shutil
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

# Import system components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from orchestrator import app
from project_generator_standalone import ProjectGenerationPipeline


class TestEndToEndWorkflows:
    """Test complete end-to-end workflows"""
    
    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for E2E tests"""
        workspace = tempfile.mkdtemp(prefix="taoforge_e2e_")
        yield workspace
        shutil.rmtree(workspace, ignore_errors=True)
    
    @pytest.fixture
    def test_client(self):
        """Create test client for API testing"""
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_simple_web_app_generation(self, temp_workspace):
        """Test generating a simple web application end-to-end"""
        project_path = Path(temp_workspace) / "simple_web_app"
        
        # Initialize pipeline
        pipeline = ProjectGenerationPipeline()
        
        # Mock all external dependencies
        with patch('src.agent_executors.call_openai_api') as mock_openai:
            # Mock different responses for different agents
            def mock_openai_response(*args, **kwargs):
                prompt = args[0] if args else kwargs.get('prompt', '')
                
                if 'requirements' in prompt.lower() or 'analyst' in prompt.lower():
                    return """
                    # Requirements Analysis
                    
                    ## Functional Requirements
                    1. User registration and login
                    2. Dashboard with user profile
                    3. Responsive design
                    
                    ## Technical Requirements
                    1. Modern web framework
                    2. Database integration
                    3. Authentication system
                    """
                
                elif 'architecture' in prompt.lower() or 'architect' in prompt.lower():
                    return """
                    # System Architecture
                    
                    ## Technology Stack
                    - Frontend: React with TypeScript
                    - Backend: Node.js with Express
                    - Database: PostgreSQL
                    - Authentication: JWT
                    
                    ## Project Structure
                    - client/ (React frontend)
                    - server/ (Node.js backend)
                    - database/ (SQL schemas)
                    """
                
                elif 'developer' in prompt.lower() or 'code' in prompt.lower():
                    return """
                    # Implementation Plan
                    
                    ## Files to Create
                    1. package.json - Project configuration
                    2. server/index.js - Express server
                    3. client/src/App.tsx - React application
                    4. database/schema.sql - Database schema
                    """
                
                else:
                    return "Generic AI response"
            
            mock_openai.side_effect = mock_openai_response
            
            with patch('src.file_generators.generate_project_files') as mock_files:
                # Mock comprehensive file generation
                mock_files.return_value = {
                    "package.json": json.dumps({
                        "name": "simple-web-app",
                        "version": "1.0.0",
                        "scripts": {
                            "start": "node server/index.js",
                            "dev": "concurrently \"npm run server\" \"npm run client\"",
                            "server": "nodemon server/index.js",
                            "client": "cd client && npm start"
                        },
                        "dependencies": {
                            "express": "^4.18.0",
                            "jsonwebtoken": "^8.5.1",
                            "bcryptjs": "^2.4.3",
                            "pg": "^8.7.3"
                        }
                    }, indent=2),
                    
                    "server/index.js": """
const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 3001;

app.use(express.json());

// Routes
app.get('/api/health', (req, res) => {
    res.json({ status: 'healthy' });
});

app.post('/api/auth/register', async (req, res) => {
    // Registration logic
    res.json({ message: 'User registered successfully' });
});

app.post('/api/auth/login', async (req, res) => {
    // Login logic
    res.json({ token: 'jwt-token' });
});

app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
                    """,
                    
                    "client/package.json": json.dumps({
                        "name": "simple-web-app-client",
                        "version": "1.0.0",
                        "dependencies": {
                            "react": "^18.2.0",
                            "react-dom": "^18.2.0",
                            "react-router-dom": "^6.3.0",
                            "axios": "^0.27.2"
                        },
                        "scripts": {
                            "start": "react-scripts start",
                            "build": "react-scripts build"
                        }
                    }, indent=2),
                    
                    "client/src/App.tsx": """
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './components/Login';
import Dashboard from './components/Dashboard';

function App() {
    return (
        <Router>
            <div className="App">
                <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/" element={<Login />} />
                </Routes>
            </div>
        </Router>
    );
}

export default App;
                    """,
                    
                    "client/src/components/Login.tsx": """
import React, { useState } from 'react';
import axios from 'axios';

const Login: React.FC = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const response = await axios.post('/api/auth/login', { email, password });
            localStorage.setItem('token', response.data.token);
            window.location.href = '/dashboard';
        } catch (error) {
            console.error('Login failed:', error);
        }
    };

    return (
        <form onSubmit={handleLogin}>
            <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                required
            />
            <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                required
            />
            <button type="submit">Login</button>
        </form>
    );
};

export default Login;
                    """,
                    
                    "database/schema.sql": """
-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table for JWT token management
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
                    """,
                    
                    "README.md": """
# Simple Web Application

A full-stack web application with user authentication and dashboard.

## Features

- User registration and login
- JWT-based authentication
- Responsive React frontend
- Express.js backend
- PostgreSQL database

## Setup

1. Install dependencies:
   ```bash
   npm install
   cd client && npm install
   ```

2. Set up database:
   ```bash
   createdb simple_web_app
   psql simple_web_app < database/schema.sql
   ```

3. Start development servers:
   ```bash
   npm run dev
   ```

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/health` - Health check

## Frontend Routes

- `/` - Login page
- `/login` - Login page
- `/dashboard` - User dashboard (protected)
                    """,
                    
                    ".env.example": """
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=simple_web_app
DB_USER=your_username
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret_key

# Server
PORT=3001
NODE_ENV=development
                    """,
                    
                    ".gitignore": """
node_modules/
.env
.DS_Store
client/build/
*.log
coverage/
.nyc_output/
                    """
                }
                
                # Run the complete pipeline
                result = await pipeline.generate_project(
                    prompt="Create a simple web application with user authentication and dashboard",
                    project_name="SimpleWebApp",
                    project_type="fullstack",
                    project_path=str(project_path)
                )
                
                # Verify successful completion
                assert result["success"] is True
                assert result["phases_completed"] >= 4  # All BMAD phases
                
                # Verify project structure was created
                assert project_path.exists()
                assert (project_path / ".aetherforge.json").exists()
                assert (project_path / "README.md").exists()
                assert (project_path / "package.json").exists()
                
                # Verify frontend structure
                assert (project_path / "client").exists()
                assert (project_path / "client" / "src" / "App.tsx").exists()
                assert (project_path / "client" / "src" / "components" / "Login.tsx").exists()
                
                # Verify backend structure
                assert (project_path / "server").exists()
                assert (project_path / "server" / "index.js").exists()
                
                # Verify database structure
                assert (project_path / "database").exists()
                assert (project_path / "database" / "schema.sql").exists()
                
                # Verify configuration files
                assert (project_path / ".env.example").exists()
                assert (project_path / ".gitignore").exists()
                
                # Verify metadata
                metadata = json.loads((project_path / ".aetherforge.json").read_text())
                assert metadata["name"] == "SimpleWebApp"
                assert metadata["type"] == "fullstack"
                assert metadata["status"] == "completed"
                assert "phases" in metadata
                assert len(metadata["phases"]) >= 4
    
    @pytest.mark.asyncio
    async def test_api_only_project_generation(self, temp_workspace):
        """Test generating an API-only project"""
        project_path = Path(temp_workspace) / "task_api"
        
        pipeline = ProjectGenerationPipeline()
        
        with patch('src.agent_executors.call_openai_api') as mock_openai:
            def mock_api_response(*args, **kwargs):
                prompt = args[0] if args else kwargs.get('prompt', '')
                
                if 'requirements' in prompt.lower():
                    return """
                    # Task Management API Requirements
                    
                    ## Endpoints
                    1. GET /api/tasks - List all tasks
                    2. POST /api/tasks - Create new task
                    3. PUT /api/tasks/:id - Update task
                    4. DELETE /api/tasks/:id - Delete task
                    5. POST /api/auth/login - User authentication
                    
                    ## Features
                    1. JWT authentication
                    2. Task CRUD operations
                    3. User management
                    4. Input validation
                    """
                
                elif 'architecture' in prompt.lower():
                    return """
                    # API Architecture
                    
                    ## Technology Stack
                    - Runtime: Node.js
                    - Framework: Express.js
                    - Database: MongoDB
                    - Authentication: JWT
                    - Validation: Joi
                    
                    ## Structure
                    - routes/ (API endpoints)
                    - models/ (Data models)
                    - middleware/ (Auth, validation)
                    - controllers/ (Business logic)
                    """
                
                return "API implementation details"
            
            mock_openai.side_effect = mock_api_response
            
            with patch('src.file_generators.generate_project_files') as mock_files:
                mock_files.return_value = {
                    "package.json": json.dumps({
                        "name": "task-api",
                        "version": "1.0.0",
                        "main": "server.js",
                        "scripts": {
                            "start": "node server.js",
                            "dev": "nodemon server.js"
                        },
                        "dependencies": {
                            "express": "^4.18.0",
                            "mongoose": "^6.3.0",
                            "jsonwebtoken": "^8.5.1",
                            "joi": "^17.6.0",
                            "bcryptjs": "^2.4.3"
                        }
                    }, indent=2),
                    
                    "server.js": """
const express = require('express');
const mongoose = require('mongoose');
const authRoutes = require('./routes/auth');
const taskRoutes = require('./routes/tasks');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/tasks', taskRoutes);

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'healthy' });
});

// Connect to MongoDB and start server
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/taskapi')
    .then(() => {
        app.listen(PORT, () => {
            console.log(`Server running on port ${PORT}`);
        });
    })
    .catch(err => console.error('Database connection error:', err));
                    """,
                    
                    "routes/tasks.js": """
const express = require('express');
const Task = require('../models/Task');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const { taskSchema } = require('../validation/schemas');

const router = express.Router();

// Get all tasks
router.get('/', auth, async (req, res) => {
    try {
        const tasks = await Task.find({ userId: req.user.id });
        res.json(tasks);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Create task
router.post('/', auth, validate(taskSchema), async (req, res) => {
    try {
        const task = new Task({
            ...req.body,
            userId: req.user.id
        });
        await task.save();
        res.status(201).json(task);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

module.exports = router;
                    """,
                    
                    "models/Task.js": """
const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    completed: {
        type: Boolean,
        default: false
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('Task', taskSchema);
                    """
                }
                
                result = await pipeline.generate_project(
                    prompt="Create a REST API for task management with authentication",
                    project_name="TaskAPI",
                    project_type="api",
                    project_path=str(project_path)
                )
                
                # Verify API project structure
                assert result["success"] is True
                assert project_path.exists()
                assert (project_path / "server.js").exists()
                assert (project_path / "routes").exists()
                assert (project_path / "models").exists()
                assert (project_path / "routes" / "tasks.js").exists()
                assert (project_path / "models" / "Task.js").exists()
    
    def test_project_creation_via_api(self, test_client, temp_workspace):
        """Test creating project through REST API"""
        project_data = {
            "prompt": "Create a blog application with user authentication and post management",
            "project_name": "BlogApp",
            "project_type": "fullstack",
            "features": ["authentication", "blog_posts", "comments", "admin_panel"]
        }
        
        with patch('src.orchestrator.PROJECTS_DIR', temp_workspace):
            with patch('src.orchestrator.requests.post') as mock_post:
                # Mock external service responses
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "status": "success",
                    "team": [
                        {"role": "analyst", "id": "analyst_1"},
                        {"role": "architect", "id": "architect_1"},
                        {"role": "developer", "id": "developer_1"},
                        {"role": "qa", "id": "qa_1"}
                    ]
                }
                mock_post.return_value = mock_response
                
                response = test_client.post("/projects", json=project_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["status"] == "success"
                assert "project_id" in data
                assert "project_slug" in data
                
                # Verify project directory was created
                project_slug = data["project_slug"]
                project_path = Path(temp_workspace) / project_slug
                assert project_path.exists()
    
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, temp_workspace):
        """Test error recovery in end-to-end workflow"""
        project_path = Path(temp_workspace) / "error_recovery_test"
        
        pipeline = ProjectGenerationPipeline()
        
        # Simulate failure in developer phase
        with patch('src.agent_executors.call_openai_api') as mock_openai:
            call_count = 0
            
            def failing_then_succeeding(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                
                # Fail on developer calls initially
                prompt = args[0] if args else kwargs.get('prompt', '')
                if 'developer' in prompt.lower() and call_count <= 2:
                    raise Exception("Simulated API failure")
                
                return "Successful response"
            
            mock_openai.side_effect = failing_then_succeeding
            
            with patch('src.file_generators.generate_project_files') as mock_files:
                mock_files.return_value = {"README.md": "# Test Project"}
                
                result = await pipeline.generate_project(
                    prompt="Create a simple application",
                    project_name="ErrorRecoveryTest",
                    project_type="fullstack",
                    project_path=str(project_path)
                )
                
                # Should eventually succeed due to retry mechanism
                # (This depends on the retry implementation in the pipeline)
                if result["success"]:
                    assert project_path.exists()
                else:
                    # Verify error was properly handled
                    assert "error" in result
                    assert result["phases_completed"] < 4


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
