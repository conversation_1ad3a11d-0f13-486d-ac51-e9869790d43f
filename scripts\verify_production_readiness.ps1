# Aetherforge Production Readiness Verification Script
# PowerShell script for Windows environments

param(
    [string]$Environment = "production",
    [switch]$Detailed = $false,
    [switch]$SkipTests = $false
)

# Color functions for output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Warning { param($Message) Write-Host "⚠️ $Message" -ForegroundColor Yellow }
function Write-Info { param($Message) Write-Host "ℹ️ $Message" -ForegroundColor Blue }

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$TestResults = @()
$OverallScore = 0
$MaxScore = 0

Write-Info "Starting Aetherforge Production Readiness Verification..."
Write-Info "Environment: $Environment"
Write-Info "Project Root: $ProjectRoot"

# Test Docker availability
function Test-DockerAvailability {
    Write-Info "Checking Docker availability..."
    $MaxScore += 10
    
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Success "Docker is available: $dockerVersion"
            $script:OverallScore += 10
            return $true
        }
    }
    catch {
        Write-Error "Docker is not available or not in PATH"
        return $false
    }
}

# Test Docker Compose availability
function Test-DockerComposeAvailability {
    Write-Info "Checking Docker Compose availability..."
    $MaxScore += 10
    
    try {
        $composeVersion = docker-compose --version 2>$null
        if ($composeVersion) {
            Write-Success "Docker Compose is available: $composeVersion"
            $script:OverallScore += 10
            return $true
        }
    }
    catch {
        Write-Error "Docker Compose is not available or not in PATH"
        return $false
    }
}

# Test configuration files
function Test-ConfigurationFiles {
    Write-Info "Checking configuration files..."
    $MaxScore += 20
    $configScore = 0
    
    $requiredFiles = @(
        "docker-compose.yml",
        "docker-compose.prod.yml",
        "Dockerfile.orchestrator",
        ".env.example"
    )
    
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $ProjectRoot $file
        if (Test-Path $filePath) {
            Write-Success "Found: $file"
            $configScore += 5
        } else {
            Write-Warning "Missing: $file"
        }
    }
    
    $script:OverallScore += $configScore
    return $configScore -eq 20
}

# Test deployment scripts
function Test-DeploymentScripts {
    Write-Info "Checking deployment scripts..."
    $MaxScore += 20
    $scriptScore = 0
    
    $requiredScripts = @(
        "scripts/deploy_production.sh",
        "scripts/config_manager.sh",
        "scripts/health_check.sh",
        "scripts/backup.sh"
    )
    
    foreach ($script in $requiredScripts) {
        $scriptPath = Join-Path $ProjectRoot $script
        if (Test-Path $scriptPath) {
            Write-Success "Found: $script"
            $scriptScore += 5
        } else {
            Write-Warning "Missing: $script"
        }
    }
    
    $script:OverallScore += $scriptScore
    return $scriptScore -eq 20
}

# Test source code structure
function Test-SourceCodeStructure {
    Write-Info "Checking source code structure..."
    $MaxScore += 15
    $sourceScore = 0
    
    $requiredDirs = @(
        "src",
        "tests",
        "docs"
    )
    
    foreach ($dir in $requiredDirs) {
        $dirPath = Join-Path $ProjectRoot $dir
        if (Test-Path $dirPath -PathType Container) {
            Write-Success "Found directory: $dir"
            $sourceScore += 5
        } else {
            Write-Warning "Missing directory: $dir"
        }
    }
    
    $script:OverallScore += $sourceScore
    return $sourceScore -eq 15
}

# Test Python dependencies
function Test-PythonDependencies {
    Write-Info "Checking Python dependencies..."
    $MaxScore += 10
    
    $requirementsPath = Join-Path $ProjectRoot "requirements.txt"
    if (Test-Path $requirementsPath) {
        Write-Success "Found requirements.txt"
        
        # Check if Python is available
        try {
            $pythonVersion = python --version 2>$null
            if ($pythonVersion) {
                Write-Success "Python is available: $pythonVersion"
                $script:OverallScore += 10
                return $true
            }
        }
        catch {
            Write-Warning "Python is not available in PATH"
            $script:OverallScore += 5
            return $false
        }
    } else {
        Write-Error "requirements.txt not found"
        return $false
    }
}

# Test environment variables
function Test-EnvironmentVariables {
    Write-Info "Checking environment variables..."
    $MaxScore += 15
    $envScore = 0
    
    $requiredEnvVars = @(
        "OPENAI_API_KEY",
        "POSTGRES_PASSWORD"
    )
    
    foreach ($envVar in $requiredEnvVars) {
        $value = [Environment]::GetEnvironmentVariable($envVar)
        if ($value) {
            Write-Success "Environment variable set: $envVar"
            $envScore += 7.5
        } else {
            Write-Warning "Environment variable not set: $envVar"
        }
    }
    
    $script:OverallScore += $envScore
    return $envScore -eq 15
}

# Run integration tests
function Test-IntegrationTests {
    if ($SkipTests) {
        Write-Info "Skipping integration tests (--SkipTests flag)"
        return $true
    }
    
    Write-Info "Running integration tests..."
    $script:MaxScore += 20
    
    try {
        # Check if test files exist
        $testDir = Join-Path $ProjectRoot "tests"
        if (Test-Path $testDir) {
            $testFiles = Get-ChildItem -Path $testDir -Filter "*.py" | Measure-Object
            if ($testFiles.Count -gt 0) {
                Write-Success "Found $($testFiles.Count) test files"
                
                # Try to run a simple test validation
                $testPath = Join-Path $testDir "test_complete_integration.py"
                if (Test-Path $testPath) {
                    Write-Success "Integration test file found"
                    $script:OverallScore += 20
                    return $true
                } else {
                    Write-Warning "Main integration test file not found"
                    $script:OverallScore += 10
                    return $false
                }
            } else {
                Write-Error "No test files found in tests directory"
                return $false
            }
        } else {
            Write-Error "Tests directory not found"
            return $false
        }
    }
    catch {
        Write-Error "Error checking integration tests: $($_.Exception.Message)"
        return $false
    }
}

# Test documentation
function Test-Documentation {
    Write-Info "Checking documentation..."
    $MaxScore += 10
    $docScore = 0
    
    $requiredDocs = @(
        "README.md",
        "docs/user-guides",
        "docs/technical"
    )
    
    foreach ($doc in $requiredDocs) {
        $docPath = Join-Path $ProjectRoot $doc
        if (Test-Path $docPath) {
            Write-Success "Found: $doc"
            $docScore += 3.33
        } else {
            Write-Warning "Missing: $doc"
        }
    }
    
    $script:OverallScore += $docScore
    return $docScore -gt 5
}

# Generate readiness report
function Generate-ReadinessReport {
    $percentage = [math]::Round(($OverallScore / $MaxScore) * 100, 1)
    
    Write-Host "`n" -NoNewline
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Aetherforge Production Readiness Report" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Environment: $Environment" -ForegroundColor White
    Write-Host "Timestamp: $(Get-Date)" -ForegroundColor White
    Write-Host "Host: $env:COMPUTERNAME" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Overall Score: $OverallScore / $MaxScore ($percentage%)" -ForegroundColor $(
        if ($percentage -ge 90) { "Green" }
        elseif ($percentage -ge 70) { "Yellow" }
        else { "Red" }
    )
    
    Write-Host ""
    Write-Host "Component Breakdown:" -ForegroundColor White
    Write-Host "- Docker Availability: $(if (Test-DockerAvailability) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Docker Compose: $(if (Test-DockerComposeAvailability) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Configuration Files: $(if (Test-ConfigurationFiles) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Deployment Scripts: $(if (Test-DeploymentScripts) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Source Code Structure: $(if (Test-SourceCodeStructure) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Python Dependencies: $(if (Test-PythonDependencies) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Environment Variables: $(if (Test-EnvironmentVariables) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Integration Tests: $(if (Test-IntegrationTests) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "- Documentation: $(if (Test-Documentation) { '✅' } else { '❌' })" -ForegroundColor White
    
    Write-Host ""
    if ($percentage -ge 90) {
        Write-Success "🎉 Aetherforge is READY for production deployment!"
    } elseif ($percentage -ge 70) {
        Write-Warning "⚠️ Aetherforge is MOSTLY ready - address warnings before production"
    } else {
        Write-Error "❌ Aetherforge is NOT ready for production - critical issues must be resolved"
    }
    
    Write-Host "========================================" -ForegroundColor Cyan
    
    return $percentage
}

# Show next steps
function Show-NextSteps {
    param($ReadinessPercentage)
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    
    if ($ReadinessPercentage -ge 90) {
        Write-Host "1. Review the production deployment checklist" -ForegroundColor Green
        Write-Host "2. Set up production environment variables" -ForegroundColor Green
        Write-Host "3. Configure SSL certificates" -ForegroundColor Green
        Write-Host "4. Run: ./scripts/deploy_production.sh" -ForegroundColor Green
        Write-Host "5. Monitor deployment with: ./scripts/health_check.sh" -ForegroundColor Green
    } else {
        Write-Host "1. Address the missing components identified above" -ForegroundColor Yellow
        Write-Host "2. Install missing dependencies (Docker, Python, etc.)" -ForegroundColor Yellow
        Write-Host "3. Set up required environment variables" -ForegroundColor Yellow
        Write-Host "4. Re-run this verification script" -ForegroundColor Yellow
        Write-Host "5. Proceed with deployment once score is 90%+" -ForegroundColor Yellow
    }
}

# Main execution
try {
    # Run all tests
    $dockerOk = Test-DockerAvailability
    $composeOk = Test-DockerComposeAvailability
    $configOk = Test-ConfigurationFiles
    $scriptsOk = Test-DeploymentScripts
    $sourceOk = Test-SourceCodeStructure
    $pythonOk = Test-PythonDependencies
    $envOk = Test-EnvironmentVariables
    $testsOk = Test-IntegrationTests
    $docsOk = Test-Documentation
    
    # Generate report
    $readinessPercentage = Generate-ReadinessReport
    
    # Show next steps
    Show-NextSteps -ReadinessPercentage $readinessPercentage
    
    # Exit with appropriate code
    if ($readinessPercentage -ge 90) {
        exit 0
    } elseif ($readinessPercentage -ge 70) {
        exit 1
    } else {
        exit 2
    }
}
catch {
    Write-Error "Error during verification: $($_.Exception.Message)"
    exit 3
}
