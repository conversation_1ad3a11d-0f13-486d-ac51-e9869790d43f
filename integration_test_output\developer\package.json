{"name": "blog-platform-api", "version": "1.0.0", "description": "Generated by Aetherforge - Blog Platform API", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix", "dev": "npm start"}, "dependencies": {"react": "^18.0", "react-dom": "^18.0", "express": "^4.0", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0"}, "devDependencies": {"jest": "^29.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "supertest": "^6.0"}, "keywords": ["aetherforge", "generated", "layered"], "author": "Aetherforge Developer Agent", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}