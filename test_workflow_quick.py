#!/usr/bin/env python3
"""
Quick test to verify workflow engine functionality
"""

import asyncio
import sys

async def test_workflow_engine():
    """Quick test of workflow engine"""
    print("🔧 Quick Workflow Engine Test")
    print("=" * 40)
    
    try:
        # Test imports
        from workflow_engine import (
            WorkflowExecutionEngine, 
            WorkflowEngine, 
            WorkflowYAMLParser,
            WorkflowDefinition,
            WorkflowStep,
            WorkflowStatus,
            StepStatus,
            StepType,
            ConditionOperator,
            AgentSelectionStrategy
        )
        
        print("✅ All imports successful")
        
        # Test YAML parser
        parser = WorkflowYAMLParser()
        workflow_data = {
            "id": "test",
            "name": "Test Workflow",
            "version": "1.0.0",
            "steps": {
                "step1": {
                    "name": "Test Step",
                    "type": "task",
                    "description": "A test step"
                }
            }
        }
        
        workflow = parser.parse_workflow_dict(workflow_data)
        print("✅ YAML parsing successful")
        
        # Test execution engine
        engine = WorkflowExecutionEngine()
        print("✅ Execution engine created")
        
        # Test built-in workflows
        builtin = await engine._get_builtin_workflow("greenfield-fullstack")
        if builtin:
            print("✅ Built-in workflow loaded")
        
        # Test legacy engine
        legacy_engine = WorkflowEngine()
        print("✅ Legacy engine created")
        
        # Test workflow types
        types = legacy_engine.get_workflow_types()
        print(f"✅ Workflow types: {types}")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_workflow_engine())
    sys.exit(0 if success else 1)
