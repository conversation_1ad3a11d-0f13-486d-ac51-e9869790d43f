#!/usr/bin/env python3
"""
Example integration of the Analyst Agent with Aetherforge Orchestrator
Demonstrates how the analyst agent fits into the complete workflow
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from analyst_agent import AnalystAgent, ProjectSpecification
from analyst_config import AnalystIntegration, AnalystConfig

class MockOrchestrator:
    """Mock orchestrator to demonstrate integration"""
    
    def __init__(self):
        self.project_counter = 1
        self.pheromones = []
    
    async def create_project(self, prompt: str, project_type: str = "web_application") -> dict:
        """Create a new project using the analyst agent"""
        
        # Generate project ID and path
        project_id = f"proj_{self.project_counter:04d}"
        project_name = f"project_{self.project_counter}"
        project_path = Path(f"projects/{project_name}")
        self.project_counter += 1
        
        print(f"🚀 Creating project: {project_id}")
        print(f"📁 Project path: {project_path}")
        print(f"💭 Prompt: {prompt}")
        
        # Step 1: Initialize Analyst Agent
        print("\n🤖 Initializing Analyst Agent...")
        analyst = AnalystAgent()
        
        # Step 2: Conduct Analysis
        print("🔍 Conducting comprehensive analysis...")
        specification = await analyst.analyze_prompt(prompt, project_type)
        
        print(f"✅ Analysis complete for: {specification.project_name}")
        print(f"   📋 Requirements: {len(specification.requirements.get('functional', []))} functional")
        print(f"   📖 User Stories: {len(specification.user_stories)} stories")
        print(f"   🛠️ Tech Stack: {specification.technical_stack.get('frontend', {}).get('framework', 'N/A')}")
        
        # Step 3: Generate Documentation
        print("\n📄 Generating project documentation...")
        project_path.mkdir(parents=True, exist_ok=True)
        generated_files = await analyst.generate_project_documents(specification, project_path)
        
        print(f"✅ Generated {len(generated_files)} documentation files")
        
        # Step 4: Create Integration Context
        print("\n🔗 Creating integration context...")
        
        # Context for orchestrator
        orchestrator_context = AnalystIntegration.create_orchestrator_context(
            prompt, project_id, str(project_path)
        )
        
        # Context for architect agent
        architect_context = AnalystIntegration.format_for_architect(specification)
        
        # Context for developer agent
        developer_context = AnalystIntegration.format_for_developer(specification)
        
        # BMAD workflow context
        bmad_context = AnalystIntegration.create_bmad_workflow_context(specification)
        
        # Step 5: Drop Pheromones for Coordination
        print("🐜 Dropping coordination pheromones...")
        pheromone_data = AnalystIntegration.create_pheromone_data(specification)
        self.pheromones.append({
            "project_id": project_id,
            "timestamp": datetime.now().isoformat(),
            "data": pheromone_data
        })
        
        # Step 6: Prepare for Next Phase
        print("⏭️ Preparing for architecture phase...")
        
        return {
            "project_id": project_id,
            "project_path": str(project_path),
            "specification": specification,
            "generated_files": generated_files,
            "contexts": {
                "orchestrator": orchestrator_context,
                "architect": architect_context,
                "developer": developer_context,
                "bmad": bmad_context
            },
            "pheromones": pheromone_data,
            "status": "analysis_complete",
            "next_phase": "architecture_design"
        }
    
    def get_project_status(self, project_id: str) -> dict:
        """Get project status and pheromone trail"""
        project_pheromones = [p for p in self.pheromones if p["project_id"] == project_id]
        
        return {
            "project_id": project_id,
            "pheromone_count": len(project_pheromones),
            "latest_pheromone": project_pheromones[-1] if project_pheromones else None,
            "status": "analysis_complete"
        }

async def demonstrate_full_workflow():
    """Demonstrate the complete analyst agent workflow"""
    
    print("🎯 Aetherforge Analyst Agent Integration Demo")
    print("=" * 60)
    
    # Initialize mock orchestrator
    orchestrator = MockOrchestrator()
    
    # Test cases for different project types
    test_projects = [
        {
            "prompt": "Create a task management web application with real-time collaboration, user authentication, project boards, and team management features",
            "type": "web_application"
        },
        {
            "prompt": "Build a REST API for a social media platform with user profiles, posts, comments, likes, and content moderation",
            "type": "api_service"
        },
        {
            "prompt": "Develop a mobile fitness tracking app with workout logging, progress analytics, social challenges, and wearable device integration",
            "type": "mobile_application"
        }
    ]
    
    results = []
    
    for i, project in enumerate(test_projects, 1):
        print(f"\n{'🔥' * 20} PROJECT {i} {'🔥' * 20}")
        
        try:
            # Create project using analyst agent
            result = await orchestrator.create_project(
                project["prompt"], 
                project["type"]
            )
            
            results.append(result)
            
            # Show key metrics
            spec = result["specification"]
            print(f"\n📊 Project Metrics:")
            print(f"   🎯 Project Name: {spec.project_name}")
            print(f"   📋 Total Requirements: {len(spec.requirements.get('functional', []))}")
            print(f"   📖 User Stories: {len(spec.user_stories)}")
            print(f"   📈 Story Points: {sum(s.get('story_points', 0) for s in spec.user_stories)}")
            print(f"   🎨 Frontend: {spec.technical_stack.get('frontend', {}).get('framework', 'N/A')}")
            print(f"   ⚙️ Backend: {spec.technical_stack.get('backend', {}).get('framework', 'N/A')}")
            print(f"   🗄️ Database: {spec.technical_stack.get('database', {}).get('primary', 'N/A')}")
            
            # Show next steps
            print(f"\n⏭️ Next Steps:")
            print(f"   1. Architect Agent: Design system architecture")
            print(f"   2. Developer Agent: Implement features")
            print(f"   3. QA Agent: Test and validate")
            
            # Show pheromone status
            status = orchestrator.get_project_status(result["project_id"])
            print(f"\n🐜 Coordination Status:")
            print(f"   Project ID: {status['project_id']}")
            print(f"   Pheromone Signals: {status['pheromone_count']}")
            print(f"   Status: {status['status']}")
            
        except Exception as e:
            print(f"❌ Project {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Summary
    print(f"\n{'🎉' * 20} SUMMARY {'🎉' * 20}")
    print(f"✅ Successfully analyzed {len(results)} projects")
    print(f"📄 Generated documentation for all projects")
    print(f"🔗 Created integration contexts for next phases")
    print(f"🐜 Established pheromone coordination trails")
    
    # Show file structure
    print(f"\n📁 Generated Project Structure:")
    projects_dir = Path("projects")
    if projects_dir.exists():
        for project_dir in projects_dir.iterdir():
            if project_dir.is_dir():
                print(f"   📂 {project_dir.name}/")
                docs_dir = project_dir / "docs"
                if docs_dir.exists():
                    for doc_file in docs_dir.iterdir():
                        print(f"      📄 docs/{doc_file.name}")
                readme = project_dir / "README.md"
                if readme.exists():
                    print(f"      📄 README.md")
    
    return results

async def demonstrate_configuration():
    """Demonstrate configuration and customization"""
    
    print("\n🔧 Configuration and Customization Demo")
    print("=" * 50)
    
    # Load configuration
    config = AnalystConfig()
    print(f"📋 Current Configuration:")
    print(f"   MCP URL: {config.mcp_url}")
    print(f"   OpenAI Model: {config.openai_model}")
    print(f"   Max Research Queries: {config.max_research_queries}")
    print(f"   Research Depth: {config.research_depth}")
    
    # Show project templates
    print(f"\n🎨 Available Project Templates:")
    for template_name, template_config in config.project_templates.items():
        print(f"   📋 {template_name}:")
        print(f"      Frontend: {template_config.get('default_stack', {}).get('frontend', 'N/A')}")
        print(f"      Backend: {template_config.get('default_stack', {}).get('backend', 'N/A')}")
        print(f"      Research Focus: {len(template_config.get('research_focus', []))} areas")
    
    # Environment validation
    from analyst_config import get_environment_status
    print(f"\n{get_environment_status()}")

async def main():
    """Main demonstration function"""
    
    command = sys.argv[1] if len(sys.argv) > 1 else "full"
    
    if command == "config":
        await demonstrate_configuration()
    elif command == "full":
        await demonstrate_full_workflow()
        await demonstrate_configuration()
    else:
        print("Usage: python example_analyst_integration.py [full|config]")
        print("  full   - Run complete workflow demonstration (default)")
        print("  config - Show configuration and environment status")

if __name__ == "__main__":
    asyncio.run(main())
