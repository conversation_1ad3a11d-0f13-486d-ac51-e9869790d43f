"""
Comprehensive Integration Tests for TaoForge
Tests complete system integration and component interactions
"""

import pytest
import asyncio
import json
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import time

# Import system components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from orchestrator import app
from pheromone_system import PheromoneSystem
from workflow_engine import WorkflowEngine
from agent_executors import create_agent_executor
from component_adapters_real import ComponentManager
from config_manager import ConfigurationManager
from project_generator_standalone import ProjectGenerationPipeline
from fastapi.testclient import TestClient


class TestSystemIntegration:
    """Test complete system integration"""
    
    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for integration tests"""
        workspace = tempfile.mkdtemp(prefix="taoforge_integration_")
        yield workspace
        shutil.rmtree(workspace, ignore_errors=True)
    
    @pytest.fixture
    def test_client(self):
        """Create test client for API testing"""
        return TestClient(app)
    
    def test_orchestrator_health_check(self, test_client):
        """Test orchestrator health check endpoint"""
        response = test_client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_component_status_endpoint(self, test_client):
        """Test component status endpoint"""
        response = test_client.get("/components/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "components" in data
        assert "timestamp" in data
        
        # Check for expected components
        expected_components = ["pheromone_system", "workflow_engine", "agent_executors"]
        for component in expected_components:
            assert component in data["components"]
    
    @pytest.mark.asyncio
    async def test_pheromone_workflow_integration(self, temp_workspace):
        """Test integration between pheromone system and workflow engine"""
        # Initialize components
        pheromone_file = Path(temp_workspace) / "pheromones.json"
        pheromone_system = PheromoneSystem(storage_file=str(pheromone_file))
        workflow_engine = WorkflowEngine()
        
        # Start workflow and track with pheromones
        project_id = "integration_test_project"
        
        # Drop initial pheromone
        pheromone_id = pheromone_system.drop_pheromone(
            signal="workflow_started",
            payload={"workflow": "test_workflow", "project_id": project_id},
            project_id=project_id,
            agent_id="orchestrator"
        )
        
        assert pheromone_id is not None
        
        # Verify pheromone was stored
        pheromones = pheromone_system.get_pheromones(project_id=project_id)
        assert len(pheromones) == 1
        assert pheromones[0].signal == "workflow_started"
        
        # Simulate workflow progress
        pheromone_system.drop_pheromone(
            signal="phase_completed",
            payload={"phase": "analysis", "progress": 25},
            project_id=project_id,
            agent_id="analyst"
        )
        
        pheromone_system.drop_pheromone(
            signal="phase_completed",
            payload={"phase": "architecture", "progress": 50},
            project_id=project_id,
            agent_id="architect"
        )
        
        # Verify workflow tracking
        all_pheromones = pheromone_system.get_pheromones(project_id=project_id)
        assert len(all_pheromones) == 3
        
        # Check phase completion signals
        phase_signals = pheromone_system.get_pheromones(
            project_id=project_id,
            signal="phase_completed"
        )
        assert len(phase_signals) == 2
    
    @pytest.mark.asyncio
    async def test_agent_coordination_via_pheromones(self, temp_workspace):
        """Test agent coordination through pheromone system"""
        pheromone_file = Path(temp_workspace) / "pheromones.json"
        pheromone_system = PheromoneSystem(storage_file=str(pheromone_file))
        
        project_id = "coordination_test"
        
        # Simulate analyst completing work
        pheromone_system.drop_pheromone(
            signal="requirements_ready",
            payload={
                "outputs": ["requirements.md", "user_stories.md"],
                "next_agent": "architect"
            },
            project_id=project_id,
            agent_id="analyst"
        )
        
        # Architect checks for requirements
        requirements_signals = pheromone_system.get_pheromones(
            project_id=project_id,
            signal="requirements_ready"
        )
        
        assert len(requirements_signals) == 1
        assert requirements_signals[0].payload["next_agent"] == "architect"
        
        # Architect starts work
        pheromone_system.drop_pheromone(
            signal="architecture_started",
            payload={"based_on": requirements_signals[0].id},
            project_id=project_id,
            agent_id="architect"
        )
        
        # Verify coordination chain
        all_signals = pheromone_system.get_pheromones(project_id=project_id)
        assert len(all_signals) == 2
        
        # Check signal relationships
        arch_signal = next(s for s in all_signals if s.signal == "architecture_started")
        assert arch_signal.payload["based_on"] == requirements_signals[0].id
    
    @pytest.mark.asyncio
    async def test_component_manager_integration(self):
        """Test component manager with all adapters"""
        async with ComponentManager() as cm:
            # Test component initialization
            assert cm.archon is not None
            assert cm.pheromind is not None
            assert cm.bmad is not None
            assert cm.mcp is not None
            
            # Test health checks
            health_results = await cm.health_check_all()
            
            for component_name, health in health_results.items():
                assert "status" in health
                assert health["status"] in ["healthy", "unhealthy", "unreachable", "error"]
                assert "timestamp" in health
    
    @pytest.mark.asyncio
    async def test_project_generation_pipeline_integration(self, temp_workspace):
        """Test complete project generation pipeline"""
        project_path = Path(temp_workspace) / "generated_project"
        
        # Initialize pipeline
        pipeline = ProjectGenerationPipeline()
        
        # Mock external dependencies
        with patch('src.agent_executors.call_openai_api') as mock_openai:
            mock_openai.return_value = "Mock AI response"
            
            with patch('src.file_generators.generate_project_files') as mock_files:
                mock_files.return_value = {
                    "package.json": '{"name": "test-app"}',
                    "src/App.js": "console.log('Hello World');",
                    "README.md": "# Test Application"
                }
                
                # Run pipeline
                result = await pipeline.generate_project(
                    prompt="Create a simple web application",
                    project_name="TestApp",
                    project_type="fullstack",
                    project_path=str(project_path)
                )
                
                # Verify results
                assert result["success"] is True
                assert "phases_completed" in result
                assert result["phases_completed"] > 0
                
                # Verify project structure
                assert project_path.exists()
                assert (project_path / ".aetherforge.json").exists()
                
                # Verify metadata
                metadata = json.loads((project_path / ".aetherforge.json").read_text())
                assert metadata["name"] == "TestApp"
                assert metadata["type"] == "fullstack"
    
    def test_api_project_creation_integration(self, test_client, temp_workspace):
        """Test project creation through API"""
        project_data = {
            "prompt": "Create a REST API for task management",
            "project_name": "TaskAPI",
            "project_type": "api"
        }
        
        with patch('src.orchestrator.PROJECTS_DIR', temp_workspace):
            with patch('src.orchestrator.requests.post') as mock_post:
                # Mock external API calls
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "status": "success",
                    "team": [{"role": "analyst"}, {"role": "architect"}]
                }
                mock_post.return_value = mock_response
                
                response = test_client.post("/projects", json=project_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["status"] == "success"
                assert "project_id" in data
                assert "project_slug" in data
    
    @pytest.mark.asyncio
    async def test_error_propagation_and_recovery(self, temp_workspace):
        """Test error handling across system components"""
        pheromone_file = Path(temp_workspace) / "pheromones.json"
        pheromone_system = PheromoneSystem(storage_file=str(pheromone_file))
        
        project_id = "error_test_project"
        
        # Simulate agent error
        pheromone_system.drop_pheromone(
            signal="agent_error",
            payload={
                "agent": "developer",
                "error": "Failed to generate code",
                "retry_count": 1
            },
            project_id=project_id,
            agent_id="developer"
        )
        
        # Simulate error recovery
        pheromone_system.drop_pheromone(
            signal="agent_retry",
            payload={
                "agent": "developer",
                "retry_count": 2,
                "strategy": "fallback_mode"
            },
            project_id=project_id,
            agent_id="orchestrator"
        )
        
        # Verify error tracking
        error_signals = pheromone_system.get_pheromones(
            project_id=project_id,
            signal="agent_error"
        )
        assert len(error_signals) == 1
        
        retry_signals = pheromone_system.get_pheromones(
            project_id=project_id,
            signal="agent_retry"
        )
        assert len(retry_signals) == 1
    
    def test_configuration_integration(self, temp_workspace):
        """Test configuration management across components"""
        config_file = Path(temp_workspace) / "config.json"
        config_manager = ConfigurationManager(str(config_file))
        
        # Test default configuration
        assert config_manager.config.version == "1.0.0"
        assert len(config_manager.workflows) > 0
        assert len(config_manager.agents) > 0
        
        # Test workflow configuration
        workflow = config_manager.get_workflow("greenfield-fullstack")
        assert workflow is not None
        assert workflow.name == "Greenfield Full Stack"
        
        # Test agent configuration
        agent_config = config_manager.get_agent("analyst")
        assert agent_config is not None
        assert agent_config.role == "analyst"
        
        # Test tech stack configuration
        tech_stack = config_manager.get_tech_stack_for_project_type("fullstack")
        assert tech_stack is not None
        assert "fullstack" in tech_stack.project_types
    
    @pytest.mark.asyncio
    async def test_concurrent_project_handling(self, temp_workspace):
        """Test handling multiple concurrent projects"""
        pheromone_file = Path(temp_workspace) / "pheromones.json"
        pheromone_system = PheromoneSystem(storage_file=str(pheromone_file))
        
        # Create multiple projects concurrently
        project_ids = [f"concurrent_project_{i}" for i in range(5)]
        
        # Simulate concurrent project starts
        for project_id in project_ids:
            pheromone_system.drop_pheromone(
                signal="project_started",
                payload={"timestamp": time.time()},
                project_id=project_id,
                agent_id="orchestrator"
            )
        
        # Verify all projects are tracked
        stats = pheromone_system.get_statistics()
        assert stats["active_projects"] == 5
        assert stats["total_pheromones"] == 5
        
        # Simulate project progress
        for i, project_id in enumerate(project_ids):
            pheromone_system.drop_pheromone(
                signal="progress_update",
                payload={"progress": (i + 1) * 20},
                project_id=project_id,
                agent_id="analyst"
            )
        
        # Verify progress tracking
        updated_stats = pheromone_system.get_statistics()
        assert updated_stats["total_pheromones"] == 10  # 5 starts + 5 progress updates
    
    @pytest.mark.asyncio
    async def test_real_time_monitoring_integration(self, temp_workspace):
        """Test real-time monitoring and status updates"""
        pheromone_file = Path(temp_workspace) / "pheromones.json"
        pheromone_system = PheromoneSystem(storage_file=str(pheromone_file))
        
        project_id = "monitoring_test_project"
        
        # Simulate real-time project events
        events = [
            ("project_started", {"phase": "initialization"}),
            ("requirements_analysis_started", {"agent": "analyst"}),
            ("requirements_analysis_completed", {"outputs": ["requirements.md"]}),
            ("architecture_design_started", {"agent": "architect"}),
            ("architecture_design_completed", {"outputs": ["architecture.md"]}),
            ("development_started", {"agent": "developer"}),
            ("code_generation_progress", {"progress": 25}),
            ("code_generation_progress", {"progress": 50}),
            ("code_generation_progress", {"progress": 75}),
            ("development_completed", {"files_created": 15}),
            ("project_completed", {"status": "success"})
        ]
        
        # Drop events with timing
        for signal, payload in events:
            pheromone_system.drop_pheromone(
                signal=signal,
                payload=payload,
                project_id=project_id,
                agent_id=payload.get("agent", "orchestrator")
            )
            await asyncio.sleep(0.01)  # Small delay to simulate real-time
        
        # Verify complete event trail
        all_events = pheromone_system.get_pheromones(project_id=project_id)
        assert len(all_events) == len(events)
        
        # Verify event ordering
        event_signals = [event.signal for event in all_events]
        expected_signals = [event[0] for event in events]
        assert event_signals == expected_signals
        
        # Test progress tracking
        progress_events = pheromone_system.get_pheromones(
            project_id=project_id,
            signal="code_generation_progress"
        )
        assert len(progress_events) == 3
        
        # Verify progress values
        progress_values = [event.payload["progress"] for event in progress_events]
        assert progress_values == [25, 50, 75]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
