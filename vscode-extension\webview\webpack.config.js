const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    entry: {
      projectConfig: './src/panels/ProjectConfigPanel.tsx',
      agentInteraction: './src/panels/AgentInteractionPanel.tsx',
      progressMonitoring: './src/panels/ProgressMonitoringPanel.tsx',
      agentCommunication: './src/agentCommunication.tsx'
    },
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                presets: [
                  '@babel/preset-env',
                  '@babel/preset-react',
                  '@babel/preset-typescript'
                ]
              }
            }
          ],
          exclude: /node_modules/
        },
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader'
          ]
        },
        {
          test: /\.(png|svg|jpg|jpeg|gif)$/i,
          type: 'asset/resource'
        }
      ]
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: './src/templates/projectConfig.html',
        filename: 'projectConfig.html',
        chunks: ['projectConfig']
      }),
      new HtmlWebpackPlugin({
        template: './src/templates/agentInteraction.html',
        filename: 'agentInteraction.html',
        chunks: ['agentInteraction']
      }),
      new HtmlWebpackPlugin({
        template: './src/templates/progressMonitoring.html',
        filename: 'progressMonitoring.html',
        chunks: ['progressMonitoring']
      }),
      new HtmlWebpackPlugin({
        template: './src/templates/agentCommunication.html',
        filename: 'agentCommunication.html',
        chunks: ['agentCommunication']
      }),
      ...(isProduction ? [new MiniCssExtractPlugin({
        filename: '[name].css'
      })] : [])
    ],
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    },
    externals: {
      vscode: 'commonjs vscode'
    }
  };
};
