"""
Comprehensive Research Engine for Aetherforge
Connects with MCP-Crawl4AI-RAG for live documentation crawling and context-aware research
during project generation. Provides intelligent research capabilities for all agents.
"""

import asyncio
import aiohttp
import json
import logging
import os
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from urllib.parse import urlparse, urljoin
import hashlib

logger = logging.getLogger(__name__)

class ResearchEngineError(Exception):
    """Base exception for Research Engine errors"""
    pass

class CrawlError(ResearchEngineError):
    """Raised when crawling operations fail"""
    pass

class SearchError(ResearchEngineError):
    """Raised when search operations fail"""
    pass

class ResearchType(Enum):
    """Types of research operations"""
    DOCUMENTATION = "documentation"
    CODE_EXAMPLES = "code_examples"
    BEST_PRACTICES = "best_practices"
    TUTORIALS = "tutorials"
    API_REFERENCE = "api_reference"
    ARCHITECTURE = "architecture"
    SECURITY = "security"
    PERFORMANCE = "performance"

class ResearchPriority(Enum):
    """Priority levels for research operations"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ResearchQuery:
    """Represents a research query"""
    query: str
    research_type: ResearchType
    priority: ResearchPriority = ResearchPriority.MEDIUM
    sources: List[str] = field(default_factory=list)
    max_results: int = 10
    include_code: bool = True
    depth: str = "comprehensive"
    filters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResearchResult:
    """Represents a research result"""
    query: str
    content: str
    source_url: str
    source_domain: str
    relevance_score: float
    research_type: ResearchType
    metadata: Dict[str, Any] = field(default_factory=dict)
    code_examples: List[str] = field(default_factory=list)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class ResearchContext:
    """Context for research operations"""
    project_type: str
    technology_stack: List[str] = field(default_factory=list)
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    target_audience: str = "developers"
    complexity_level: str = "intermediate"
    research_scope: str = "comprehensive"

@dataclass
class CrawlRequest:
    """Request for crawling operations"""
    url: str
    crawl_type: str = "smart"  # "single", "smart", "recursive"
    max_pages: int = 50
    include_code: bool = True
    follow_links: bool = True
    respect_robots: bool = True

class ResearchEngine:
    """Comprehensive Research Engine with MCP-Crawl4AI-RAG integration"""
    
    def __init__(self, mcp_url: str = None, openai_api_key: str = None):
        self.mcp_url = mcp_url or os.getenv("MCP_URL", "http://localhost:8051")
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Research configuration
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=120)
        
        # Research templates and patterns
        self.research_templates = self._initialize_research_templates()
        self.source_priorities = self._initialize_source_priorities()
        self.query_patterns = self._initialize_query_patterns()
        
        # Cache for research results
        self.result_cache = {}
        self.cache_ttl = 3600  # 1 hour
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _initialize_research_templates(self) -> Dict[str, List[str]]:
        """Initialize research query templates for different project types"""
        return {
            "web_application": [
                "modern web development best practices",
                "responsive web design patterns",
                "web application security guidelines",
                "frontend framework comparison",
                "backend API design principles",
                "web performance optimization",
                "accessibility standards implementation"
            ],
            "mobile_application": [
                "mobile app development best practices",
                "mobile UI/UX design patterns",
                "mobile app security guidelines",
                "cross-platform development frameworks",
                "mobile performance optimization",
                "app store optimization strategies",
                "mobile accessibility guidelines"
            ],
            "api_service": [
                "REST API design best practices",
                "API security implementation",
                "API documentation standards",
                "microservices architecture patterns",
                "API versioning strategies",
                "API rate limiting techniques",
                "GraphQL vs REST comparison"
            ],
            "data_platform": [
                "data pipeline architecture patterns",
                "data warehouse design principles",
                "ETL best practices",
                "data security and privacy",
                "real-time data processing",
                "data visualization techniques",
                "machine learning pipeline design"
            ],
            "desktop_application": [
                "desktop application frameworks",
                "cross-platform desktop development",
                "desktop UI design patterns",
                "application packaging and distribution",
                "desktop security best practices",
                "performance optimization techniques"
            ]
        }
    
    def _initialize_source_priorities(self) -> Dict[str, int]:
        """Initialize source priority rankings"""
        return {
            # Official documentation (highest priority)
            "docs.python.org": 10,
            "developer.mozilla.org": 10,
            "docs.microsoft.com": 10,
            "docs.aws.amazon.com": 10,
            "cloud.google.com": 10,
            "docs.docker.com": 10,
            "kubernetes.io": 10,
            
            # Framework documentation
            "reactjs.org": 9,
            "vuejs.org": 9,
            "angular.io": 9,
            "nodejs.org": 9,
            "expressjs.com": 9,
            "flask.palletsprojects.com": 9,
            "django.readthedocs.io": 9,
            
            # Community resources
            "stackoverflow.com": 8,
            "github.com": 8,
            "medium.com": 7,
            "dev.to": 7,
            
            # General tech sites
            "techcrunch.com": 5,
            "wired.com": 5,
            
            # Default priority
            "default": 6
        }

    def _initialize_query_patterns(self) -> Dict[str, List[str]]:
        """Initialize query patterns for different research types"""
        return {
            ResearchType.DOCUMENTATION.value: [
                "{technology} official documentation",
                "{technology} getting started guide",
                "{technology} API reference",
                "{technology} configuration options"
            ],
            ResearchType.CODE_EXAMPLES.value: [
                "{technology} code examples",
                "{technology} sample projects",
                "{technology} implementation examples",
                "{technology} tutorial code"
            ],
            ResearchType.BEST_PRACTICES.value: [
                "{technology} best practices",
                "{technology} coding standards",
                "{technology} design patterns",
                "{technology} architecture guidelines"
            ],
            ResearchType.TUTORIALS.value: [
                "{technology} tutorial",
                "{technology} step by step guide",
                "{technology} beginner guide",
                "{technology} how to"
            ],
            ResearchType.SECURITY.value: [
                "{technology} security best practices",
                "{technology} security vulnerabilities",
                "{technology} secure coding",
                "{technology} authentication"
            ],
            ResearchType.PERFORMANCE.value: [
                "{technology} performance optimization",
                "{technology} performance tuning",
                "{technology} scalability",
                "{technology} benchmarks"
            ]
        }

    async def conduct_research(self, queries: List[ResearchQuery],
                             context: ResearchContext) -> Dict[str, List[ResearchResult]]:
        """Conduct comprehensive research using multiple queries"""
        logger.info(f"Starting research with {len(queries)} queries")

        try:
            # Check MCP service availability
            if not await self._check_mcp_availability():
                logger.warning("MCP service unavailable, using fallback research")
                return await self._fallback_research(queries, context)

            # Process queries by priority
            sorted_queries = sorted(queries, key=lambda q: self._get_priority_value(q.priority), reverse=True)

            research_results = {}

            for query in sorted_queries:
                try:
                    # Generate expanded queries based on context
                    expanded_queries = self._expand_query(query, context)

                    # Perform research for each expanded query
                    query_results = []
                    for expanded_query in expanded_queries:
                        results = await self._perform_single_research(expanded_query, query, context)
                        query_results.extend(results)

                    # Deduplicate and rank results
                    unique_results = self._deduplicate_results(query_results)
                    ranked_results = self._rank_results(unique_results, query, context)

                    research_results[query.query] = ranked_results[:query.max_results]

                    logger.info(f"Research completed for query: {query.query} ({len(ranked_results)} results)")

                except Exception as e:
                    logger.error(f"Research failed for query '{query.query}': {e}")
                    research_results[query.query] = []

            return research_results

        except Exception as e:
            logger.error(f"Research process failed: {e}")
            raise ResearchEngineError(f"Research failed: {e}")

    async def crawl_and_index(self, requests: List[CrawlRequest]) -> Dict[str, Any]:
        """Crawl URLs and index them in the MCP system"""
        logger.info(f"Starting crawl and index operation for {len(requests)} URLs")

        try:
            crawl_results = {}

            for request in requests:
                try:
                    if request.crawl_type == "single":
                        result = await self._crawl_single_page(request.url)
                    elif request.crawl_type == "smart":
                        result = await self._smart_crawl_url(request.url)
                    else:
                        result = await self._crawl_single_page(request.url)  # Default fallback

                    crawl_results[request.url] = {
                        "success": True,
                        "pages_crawled": result.get("pages_crawled", 1),
                        "content_indexed": result.get("content_indexed", True),
                        "source_id": result.get("source_id", ""),
                        "summary": result.get("summary", "")
                    }

                    logger.info(f"Successfully crawled and indexed: {request.url}")

                except Exception as e:
                    logger.error(f"Failed to crawl {request.url}: {e}")
                    crawl_results[request.url] = {
                        "success": False,
                        "error": str(e)
                    }

            return {
                "total_requests": len(requests),
                "successful_crawls": len([r for r in crawl_results.values() if r.get("success")]),
                "failed_crawls": len([r for r in crawl_results.values() if not r.get("success")]),
                "results": crawl_results
            }

        except Exception as e:
            logger.error(f"Crawl and index operation failed: {e}")
            raise CrawlError(f"Crawl operation failed: {e}")

    async def search_knowledge_base(self, query: str, sources: List[str] = None,
                                  max_results: int = 10, include_code: bool = True) -> List[ResearchResult]:
        """Search the indexed knowledge base"""
        logger.info(f"Searching knowledge base for: {query}")

        try:
            # Perform RAG query
            rag_results = await self._perform_rag_query(query, sources, max_results)

            # Search for code examples if requested
            code_results = []
            if include_code:
                code_results = await self._search_code_examples(query, max_results // 2)

            # Combine and format results
            all_results = []

            # Process RAG results
            for result in rag_results:
                research_result = ResearchResult(
                    query=query,
                    content=result.get("content", ""),
                    source_url=result.get("url", ""),
                    source_domain=self._extract_domain(result.get("url", "")),
                    relevance_score=result.get("similarity", 0.0),
                    research_type=ResearchType.DOCUMENTATION,
                    metadata=result.get("metadata", {})
                )
                all_results.append(research_result)

            # Process code results
            for result in code_results:
                research_result = ResearchResult(
                    query=query,
                    content=result.get("summary", ""),
                    source_url=result.get("url", ""),
                    source_domain=self._extract_domain(result.get("url", "")),
                    relevance_score=result.get("similarity", 0.0),
                    research_type=ResearchType.CODE_EXAMPLES,
                    metadata=result.get("metadata", {}),
                    code_examples=[result.get("code_example", "")]
                )
                all_results.append(research_result)

            # Sort by relevance score
            all_results.sort(key=lambda x: x.relevance_score, reverse=True)

            return all_results[:max_results]

        except Exception as e:
            logger.error(f"Knowledge base search failed: {e}")
            raise SearchError(f"Search failed: {e}")

    async def get_available_sources(self) -> List[str]:
        """Get list of available sources in the knowledge base"""
        try:
            if not self.session:
                raise ResearchEngineError("Session not initialized")

            async with self.session.get(f"{self.mcp_url}/sse") as response:
                if response.status == 200:
                    # This would need to be adapted based on actual MCP API
                    data = await response.json()
                    return data.get("sources", [])
                else:
                    logger.warning(f"Failed to get sources: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Failed to get available sources: {e}")
            return []

    async def _check_mcp_availability(self) -> bool:
        """Check if MCP service is available"""
        try:
            if not self.session:
                return False

            async with self.session.get(f"{self.mcp_url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                return response.status == 200
        except Exception:
            return False

    async def _perform_single_research(self, query: str, original_query: ResearchQuery,
                                     context: ResearchContext) -> List[ResearchResult]:
        """Perform research for a single query"""
        try:
            # Check cache first
            cache_key = self._generate_cache_key(query, original_query.research_type)
            if cache_key in self.result_cache:
                cached_result = self.result_cache[cache_key]
                if self._is_cache_valid(cached_result["timestamp"]):
                    return cached_result["results"]

            # Perform RAG query
            rag_results = await self._perform_rag_query(
                query,
                original_query.sources,
                original_query.max_results
            )

            # Convert to ResearchResult objects
            results = []
            for result in rag_results:
                research_result = ResearchResult(
                    query=query,
                    content=result.get("content", ""),
                    source_url=result.get("url", ""),
                    source_domain=self._extract_domain(result.get("url", "")),
                    relevance_score=result.get("similarity", 0.0),
                    research_type=original_query.research_type,
                    metadata=result.get("metadata", {})
                )
                results.append(research_result)

            # Cache results
            self.result_cache[cache_key] = {
                "results": results,
                "timestamp": datetime.now().isoformat()
            }

            return results

        except Exception as e:
            logger.error(f"Single research failed for query '{query}': {e}")
            return []

    async def _perform_rag_query(self, query: str, sources: List[str] = None,
                               max_results: int = 10) -> List[Dict[str, Any]]:
        """Perform RAG query against MCP service"""
        try:
            if not self.session:
                raise ResearchEngineError("Session not initialized")

            # Prepare request payload
            payload = {
                "query": query,
                "max_results": max_results
            }

            if sources:
                payload["sources"] = sources

            # Make request to MCP service
            async with self.session.post(
                f"{self.mcp_url}/perform_rag_query",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("results", [])
                else:
                    logger.warning(f"RAG query failed: HTTP {response.status}")
                    return []

        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            return []

    async def _search_code_examples(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for code examples"""
        try:
            if not self.session:
                return []

            payload = {
                "query": query,
                "max_results": max_results
            }

            async with self.session.post(
                f"{self.mcp_url}/search_code_examples",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("results", [])
                else:
                    return []

        except Exception as e:
            logger.error(f"Code search failed: {e}")
            return []

    async def _crawl_single_page(self, url: str) -> Dict[str, Any]:
        """Crawl a single page using MCP service"""
        try:
            if not self.session:
                raise CrawlError("Session not initialized")

            payload = {"url": url}

            async with self.session.post(
                f"{self.mcp_url}/crawl_single_page",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "pages_crawled": 1,
                        "content_indexed": True,
                        "source_id": self._extract_domain(url),
                        "summary": data.get("summary", "Page crawled successfully")
                    }
                else:
                    raise CrawlError(f"Crawl failed: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Single page crawl failed: {e}")
            raise CrawlError(f"Failed to crawl {url}: {e}")

    async def _smart_crawl_url(self, url: str) -> Dict[str, Any]:
        """Smart crawl URL using MCP service"""
        try:
            if not self.session:
                raise CrawlError("Session not initialized")

            payload = {"url": url}

            async with self.session.post(
                f"{self.mcp_url}/smart_crawl_url",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "pages_crawled": data.get("pages_crawled", 1),
                        "content_indexed": True,
                        "source_id": self._extract_domain(url),
                        "summary": data.get("summary", "Smart crawl completed successfully")
                    }
                else:
                    raise CrawlError(f"Smart crawl failed: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Smart crawl failed: {e}")
            raise CrawlError(f"Failed to smart crawl {url}: {e}")

    def _expand_query(self, query: ResearchQuery, context: ResearchContext) -> List[str]:
        """Expand a research query based on context"""
        expanded_queries = [query.query]

        # Get query patterns for the research type
        patterns = self.query_patterns.get(query.research_type.value, [])

        # Expand with technology stack
        for tech in context.technology_stack:
            for pattern in patterns:
                expanded_query = pattern.format(technology=tech)
                if expanded_query not in expanded_queries:
                    expanded_queries.append(expanded_query)

        # Add project type specific queries
        project_templates = self.research_templates.get(context.project_type, [])
        for template in project_templates:
            if template not in expanded_queries:
                expanded_queries.append(template)

        return expanded_queries[:10]  # Limit to 10 queries

    def _deduplicate_results(self, results: List[ResearchResult]) -> List[ResearchResult]:
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_urls = set()

        for result in results:
            # Simple deduplication by URL
            if result.source_url not in seen_urls:
                unique_results.append(result)
                seen_urls.add(result.source_url)

        return unique_results

    def _rank_results(self, results: List[ResearchResult], query: ResearchQuery,
                     context: ResearchContext) -> List[ResearchResult]:
        """Rank results based on relevance and source priority"""
        for result in results:
            # Base score from relevance
            score = result.relevance_score

            # Boost based on source priority
            domain_priority = self.source_priorities.get(result.source_domain,
                                                       self.source_priorities["default"])
            score *= (domain_priority / 10.0)

            # Boost based on research type match
            if result.research_type == query.research_type:
                score *= 1.2

            # Boost if contains code examples and requested
            if query.include_code and result.code_examples:
                score *= 1.1

            # Update the score
            result.relevance_score = score

        # Sort by updated relevance score
        return sorted(results, key=lambda x: x.relevance_score, reverse=True)

    def _get_priority_value(self, priority: ResearchPriority) -> int:
        """Convert priority enum to numeric value"""
        priority_values = {
            ResearchPriority.LOW: 1,
            ResearchPriority.MEDIUM: 2,
            ResearchPriority.HIGH: 3,
            ResearchPriority.CRITICAL: 4
        }
        return priority_values.get(priority, 2)

    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            parsed = urlparse(url)
            return parsed.netloc or "unknown"
        except Exception:
            return "unknown"

    def _generate_cache_key(self, query: str, research_type: ResearchType) -> str:
        """Generate cache key for query"""
        key_string = f"{query}_{research_type.value}"
        return hashlib.md5(key_string.encode()).hexdigest()

    def _is_cache_valid(self, timestamp: str) -> bool:
        """Check if cached result is still valid"""
        try:
            cached_time = datetime.fromisoformat(timestamp)
            current_time = datetime.now()
            return (current_time - cached_time).total_seconds() < self.cache_ttl
        except Exception:
            return False

    async def _fallback_research(self, queries: List[ResearchQuery],
                               context: ResearchContext) -> Dict[str, List[ResearchResult]]:
        """Fallback research when MCP service is unavailable"""
        logger.info("Using fallback research mode")

        fallback_results = {}

        for query in queries:
            # Create mock results based on query patterns
            mock_results = []

            # Generate some basic results
            for i, pattern in enumerate(self.query_patterns.get(query.research_type.value, [])[:3]):
                mock_result = ResearchResult(
                    query=query.query,
                    content=f"Fallback content for {pattern}",
                    source_url=f"https://example.com/fallback/{i}",
                    source_domain="example.com",
                    relevance_score=0.5,
                    research_type=query.research_type,
                    metadata={"fallback": True}
                )
                mock_results.append(mock_result)

            fallback_results[query.query] = mock_results

        return fallback_results


# Research Engine Executor for orchestrator integration
class ResearchEngineExecutor:
    """Research Engine executor for orchestrator integration"""

    def __init__(self):
        self.research_engine = None

    async def execute_research(self, project_id: str, research_queries: List[Dict[str, Any]],
                             context_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute research as part of orchestrator workflow"""
        try:
            logger.info(f"Executing research for project {project_id}")

            # Initialize research engine
            async with ResearchEngine() as engine:
                self.research_engine = engine

                # Convert query data to ResearchQuery objects
                queries = []
                for query_data in research_queries:
                    query = ResearchQuery(
                        query=query_data.get("query", ""),
                        research_type=ResearchType(query_data.get("research_type", "documentation")),
                        priority=ResearchPriority(query_data.get("priority", "medium")),
                        sources=query_data.get("sources", []),
                        max_results=query_data.get("max_results", 10),
                        include_code=query_data.get("include_code", True)
                    )
                    queries.append(query)

                # Create research context
                context = ResearchContext(
                    project_type=context_data.get("project_type", "web_application"),
                    technology_stack=context_data.get("technology_stack", []),
                    requirements=context_data.get("requirements", []),
                    constraints=context_data.get("constraints", [])
                )

                # Conduct research
                results = await engine.conduct_research(queries, context)

                # Format results for orchestrator
                formatted_results = {}
                total_results = 0

                for query, query_results in results.items():
                    formatted_results[query] = []
                    for result in query_results:
                        formatted_results[query].append({
                            "content": result.content,
                            "source_url": result.source_url,
                            "source_domain": result.source_domain,
                            "relevance_score": result.relevance_score,
                            "research_type": result.research_type.value,
                            "code_examples": result.code_examples,
                            "metadata": result.metadata
                        })
                    total_results += len(query_results)

                return {
                    "success": True,
                    "project_id": project_id,
                    "total_queries": len(queries),
                    "total_results": total_results,
                    "results": formatted_results,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Research execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }
