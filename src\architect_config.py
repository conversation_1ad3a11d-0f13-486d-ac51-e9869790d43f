"""
Configuration and integration utilities for the Architect Agent
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass
from pathlib import Path
from enum import Enum

from architect_agent import ArchitecturePattern, ScalabilityTier, SystemArchitecture

@dataclass
class ArchitectConfig:
    """Configuration for the Architect Agent"""
    
    # MCP-RAG Configuration
    mcp_url: str = "http://localhost:8051"
    mcp_timeout: int = 60
    mcp_enabled: bool = True
    
    # OpenAI Configuration
    openai_api_key: str = None
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 3000
    
    # Architecture Configuration
    default_architecture_pattern: str = "layered"
    default_scalability_tier: str = "medium"
    enable_microservices_recommendation: bool = True
    
    # Technology Selection Configuration
    prefer_typescript: bool = True
    prefer_open_source: bool = True
    consider_team_expertise: bool = True
    
    # Documentation Configuration
    generate_diagrams: bool = True
    include_code_examples: bool = True
    detailed_justifications: bool = True
    
    def __post_init__(self):
        """Initialize configuration from environment variables"""
        self.mcp_url = os.getenv("MCP_URL", self.mcp_url)
        self.openai_api_key = os.getenv("OPENAI_API_KEY", self.openai_api_key)
        self.openai_model = os.getenv("OPENAI_MODEL", self.openai_model)

class ArchitectIntegration:
    """Integration utilities for the Architect Agent with Aetherforge components"""
    
    @staticmethod
    def create_orchestrator_context(architecture: SystemArchitecture, project_id: str, 
                                  project_path: str) -> Dict[str, Any]:
        """Create context for orchestrator integration"""
        return {
            "project_id": project_id,
            "project_path": project_path,
            "agent_role": "architect",
            "phase": "architecture_design",
            "architecture_pattern": architecture.architecture_pattern.value,
            "scalability_tier": architecture.scalability_tier.value,
            "component_count": len(architecture.components),
            "technology_categories": list(architecture.technology_stack.keys()),
            "timestamp": "2025-01-20T00:00:00Z"
        }
    
    @staticmethod
    def create_pheromone_data(architecture: SystemArchitecture, design_status: str = "complete") -> Dict[str, Any]:
        """Create pheromone data for agent coordination"""
        return {
            "agent_type": "architect",
            "design_status": design_status,
            "project_name": architecture.project_name,
            "architecture_pattern": architecture.architecture_pattern.value,
            "scalability_tier": architecture.scalability_tier.value,
            "components": [comp.name for comp in architecture.components],
            "technology_stack": {
                category: [tech.name for tech in technologies] 
                for category, technologies in architecture.technology_stack.items()
            },
            "next_phase": "development_planning",
            "outputs_ready": True
        }
    
    @staticmethod
    def format_for_developer(architecture: SystemArchitecture) -> Dict[str, Any]:
        """Format architecture data for developer agent"""
        return {
            "project_name": architecture.project_name,
            "architecture_pattern": architecture.architecture_pattern.value,
            "components": [
                {
                    "name": comp.name,
                    "description": comp.description,
                    "responsibilities": comp.responsibilities,
                    "technologies": [
                        {
                            "name": tech.name,
                            "version": tech.version,
                            "category": tech.category
                        } for tech in comp.technologies
                    ],
                    "interfaces": comp.interfaces,
                    "dependencies": comp.dependencies
                } for comp in architecture.components
            ],
            "technology_stack": {
                category: [
                    {
                        "name": tech.name,
                        "version": tech.version,
                        "category": tech.category,
                        "justification": tech.justification
                    } for tech in technologies
                ] for category, technologies in architecture.technology_stack.items()
            },
            "integration_patterns": architecture.integration_patterns,
            "quality_attributes": architecture.quality_attributes,
            "security_requirements": architecture.security_architecture,
            "deployment_requirements": architecture.deployment_architecture
        }
    
    @staticmethod
    def format_for_qa(architecture: SystemArchitecture) -> Dict[str, Any]:
        """Format architecture data for QA agent"""
        return {
            "project_name": architecture.project_name,
            "quality_attributes": architecture.quality_attributes,
            "components": [
                {
                    "name": comp.name,
                    "security_requirements": comp.security_requirements,
                    "performance_requirements": comp.performance_requirements,
                    "scalability_requirements": comp.scalability_requirements
                } for comp in architecture.components
            ],
            "testing_technologies": architecture.technology_stack.get("testing", []),
            "security_architecture": architecture.security_architecture,
            "integration_patterns": architecture.integration_patterns,
            "risks": architecture.risks
        }
    
    @staticmethod
    def create_bmad_workflow_context(architecture: SystemArchitecture) -> Dict[str, Any]:
        """Create context for BMAD workflow integration"""
        return {
            "methodology": "BMAD",
            "phase": "architecture_complete",
            "project_context": {
                "name": architecture.project_name,
                "type": "software_architecture",
                "complexity": architecture.scalability_tier.value,
                "pattern": architecture.architecture_pattern.value
            },
            "deliverables": {
                "system_architecture": "architecture/system_architecture.md",
                "technology_selection": "architecture/technology_selection.md",
                "component_specifications": "architecture/component_specifications.md",
                "deployment_architecture": "architecture/deployment_architecture.md",
                "security_architecture": "architecture/security_architecture.md",
                "data_architecture": "architecture/data_architecture.md",
                "integration_patterns": "architecture/integration_patterns.md",
                "quality_attributes": "architecture/quality_attributes.md",
                "architecture_decisions": "architecture/architecture_decisions.md"
            },
            "next_actions": [
                "Review and approve architecture design",
                "Begin detailed component development",
                "Set up development infrastructure",
                "Create development environment setup"
            ]
        }

class ArchitecturePatternSelector:
    """Utility class for architecture pattern selection"""
    
    @staticmethod
    def recommend_pattern(requirements: Dict[str, Any]) -> ArchitecturePattern:
        """Recommend architecture pattern based on requirements"""
        complexity_indicators = requirements.get("complexity_indicators", {})
        
        # Score different patterns based on requirements
        pattern_scores = {
            ArchitecturePattern.LAYERED: 0,
            ArchitecturePattern.MICROSERVICES: 0,
            ArchitecturePattern.EVENT_DRIVEN: 0,
            ArchitecturePattern.HEXAGONAL: 0
        }
        
        # Scoring logic
        user_story_count = complexity_indicators.get("user_story_count", 0)
        has_real_time = complexity_indicators.get("has_real_time_features", False)
        has_complex_business_logic = complexity_indicators.get("functional_requirement_count", 0) > 10
        
        if user_story_count > 50:
            pattern_scores[ArchitecturePattern.MICROSERVICES] += 3
        elif user_story_count > 20:
            pattern_scores[ArchitecturePattern.HEXAGONAL] += 2
        else:
            pattern_scores[ArchitecturePattern.LAYERED] += 2
        
        if has_real_time:
            pattern_scores[ArchitecturePattern.EVENT_DRIVEN] += 3
            pattern_scores[ArchitecturePattern.MICROSERVICES] += 1
        
        if has_complex_business_logic:
            pattern_scores[ArchitecturePattern.HEXAGONAL] += 2
            pattern_scores[ArchitecturePattern.MICROSERVICES] += 1
        
        # Return pattern with highest score
        return max(pattern_scores, key=pattern_scores.get)
    
    @staticmethod
    def recommend_scalability_tier(requirements: Dict[str, Any]) -> ScalabilityTier:
        """Recommend scalability tier based on requirements"""
        success_metrics = requirements.get("success_metrics", [])
        description = requirements.get("description", "").lower()
        
        # Look for scalability indicators
        for metric in success_metrics:
            metric_str = str(metric).lower()
            if "million" in metric_str or "1000000" in metric_str:
                return ScalabilityTier.ENTERPRISE
            elif "hundred thousand" in metric_str or "100000" in metric_str:
                return ScalabilityTier.LARGE
            elif "thousand" in metric_str or "1000" in metric_str:
                return ScalabilityTier.MEDIUM
        
        # Check description for scale indicators
        if any(term in description for term in ["enterprise", "large scale", "millions"]):
            return ScalabilityTier.ENTERPRISE
        elif any(term in description for term in ["startup", "mvp", "prototype"]):
            return ScalabilityTier.SMALL
        else:
            return ScalabilityTier.MEDIUM

class TechnologySelector:
    """Utility class for technology selection"""
    
    @staticmethod
    def score_technology(tech_name: str, criteria: Dict[str, Any]) -> float:
        """Score a technology based on selection criteria"""
        # Technology scoring matrix (simplified)
        tech_scores = {
            "React": {"performance": 0.9, "maintainability": 0.9, "community": 0.95},
            "Vue.js": {"performance": 0.85, "maintainability": 0.9, "community": 0.8},
            "Angular": {"performance": 0.8, "maintainability": 0.85, "community": 0.85},
            "Node.js": {"performance": 0.85, "maintainability": 0.8, "community": 0.95},
            "Express.js": {"performance": 0.8, "maintainability": 0.85, "community": 0.9},
            "NestJS": {"performance": 0.85, "maintainability": 0.9, "community": 0.75},
            "PostgreSQL": {"performance": 0.9, "maintainability": 0.85, "community": 0.9},
            "MongoDB": {"performance": 0.85, "maintainability": 0.8, "community": 0.85},
            "Redis": {"performance": 0.95, "maintainability": 0.8, "community": 0.9}
        }
        
        if tech_name not in tech_scores:
            return 0.7  # Default score for unknown technologies
        
        scores = tech_scores[tech_name]
        weights = criteria.get("weights", {"performance": 0.4, "maintainability": 0.3, "community": 0.3})
        
        total_score = sum(scores.get(criterion, 0.5) * weight for criterion, weight in weights.items())
        return total_score
    
    @staticmethod
    def recommend_technologies(category: str, requirements: Dict[str, Any]) -> List[str]:
        """Recommend technologies for a specific category"""
        recommendations = {
            "frontend": {
                "simple": ["React", "Vue.js"],
                "complex": ["React", "Angular"],
                "enterprise": ["React", "Angular", "Vue.js"]
            },
            "backend": {
                "simple": ["Express.js", "Fastify"],
                "complex": ["NestJS", "Express.js"],
                "enterprise": ["NestJS", "Spring Boot"]
            },
            "database": {
                "simple": ["PostgreSQL", "SQLite"],
                "complex": ["PostgreSQL", "MongoDB"],
                "enterprise": ["PostgreSQL", "MongoDB", "Cassandra"]
            }
        }
        
        complexity = requirements.get("complexity_level", "simple")
        return recommendations.get(category, {}).get(complexity, [])

def load_config_from_file(config_path: str = None) -> ArchitectConfig:
    """Load configuration from file"""
    if config_path is None:
        config_path = os.getenv("ARCHITECT_CONFIG_PATH", "architect_config.json")
    
    config_file = Path(config_path)
    if config_file.exists():
        import json
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        return ArchitectConfig(**config_data)
    else:
        return ArchitectConfig()

def save_config_to_file(config: ArchitectConfig, config_path: str = None):
    """Save configuration to file"""
    if config_path is None:
        config_path = "architect_config.json"
    
    import json
    from dataclasses import asdict
    
    config_data = asdict(config)
    
    with open(config_path, 'w') as f:
        json.dump(config_data, f, indent=2)

def validate_environment() -> Dict[str, bool]:
    """Validate environment setup for architect agent"""
    checks = {
        "openai_api_key": bool(os.getenv("OPENAI_API_KEY")),
        "mcp_url_configured": bool(os.getenv("MCP_URL")),
        "python_version": True,
        "required_packages": True
    }
    
    return checks

def get_environment_status() -> str:
    """Get human-readable environment status"""
    checks = validate_environment()
    
    status_lines = []
    status_lines.append("🏗️ Architect Agent Environment Status:")
    
    for check, passed in checks.items():
        icon = "✅" if passed else "❌"
        status_lines.append(f"   {icon} {check.replace('_', ' ').title()}")
    
    if all(checks.values()):
        status_lines.append("\n🎉 Environment is ready for architect agent!")
    else:
        status_lines.append("\n⚠️  Some environment checks failed. Please review configuration.")
    
    return "\n".join(status_lines)

# Example usage and testing
if __name__ == "__main__":
    print(get_environment_status())
    
    # Create and save default config
    config = ArchitectConfig()
    save_config_to_file(config, "example_architect_config.json")
    print("\n📄 Example configuration saved to example_architect_config.json")
    
    # Test pattern recommendation
    sample_requirements = {
        "complexity_indicators": {
            "user_story_count": 25,
            "has_real_time_features": True,
            "functional_requirement_count": 15
        },
        "success_metrics": ["Support 10,000+ concurrent users"],
        "description": "A real-time collaboration platform"
    }
    
    recommended_pattern = ArchitecturePatternSelector.recommend_pattern(sample_requirements)
    recommended_tier = ArchitecturePatternSelector.recommend_scalability_tier(sample_requirements)
    
    print(f"\n🎯 Pattern Recommendation: {recommended_pattern.value}")
    print(f"📈 Scalability Tier: {recommended_tier.value}")
    
    # Test technology recommendations
    frontend_techs = TechnologySelector.recommend_technologies("frontend", {"complexity_level": "complex"})
    print(f"🛠️ Frontend Technologies: {', '.join(frontend_techs)}")
